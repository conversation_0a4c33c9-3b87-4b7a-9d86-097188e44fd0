import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

import '../constants/api_constant.dart';
import '../constants/api_headers.dart';
import '../helpers/flavor_config.dart';
import '../utils/pref_utils.dart';


class GraphQLConfig {
  static Map<String, String> headers = {};
  static HttpLink? httpLink;

  static ValueNotifier<GraphQLClient> initializeClient() {
    debugPrint(':: auth ::');
    headers = {ApiHeadersConstant.AUTHORIZATION: ApiConstant.BEARER  + PrefsUtils.getString(PrefKeys.authToken)!,};
    httpLink = HttpLink(ApiConfig.graphqlUrl,
        defaultHeaders: headers);
    debugPrint(':: auth $headers::');
    final Link link = httpLink!;
    print('$link');
    ValueNotifier<GraphQLClient> client = ValueNotifier(
      GraphQLClient(cache: GraphQLCache(store: InMemoryStore()), link: link),
    );
    return client;
  }
}