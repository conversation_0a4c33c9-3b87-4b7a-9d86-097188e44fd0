class GraphQLQuery {

  static String getMenus = '''query getMenus(
   \$businessId: String!
   \$status: Boolean! = true 
   \$locationId: String !
   \$timezone: String = "America/New_York"
   \$date:DateTime!
) {
  menusData: menus( findAllMenuInput: {
            businessId: \$businessId
            status: \$status
            date: \$date
        }) {
           payload{
               id
               name
               desc
               order
               status
               menuImage: image
               menuCategories: categories( getCategoriesInput :{
                filterByDate :{
                      timezone: \$timezone
                }
                excludeEmptyCategories :{
                  locationId: \$locationId
                  excludeItemStatus : false
                }
               }){
                id
                categoryName: name
                categoryStatus: status
                course
                menuId
                items(getItemsInput : {locationId: \$locationId
                status : true
                }) {
                    itemId: id
                    name
                    categoryId
                    course
                     itemPriceInventory{
                        id
                        locationId
                        price
                        cost
                        currentInventory
                    }
                    status
                    shortDescription
                    itemOrder: order
                    itemSku: sku
                    imageUrl: image
                    itemTags: tags{
                          businessId
                          id
                          name
                          image
                     }
                  itemModifiers : modifiers{
                     modifierId: id
                     name
                     basePrice
                     min
                     max
                     included
                     image
                     sku
                     status
                     order
                     modifierItems: modifierItems{
                        modifierItemId: id
                        name
                        price
                        min
                        max
                        order
                        sku
                        status
                        modifierId
                     }
                  }   
                }
               }
           }

        }
} ''';

  static String getTags = ''' query fetchTags(){
  tags: tags{
     payload {
        id
        name
        image
    }
  }
}''';

  static String searchItem ='''query searchItem(
     \$businessId: String!
     \$locationId: String!
     \$searchKeyword:String
     \$tags:[Int!]
     \$itemIds:[Int!]
){
    itemSearch(
        itemSearchInput: {
            businessId: \$businessId
            locationId: \$locationId
            search: \$searchKeyword
            excludeTags: \$tags
            itemIds: \$itemIds
            status : true 
        }
    ){
    message
    payload {
        category{
            id
            categoryName: name
            categoryStatus: status
            course
            menuId
        }
        itemId: id
        name
        categoryId
        itemPriceInventory{
                id
                locationId
                price
                cost
                currentInventory
                enableInventoryCountdown
             }
        status
        description
        itemOrder: order
        itemSku: sku
        imageUrl: image   
        itemTags: tags{
            businessId
            id
            name
            image
        }
        itemPriceInventory{
            id
            locationId
            price
            currentInventory
            enableInventoryCountdown
        }
        itemModifiers : modifiers{
                     modifierId: id
                     name
                     basePrice
                     min
                     max
                     included
                     image
                     sku
                     status
                     order
                     modifierItems: modifierItems{
                        modifierItemId: id
                        name
                        price
                        min
                        max
                        order
                        sku
                        status
                        modifierId
                     }
                }   
    }
  }
}''';

  static String queryPresetDiscounts='''query getPresetDiscounts(
      \$locationId: String!
      \$itemIds: [Int!]
    ){
    presetDiscounts: discounts(
        findAllDiscountsInput: { 
            locationId: \$locationId,
            itemIds : \$itemIds,
            perPage : 50,
        }
    ) {
        message
        payload {
            totalRecords
            currentPage
            totalPages
            perPage
            discounts:data {
                id
                businessId
                description
                title
                userType
                sort
                startDate
                endDate
                discountType
                applyDiscountOn
                discount
                maxDiscountAmount
                minAmount
                maxUse
                maxUsePerPerson
                status
            }
        }
    }
  }
   ''';
}