import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_color.dart';
import 'package:gr8tables_server_manager/constants/app_dimens.dart';
import 'package:gr8tables_server_manager/custom_widgets/app_text_style.dart';
import 'package:gr8tables_server_manager/models/discount/promotion_apply_on.dart';
import 'package:gr8tables_server_manager/providers/discount/discount_provider.dart';
import 'package:gr8tables_server_manager/providers/notification/notification_provider.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class NotifCartSummaryView extends StatefulWidget {
  const NotifCartSummaryView({Key? key}) : super(key: key);

  @override
  State<NotifCartSummaryView> createState() => _NotifCartSummaryViewState();
}

class _NotifCartSummaryViewState extends State<NotifCartSummaryView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      padding: EdgeInsets.only(bottom: 70),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Cart Summary", // Replace with CartCustomDiscountString.cartSummary if defined
            style: AppTextStyle.mediumTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
              letterSpacing: 1.2,
              fontWeight: FontWeight.w700,
              color: ColorConstant.notifTextColor,
            ),
          ),
          HeightDimens().getHeight8(),
          Column(
            children: [
              if (context.watch<DiscountProvider>().cartData != null) ...[
                _buildSubTotalRow(),
                HeightDimens().getHeight8(),
                ..._buildCustomDiscountRows(),
                HeightDimens().getHeight8(),
                if (context.read<DiscountProvider>().cartData?.cartDiscount != null)
                  _buildPresetDiscountRow(),
                ..._buildNewSubTotalSection(),
                _buildTotalRow(),
                HeightDimens().getHeight8(),
                if (context.read<DiscountProvider>().totalSavings != 0)
                  _buildTotalSavingsRow(),
                HeightDimens().getHeight15(),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubTotalRow() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: _cartHeaderTextView("Sub Total"), // Replace with CartCustomDiscountString.subTotal if defined
        ),
        _cartHeaderTextView(
          '${NumberFormat.simpleCurrency().format(
            context.read<DiscountProvider>().subTotal < 0
                ? 0
                : context.read<DiscountProvider>().subTotal,
          )}',
        ),
      ],
    );
  }

  List<Widget> _buildCustomDiscountRows() {
    return context.read<DiscountProvider>().cartData!.cartCustomDiscount!
        .map((customDiscount) => Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _promotionalView(
          type: "Custom", // Replace with CartCustomDiscountString.custom if defined
          onCart: customDiscount.applyOn!.toLowerCase() ==
              PromotionApplyOn.cart.name,
          label: "Discount", // Replace with CartCustomDiscountString.discount if defined
          value: customDiscount.appliedValue,
          approved: customDiscount.approved ?? true,
          onRemove: () {
            // Placeholder for removal logic; implement as needed
          },
        ),
      ],
    ))
        .toList();
  }

  Widget _buildPresetDiscountRow() {
    return _promotionalView(
      type: "Preset", // Replace with CartCustomDiscountString.preset if defined
      onCart: context.read<DiscountProvider>().cartData?.cartDiscount?.applyOn?.toLowerCase() == PromotionApplyOn.cart.name,
      label: context.read<DiscountProvider>().cartData?.cartDiscount!.title!,
      value: context.read<DiscountProvider>().cartData?.cartDiscount!.appliedValue,
      approved: context.read<DiscountProvider>().cartData?.cartDiscount!.approved ?? true,
      onRemove: () {
        // Placeholder for removal logic; implement as needed
      },
    );
  }

  List<Widget> _buildNewSubTotalSection() {
    if (context.read<DiscountProvider>().newSubTotal == 0.0 &&
        context.read<DiscountProvider>().customCartDiscount == 0) {
      return [];
    }

    return [
      Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              "New Sub Total", // Replace with CartCustomDiscountString.newSubTotal if defined
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            '${NumberFormat.simpleCurrency().format(
              context.read<DiscountProvider>().newSubTotal,
            )}',
            style: AppTextStyle.largeTextStyle.copyWith(
              color: ColorConstant.colorBlueDark,
              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
            ),
          ),
        ],
      ),
      HeightDimens().getHeight8(),
      Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              "Tax", // Replace with CartViewString.tax if defined
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            '${NumberFormat.simpleCurrency().format(
              context.read<DiscountProvider>().cartData!.tax,
            )}',
            style: AppTextStyle.largeTextStyle.copyWith(
              color: ColorConstant.colorBlueDark,
              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
            ),
          ),
        ],
      ),
      HeightDimens().getHeight8(),
    ];
  }

  Widget _buildTotalRow() {
    if (context.read<DiscountProvider>().cartTotal == 0) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
          child: Text(
            "Total", // Replace with CartViewString.total if defined
            style: AppTextStyle.mediumTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 18.0 : 16.0,
              color: ColorConstant.notifTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Text(
          '${NumberFormat.simpleCurrency().format(
            context.read<DiscountProvider>().cartTotal,
          )}',
          style: AppTextStyle.mediumTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 18.0 : 16.0,
            color: ColorConstant.notifTextColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTotalSavingsRow() {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: _cartHeaderTextView("Total Savings"), // Replace with CartCustomDiscountString.totalSavings if defined
            ),
            _cartHeaderTextView(
              '${NumberFormat.simpleCurrency().format(
                context.read<DiscountProvider>().totalSavings,
              )}',
            ),
          ],
        ),
        _height15(),
      ],
    );
  }

  Widget _cartHeaderTextView(String label) {
    return Text(
      label,
      style: AppTextStyle.smallTextStyle.copyWith(
        fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
        color: ColorConstant.colorBlueDark,
      ),
    );
  }

  Widget _height15() {
    return SizedBox(
      height: isBigScreenResolution(context) ? 15 : 10,
    );
  }

  Widget _promotionalView({
    String type = '',
    bool onCart = false,
    String? label = "Custom", // Replace with CartCustomDiscountString.custom if defined
    num? value = 0,
    bool approved = true,
    Function()? onRemove,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 5, right: 5),
      padding: const EdgeInsets.all(10.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: 16,
            color: ColorConstant.colorBlueDark,
          ),
          const SizedBox(width: 5),
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                  ),
                ),
                Flexible(
                  child: Text(
                    ' $label '.replaceAll("", "\u{200B}"),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.smallTextStyle.copyWith(
                      fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                      color: ColorConstant.colorBlueDark,
                    ),
                  ),
                ),
                Text(
                  '(-${NumberFormat.simpleCurrency().format(value ?? 0)})',
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                  ),
                ),
                const SizedBox(width: 5),
              ],
            ),
          ),
          if (context.watch<NotificationProvider>().showApproveActionButton) ...[
            if (!approved)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: ColorConstant.notifPendingBackground,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  "Pending",
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.notifPendingTextColor,
                    fontSize: 12,
                  ),
                ),
              )
            else
              GestureDetector(
                onTap: onRemove,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: ColorConstant.notifIconColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    size: isBigScreenResolution(context) ? 16.0 : 12.0,
                    color: ColorConstant.white,
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  bool isBigScreenResolution(BuildContext context) {
    return MediaQuery.of(context).size.width > 600;
  }
}