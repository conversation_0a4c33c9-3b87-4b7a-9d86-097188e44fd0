import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_string.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'dart:io' show Platform;

import '../../constants/api_constant.dart';
import '../../constants/app_color.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../models/cart/cart_items/cart_item_modifiers.dart';
import '../../models/cart/cart_items/cart_items.dart';
import '../../models/cart/cart_users/cart_user_data.dart';
import '../../models/notification/all_notification_response.dart';
import '../../providers/discount/discount_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import 'notif_cart_summary.dart';
import 'notif_discount_view.dart';

class NotifCartDiscountSentItemView extends StatefulWidget {
  final int? cartId;
  final UserNotificationData? userNotificationData;

  const NotifCartDiscountSentItemView({
    Key? key,
    this.cartId, this.userNotificationData,
  }) : super(key: key);

  @override
  State<NotifCartDiscountSentItemView> createState() =>
      _NotifCartDiscountSentItemViewState();
}

class _NotifCartDiscountSentItemViewState extends State<NotifCartDiscountSentItemView>
        with WidgetsBindingObserver {

  /// Determine whether the keyboard is hidden.
  Future<bool> get keyboardHidden async {
    final check = () =>
    (MediaQuery.viewInsetsOf(context).bottom) <= 0;
    if (!check()) return false;
    return await Future.delayed(
      Duration(milliseconds: 100),
          () => check(),
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Load cart data if cartId is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setCartData();
    });
  }


  @override
  void didChangeMetrics() async{
    final value = MediaQuery.viewInsetsOf(context).bottom;
    debugPrint('dichangedepenncy $value');
    if (value == 0) {
      // Handle keyboard dismissal
    }
    if (Platform.isAndroid) {
      debugPrint('dichangedepenncy $value  22 ${await keyboardHidden}');
      keyboardHidden.then((v) {
        debugPrint('dichangedepenncy $v  23 ${v}');
        if(v){
          //FocusManager.instance.primaryFocus?.unfocus();
          FocusScope.of(context).unfocus();
        }
      });
      //value ? FocusManager.instance.primaryFocus?.unfocus() : null);
    }
    super.didChangeMetrics();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CartViewProvider>(
      builder: (context, data, child) {
        return SingleChildScrollView(
          padding: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
          child: Container(
            margin: const EdgeInsets.only(
              left: 15,
              right: 15,
              top: 10,
              bottom: 10,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                _submittedOrderView(data),
                Container(
                  width: double.infinity,
                  height: 1,
                  color: ColorConstant.colorBlueLight_16,
                ),
                if (context.watch<NotificationProvider>().showApproveActionButton &&
                    context.watch<CartViewProvider>().getSubmittedItemsCount() > 0)
                  NotifDiscountView(
                    cartId: widget.cartId,
                    userNotificationData: widget.userNotificationData,
                  ),
                if (context.watch<CartViewProvider>().getSubmittedItemsCount() > 0)
                  NotifCartSummaryView(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget get _verticalSpacing7 => SizedBox(
    height: isBigScreenResolution(context) ? 7 : 6,
  );

  /// Submitted order view
  Widget _submittedOrderView(CartViewProvider data) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (data.submittedCartItemMap.isEmpty) ...[
                _verticalSpacing7,
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        CartViewString.billIsEmpty,
                        textAlign: TextAlign.center,
                        style: AppTextStyle.smallTextStyle.copyWith(
                          color: ColorConstant.colorBlueLight_50,
                          fontSize: isBigScreenResolution(context) ? 14 : 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              ...data.submittedCartItemMap.entries.map(
                    (e) => _userWiseCartView(e),
              ),
            ],
          ),
          cutImage(
            7.0,
            MediaQuery.of(context).size.width,
            AssetIcons.bottomCut,
          ),
        ],
      ),
    );
  }


  // Common label view
  Widget _commonLabelView(
      String label, {
        TextAlign textAlign = TextAlign.center,
        bool isBold = true,
      }) {
    return AutoSizeText(
      label,
      textAlign: textAlign,
      maxLines: 2,
      style: AppTextStyle.smallTextStyle.copyWith(
        color: ColorConstant.bluePriceFontColor,
        fontSize: 12,
        fontWeight: isBold ? FontWeight.bold : FontWeight.w400,
      ),
      minFontSize: isBigScreenResolution(context) ? 13 : 11,
      maxFontSize: isBigScreenResolution(context) ? 14 : 12,
    );
  }

  /// Item row view
  Widget _itemRowView(CartItems item, {bool hideModifiers = false}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 30,
              height: 25,
              decoration: BoxDecoration(
                color: ColorConstant.customerBackground,
                border: Border.all(color: ColorConstant.customerBackground),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: Text(
                  '${item.itemQty}',
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.notifTextColor,
                    fontSize: 12.0,
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                item.name!,
                style: AppTextStyle.mediumTextStyle.copyWith(
                  color: ColorConstant.notifTextColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
            Expanded(
              child: _commonLabelView(
                NumberFormat.simpleCurrency().format(item.lineTotal!),
                textAlign: TextAlign.end,
                isBold: false,
              ),
            ),
          ],
        ),
        if (!hideModifiers) ...[
          if (item.cartItemModifiers!.isNotEmpty) ...[
            SizedBox(
              height: isBigScreenResolution(context) ? 3 : 2,
            ),
            ...item.cartItemModifiers!.map(
                  (e) => _itemModifiersView(e),
            ),
          ],
        ],
        if (item.note!.isNotEmpty) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: _noteLabelView(
                  '${CartViewString.note} ${item.note!}',
                  textAlign: TextAlign.start,
                  isBold: false,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _approvedItemRowView(CartItems item, {bool hideModifiers = false}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 30,
              height: 25,
              decoration: BoxDecoration(
                color: ColorConstant.customerBackground,
                border: Border.all(color: ColorConstant.customerBackground),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: Text(
                  '${item.itemQty}',
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.notifTextColor,
                    fontSize: 12.0,
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                item.name!,
                style: AppTextStyle.mediumTextStyle.copyWith(
                  color: ColorConstant.notifTextColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 42),
            Expanded(
              child: _commonLabelView(
                NumberFormat.simpleCurrency().format(item.lineTotal!),
                textAlign: TextAlign.start,
                isBold: false,
              ),
            ),
          ],
        ),
        if (item.category!.isNotEmpty) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(width: 42),
              Expanded(
                child: Text(
                  item.category!,
                  textAlign: TextAlign.start,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.warningOrangeFontColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
        if (!item.note!.isNotEmpty && item.note!.length > 0) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(width: 42),
              Expanded(
                child: Text(
                  item.note!,
                  textAlign: TextAlign.start,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.warningOrangeFontColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Item modifiers View
  Widget _itemModifiersView(CartItemModifiers itemModifiers) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: isBigScreenResolution(context) ? 6 : 4,
            ),
            Expanded(
              child: Text(
                itemModifiers.name!,
                style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 11.0 : 10.0,
                  color: ColorConstant.colorBlueDark,
                ),
              ),
            ),
          ],
        ),
        if (itemModifiers.cartItemModifierItems!.isNotEmpty) ...[
          ...itemModifiers.cartItemModifierItems!.map(
                (e) => _modifierItemsView(e),
          ),
        ],
        SizedBox(
          height: isBigScreenResolution(context) ? 3 : 2,
        ),
      ],
    );
  }

  /// Modifier items view
  Widget _modifierItemsView(CartItemModifierItems modifierItems) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: isBigScreenResolution(context) ? 4 : 3,
        ),
        Expanded(
          child: Text(
            '  ${modifierItems.qty} x ${modifierItems.name!}',
            style: AppTextStyle.smallTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 10.0 : 9.0,
              color: ColorConstant.colorBlueDark,
            ),
          ),
        ),
      ],
    );
  }

  // Note label view
  Widget _noteLabelView(
      String label, {
        TextAlign textAlign = TextAlign.center,
        bool isBold = true,
      }) {
    return AutoSizeText(
      label,
      textAlign: textAlign,
      maxLines: 2,
      style: AppTextStyle.smallTextStyle.copyWith(
        color: ColorConstant.colorBlueDark,
        fontWeight: isBold ? FontWeight.bold : FontWeight.w400,
      ),
      minFontSize: isBigScreenResolution(context) ? 11 : 10,
      maxFontSize: isBigScreenResolution(context) ? 12 : 11,
    );
  }

  /// User wise cart view
  Widget _userWiseCartView(
      MapEntry<CartUserData, List<CartItems>> e, {
        bool hideModifiers = true,
      }) {
    bool isYou = e.key.name == 'You';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 16.0,
          ),
          color: ColorConstant.customerBackground,
          child: Row(
            children: [
              const Icon(
                CupertinoIcons.person_fill,
                size: 18,
                color: ColorConstant.notifIconColor,
              ),
              const SizedBox(width: 8),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: e.key.userId!.isNotEmpty
                    ? () {
                  navigateToCustomerDetailsScreen(
                    context,
                    e.key.userId!,
                    e.key.name!,
                  );
                }
                    : null,
                child: Row(
                  children: [
                    Text(
                      isYou ? '${e.key.name!} (You)' : e.key.name!,
                      style: AppTextStyle.largeTextStyle.copyWith(
                        color: ColorConstant.notifTextColor,
                        fontSize: 14.0,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Text(
                '${e.value.length} items',
                style: AppTextStyle.smallTextStyle.copyWith(
                  fontWeight: FontWeight.w400,
                  color: ColorConstant.notifItemColor,
                  fontSize: 12.0,
                ),
              ),
            ],
          ),
        ),
        e.value.isEmpty 
            ? Container(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Center(
                  child: Text(
                    NotificationsString.noItems,
                    style: AppTextStyle.mediumTextStyle.copyWith(
                      color: ColorConstant.notifTextColor,
                      fontSize: 14.0,
                    ),
                  ),
                ),
              )
            : ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: e.value.length,
                itemBuilder: (context, index) {
                  final item = e.value[index];
                  return Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isBigScreenResolution(context) ? 16.0 : 12.0,
                      vertical: isBigScreenResolution(context) ? 12.0 : 10.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: index < e.value.length - 1
                          ? Border(
                              bottom: BorderSide(
                                color: ColorConstant.customerBackground,
                                width: 0.5,
                              ),
                            )
                          : null,
                    ),
                    child: context.watch<NotificationProvider>().showApproveActionButton?
                        _itemRowView(item, hideModifiers: hideModifiers):
                        _approvedItemRowView(item, hideModifiers: hideModifiers),
                  );
                },
              ),
      ],
    );
  }

  Future<void> _setCartData() async {
      try {
        // Set order after successfully loading cart data
        await context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);

        WidgetsBinding.instance.addPostFrameCallback((_) {
          // custom discount
          if (context.read<DiscountProvider>().cartData != null &&
              context.read<DiscountProvider>().cartData!.cartCustomDiscount !=null &&
              context.read<DiscountProvider>().cartData!.cartCustomDiscount!.isNotEmpty &&
              context.read<DiscountProvider>().cartData!.cartCustomDiscount!.first.id! == widget.userNotificationData!.data!.discountId) {

            if (ApiConstant.VALUE_TYPE_FLAT == context.read<CartViewProvider>().cartData!.cartCustomDiscount!.first.valueType) {
              context.read<DiscountProvider>().setDiscountType("\$", Alignment.centerRight);
              context.read<DiscountProvider>().setDiscountAmount(NumberFormat.simpleCurrency().format(context .read<CartViewProvider>().cartData!.cartCustomDiscount!.first.appliedValue));
            } else {
              context.read<DiscountProvider>()
                  .setDiscountType("\%", Alignment.centerLeft);
              context.read<DiscountProvider>().setDiscountAmount(context .read<CartViewProvider>().cartData!.cartCustomDiscount!.first
                  .value
                  .toString());
            }
            context.read<DiscountProvider>().setDiscountNote(context.read<DiscountProvider>().cartData?.cartCustomDiscount?.first.note?.toString() ??"");

          }else if (context.read<DiscountProvider>().cartData != null && context.read<DiscountProvider>().cartData!.cartDiscount != null) {
            if (ApiConstant.VALUE_TYPE_FLAT == context.read<CartViewProvider>().cartData!.cartDiscount!.valueType) {
              context.read<DiscountProvider>().setDiscountType("\$", Alignment.centerRight);
              context.read<DiscountProvider>().setDiscountAmount(NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().cartData!.cartDiscount!.appliedValue));
            } else {
              context.read<DiscountProvider>().setDiscountType("\%", Alignment.centerLeft);
              context.read<DiscountProvider>().setDiscountAmount(context.read<CartViewProvider>().cartData!.cartDiscount!.value.toString());
            }
          }
        });
      }  catch (error) {
        debugPrint('Getting error : ${error.toString()}');
        errorAlertDialog(error);
        return;
    }
  }
}
