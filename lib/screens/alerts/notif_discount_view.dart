import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/utils/string_extension.dart';
import 'package:provider/provider.dart';
import 'package:gr8tables_server_manager/constants/app_color.dart';
import 'package:gr8tables_server_manager/custom_widgets/app_text_style.dart';
import 'package:gr8tables_server_manager/models/notification/all_notification_response.dart';
import 'package:gr8tables_server_manager/providers/discount/discount_provider.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';

class NotifDiscountView extends StatefulWidget {
  final int? cartId;
  final UserNotificationData? userNotificationData;
  final TextEditingController amountController;
  final TextEditingController noteController;
  final FocusNode amountFieldFocus;
  final FocusNode noteFieldFocus;

  const NotifDiscountView({
    Key? key,
    required this.cartId,
    required this.userNotificationData,
    required this.amountController,
    required this.noteController,
    required this.amountFieldFocus,
    required this.noteFieldFocus,
  }) : super(key: key);

  @override
  State<NotifDiscountView> createState() => _NotifDiscountViewState();
}

class _NotifDiscountViewState extends State<NotifDiscountView> {
  @override
  Widget build(BuildContext context) {
    return Consumer2<DiscountProvider, CartViewProvider>(
      builder: (context, discountProvider, cartViewProvider, child) {
        return Container(
          margin: EdgeInsets.symmetric(vertical: 5,horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(7.0),
                      decoration: BoxDecoration(
                        color: discountProvider.customDiscountError.isNotEmpty
                            ? ColorConstant.colorLightRed_20
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(
                          color: discountProvider.customDiscountError.isNotEmpty
                              ? ColorConstant.colorLightRed_20
                              : ColorConstant.colorBlueLight_8,
                          width: 2,
                        ),
                      ),
                      child: Container(
                        margin: const EdgeInsets.only(top: 5, left: 5),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                style: AppTextStyle.smallTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                                  letterSpacing: 0.3,
                                  color: ColorConstant.colorBlueDark,
                                ),
                                children: [
                                  TextSpan(
                                    text: "${widget.userNotificationData!.data!.discountType?.toString().capitalizeFirst()} ",
                                    style: AppTextStyle.smallTextStyle.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: "Discount Amount",
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 15),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                _switchDiscount(),
                                Expanded(child: _buildAmountTextField()),
                                if (cartViewProvider.cartData?.cartCustomDiscount?.isNotEmpty == true &&
                                    cartViewProvider.cartData!.cartCustomDiscount!.first.valueType == "percentage")...[
                                  Text('%',
                                    style: AppTextStyle.largeTextStyle.copyWith(
                                      fontSize: isBigScreenResolution(context) ? 24.0 : 14.0,
                                      letterSpacing: 0.5,
                                      color: ColorConstant.colorBlueDark,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 7),
              if (discountProvider.customDiscountError.isNotEmpty)
                _buildErrorMessage(),
              if (widget.noteController.text.isNotEmpty)
                _notesSection(),
              if (widget.amountFieldFocus.hasFocus) const SizedBox(height: 60),
              if (widget.noteFieldFocus.hasFocus) const SizedBox(height: 120),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAmountTextField() {
    return TextField(
      enabled: false,
      textAlign: TextAlign.right,
      showCursor: true,
      keyboardType: TextInputType.numberWithOptions(decimal: true),
      maxLines: 1,
      controller: widget.amountController,
      focusNode: widget.amountFieldFocus,
      autofocus: true,
      cursorColor: ColorConstant.colorBlueDark,
      onChanged: (value) {
        context.read<DiscountProvider>().hasApplyCustomDiscountError(0, forCart: true);
        if (value.isNotEmpty) {
          var tmp = value.replaceAll("\$", "").replaceAll(",", "").trim();
          tmp = tmp.isEmpty ? "0" : tmp;
          context.read<DiscountProvider>().hasApplyCustomDiscountError(
            num.parse(tmp),
            forCart: true,
          );
        } else {
          context.read<DiscountProvider>().hasApplyCustomDiscountError(0, forCart: true);
        }
      },
      onSubmitted: (value) {},
      style: AppTextStyle.largeTextStyle.copyWith(
        color: context.read<DiscountProvider>().customDiscountError.isNotEmpty
            ? ColorConstant.colorLightRed_20
            : ColorConstant.colorBlueDark,
        fontSize: isBigScreenResolution(context) ? 24.0 : 14.0,
        letterSpacing: 0.5,
      ),
      decoration: InputDecoration(
        filled: false,
        border: InputBorder.none,
        hintText: context.read<DiscountProvider>().discountType == '\$'
            ? "\$0.00"
            : "0.00",
        contentPadding: EdgeInsets.zero,
        hintStyle: AppTextStyle.largeTextStyle.copyWith(
          letterSpacing: 0.5,
          fontSize: isBigScreenResolution(context) ? 24.0 : 14.0,
          color: ColorConstant.colorBlueDark,
        ),
      ),
      onTapOutside: (event) {
        FocusManager.instance.primaryFocus?.unfocus();
      },
    );
  }

  Widget _buildErrorMessage() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        margin: const EdgeInsets.only(left: 5),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(
              Icons.info_sharp,
              color: ColorConstant.colorLightRed_20,
              size: 13,
            ),
            const SizedBox(width: 2),
            Flexible(
              child: Text(
                context.read<DiscountProvider>().customDiscountError,
                style: AppTextStyle.smallTextStyle.copyWith(
                  color: ColorConstant.colorLightRed_20,
                  fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                ),
                softWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _switchDiscount() {
    return GestureDetector(
      onTap: () {
        // Handle discount type switching
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 70,
        height: 28,
        curve: Curves.decelerate,
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstant.colorBlueDark, width: 1),
          borderRadius: BorderRadius.circular(3),
          color: Colors.white,
        ),
        child: Stack(
          children: [
            _symbolTextView(
              label: '%',
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(left: 10.0),
            ),
            _symbolTextView(
              label: '\$',
              alignment: Alignment.centerRight,
              padding: const EdgeInsets.only(right: 10.0),
            ),
            AnimatedAlign(
              duration: const Duration(milliseconds: 300),
              alignment: context.read<DiscountProvider>().knobAlignment,
              curve: Curves.decelerate,
              child: Container(
                decoration: BoxDecoration(
                  color: ColorConstant.colorBlueDark,
                  borderRadius: BorderRadius.circular(5),
                ),
                margin: const EdgeInsets.only(
                  top: 3,
                  bottom: 3,
                  left: 3,
                  right: 3,
                ),
                padding: const EdgeInsets.only(
                  left: 12,
                  right: 12,
                  top: 2,
                  bottom: 2,
                ),
                child: Text(
                  context.read<DiscountProvider>().discountType,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: 'Roboto',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _symbolTextView({
    String label = '',
    AlignmentGeometry alignment = Alignment.center,
    required EdgeInsetsGeometry padding,
  }) {
    return Padding(
      padding: padding,
      child: Align(
        alignment: alignment,
        child: Text(
          label,
          style: const TextStyle(
            color: ColorConstant.colorGrayDark,
            fontSize: 12,
            fontFamily: 'Roboto',
          ),
        ),
      ),
    );
  }

  Widget _notesSection() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 5, bottom: 5),
      padding: const EdgeInsets.all(7.0),
      decoration: BoxDecoration(
        color: context.read<DiscountProvider>().customDiscountError.isNotEmpty
            ? ColorConstant.colorLightRed_20
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: context.read<DiscountProvider>().customDiscountError.isNotEmpty
              ? ColorConstant.colorLightRed_20
              : ColorConstant.colorBlueLight_8,
          width: 2,
        ),
      ),
      child: Text(widget.noteController.text,
        maxLines: 2, overflow: TextOverflow.ellipsis,
        style: AppTextStyle.smallTextStyle.copyWith(
          fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
          letterSpacing: 0.5,
          color: ColorConstant.colorBlueDark,
        ),
      ),
    );
  }

  bool isBigScreenResolution(BuildContext context) {
    return MediaQuery.of(context).size.width > 600;
  }
}