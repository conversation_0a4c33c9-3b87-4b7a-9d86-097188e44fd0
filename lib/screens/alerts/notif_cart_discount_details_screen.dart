import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_color.dart';
import 'package:provider/provider.dart';

import '../../constants/app_dimens.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/cart/cart_custom_discount_response.dart';
import '../../models/notification/activeview_provider.dart';
import '../../models/notification/all_notification_response.dart';
import '../../my_app.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/discount/discount_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import '../../utils/datetime_utils.dart';
import 'notif_cart_discount_pending_view.dart';
import 'notif_cart_discount_sent_view.dart';

class NotifCartDiscountDetailsScreen extends StatefulWidget {
  final UserNotificationData notification;

  const NotifCartDiscountDetailsScreen({
    Key? key,
    required this.notification,
  }) : super(key: key);

  @override
  State<NotifCartDiscountDetailsScreen> createState() =>
      _NotifCartDiscountDetailsScreenState();
}

class _NotifCartDiscountDetailsScreenState extends State<NotifCartDiscountDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    

    
    // // Load cart data if cartId is available
    if (widget.notification.data?.cartId != null) {
      debugPrint(
          "Loading cart data for cartId: ${widget.notification.data!.cartId}");
        // _getCart(widget.notification.data!.cartId!);
    } else {
      debugPrint("No cart ID available in notification data");
    }
    _tabController = TabController(length: 2, vsync: this);
    if(context.read<CartViewProvider>().getPendingItemsCount()>0){
      _tabController.animateTo(0);
    }else{
      _tabController.animateTo(1);
    }

    _tabController.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          color: ColorConstant.white,
          child: Column(
            children: [
              // Custom header section replacing AppBar
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    if(context.watch<NotificationProvider>().showApproveActionButton)...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8.0, horizontal: 16.0),
                        child:_backToPreviousView(),
                      ),
                    ] else...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8.0, horizontal: 16.0),
                        child:_headerApproveView(),
                      ),
                    ],
                    // if(!context.watch<NotificationProvider>().showApproveActionButton)
                    // buildOrderNumberRow(),
                    // Tab buttons
                    Container(
                      margin: EdgeInsets.only(left: 10.0),
                      padding: const EdgeInsets.symmetric(
                          vertical: 8.0, horizontal: 16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: context.watch<CartViewProvider>().getPendingItemsCount() <= 0
                                  ? null
                                  : () {
                                _tabController.animateTo(0);
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 12.0, horizontal: 16.0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25.0),
                                  color: _tabController.index == 0
                                      ? ColorConstant.colorBlueDark
                                      : ColorConstant.customerBackground,
                                ),
                                child: Text(
                                  '${NotificationsString.pendingItems} ${context.watch<CartViewProvider>().getPendingItemsCount()}',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: _tabController.index == 0
                                        ? Colors.white
                                        : ColorConstant.notifDetailText,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: GestureDetector(
                              onTap: context.watch<CartViewProvider>().getSubmittedItemsCount() <= 0
                                  ? null
                                  : () {
                                      _tabController.animateTo(1);
                                    },
                              child: Container(
                                margin: EdgeInsets.only(right: 10.0),
                                padding: const EdgeInsets.symmetric(
                                    vertical: 12.0, horizontal: 16.0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25.0),
                                  color: _tabController.index == 1
                                      ? ColorConstant.colorBlueDark
                                      : ColorConstant.customerBackground,
                                ),
                                child: Text(
                                  '${NotificationsString.sentItems} ${context.watch<CartViewProvider>().getSubmittedItemsCount()}',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: _tabController.index == 1
                                        ? Colors.white
                                        : Colors.grey.shade800,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // TabBarView content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  // Disable swiping
                  children: [
                    // Pending Items Tab Content
                    NotifCartDiscountPendingItemView(
                      cartId: widget.notification.data?.cartId,
                      userNotificationData: widget.notification,
                    ),
                    // Sent Items Tab Content
                    NotifCartDiscountSentItemView(
                      cartId: widget.notification.data?.cartId,
                      userNotificationData: widget.notification,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomSheet: context.watch<NotificationProvider>().showApproveActionButton ? Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, -1),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () async{
                    await _approveDiscount(approveDiscount: false);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black87,
                    elevation: 0,
                    side: BorderSide(color: Colors.grey.shade300),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: const Text(
                    NotificationsString.deny,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async{
                      // Implement approval logic
                      await _approveDiscount(approveDiscount: true);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.colorBlueDark,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: const Text(
                      NotificationsString.approve,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ) : Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, -1),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
            ],
          ),
        ),
      ),
    );
  }

  /// back to previous view
  _backToPreviousView() {
    return Container(
      margin: const EdgeInsets.only(
        bottom: 15,
      ),
      child: InkWell(
        onTap: () {
          Navigator.of(context).pop();
          context.read<DiscountProvider>().setCustomNextPressed(false);
          context.read<DiscountProvider>().resetCustomDiscount();
          context.read<DiscountProvider>().reset();
          context.read<CartViewProvider>().reset();
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.arrow_back_ios_outlined,
              size: isBigScreenResolution(context) ? 15.0 : 13.0,
              color: ColorConstant.colorBlueDark,
            ),
            WidthDimens().getWidth12(),
            Expanded(
              flex: 10,
              child: RichText(
                text: TextSpan(
                  text: CartCustomDiscountString.discountOption,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorBlueLight_50,
                    fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                  ),
                  children: [
                    TextSpan(
                      text: '  /  ${CartCustomDiscountString.optionCart}',
                      style: AppTextStyle.largeTextStyle.copyWith(
                        fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                        color: ColorConstant.colorBlueDark,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                context.read<DiscountProvider>().setCustomNextPressed(false);
                Navigator.of(context).pop();
              },
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: ColorConstant.colorBlueDark,
                    width: 1.0,
                  ),
                ),
                padding: const EdgeInsets.all(2.0),
                child: Icon(
                  Icons.close_outlined,
                  size: isBigScreenResolution(context) ? 16.0 : 12.0,
                  color: ColorConstant.colorBlueDark,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _headerApproveView() {
    return Container(
      child: InkWell(
        onTap: () {
          Navigator.of(context).pop();
          context.read<DiscountProvider>().setCustomNextPressed(false);
          context.read<DiscountProvider>().resetCustomDiscount();
          context.read<DiscountProvider>().reset();
          context.read<CartViewProvider>().reset();
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
              },
              child:   Icon(
                Icons.close,
                size: isBigScreenResolution(context) ? 20.0 : 18.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),

            WidthDimens().getWidth12(),
            Expanded(
              flex: 10,
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  ' ${NotificationsString.order}:  ${context.watch<CartViewProvider>().getSubmittedItemsCount()} ${NotificationsString.items}',
                  style: AppTextStyle.largeTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                    color: ColorConstant.notifTextColor,
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                // context.read<DiscountProvider>().setCustomNextPressed(false);
                //Navigator.of(context).pop();
              },
              child: Container(
                decoration: BoxDecoration(
                  color: ColorConstant.customerBackground,
                  shape: BoxShape.rectangle,
                ),
                padding: const EdgeInsets.all(5.0),
                child: Text(
                    'T${context.watch<CartViewProvider>().cartData?.tableId ?? 0}',
                    style: AppTextStyle.mediumTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                    color: ColorConstant.notifTextColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// get cart
  Future<void> _getCart(int cartId) async {
    var hasConnected =
        await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        await context.read<CartViewProvider>().getCart(id: cartId);

        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  Widget buildOrderNumberRow() {
    return Container(
      padding: EdgeInsets.all(10.0),
      margin: EdgeInsets.only(bottom: 5),
      color: Colors.grey[200],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '#${context.watch<CartViewProvider>().cartData!.id??0}',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text(

            '${DateTimeUtils.utcTimeToConvertLocalTime(
                context.watch<CartViewProvider>().cartData!.updatedAt!,
                formattedString: DateTimeUtils
                .dd_mm_yyyy_hh_mm_a)}',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  /// Approve  discount
  _approveDiscount({bool approveDiscount = false,}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        // find discount id
        num? discountId = 0;
        if(widget.notification.data?.discountType == NotificationsString.presetDiscountType){
          discountId = context.read<CartViewProvider>().cartData?.cartDiscount?.id ?? 0;
        }else if(widget.notification.data?.discountType == NotificationsString.customDiscountType){
          discountId = context.read<CartViewProvider>().cartData?.cartCustomDiscount?.first.id ?? 0;
        }
        CartCustomDiscountResponse cartCustomDiscountResponse =
        await context.read<DiscountProvider>().approveCustomDiscount(id: discountId,
            isApprove: approveDiscount,discountType: widget.notification.data?.discountType,
            note:context.read<DiscountProvider>().discountApproveNote);

        if (!mounted) return;
        hideLoaderDialog(context);
        context.read<DiscountProvider>().setSuccessMsg(cartCustomDiscountResponse.message!);
        if(approveDiscount){
          await _markReadNotification();
          await _getAllNotification();
          context.read<NotificationProvider>().setShowApproveActionButton(false);
          Navigator.pop(context);
          if( context.read<CartViewProvider>().cartData?.tableId != null){
            _getCart(context.read<CartViewProvider>().cartData!.id!);
            navigateToCartScreen(context, context.read<CartViewProvider>().cartData!.id!, context.read<CartViewProvider>().cartData!.tableId!);
          }else{
            debugPrint("Not table data found userData.data!.tableId:${context.read<CartViewProvider>().cartData!.tableId}");
          }
        }
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        errorUnauthorisedAlertDialog(
          navigatorKey.currentState!.overlay!.context,
          error,
        );
        return;
      } on FetchDataException catch (error) {
        debugPrint('apply custom discount data error : ${error.toString()}');
        if (!mounted) return;
        hideLoaderDialog(context);
        await context
            .read<DiscountProvider>()
            .setCustomDiscountError(error.toString());
        showErrorDialog(error:error.toString());
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ${error.toString()}');
        if (!mounted) return;
        hideLoaderDialog(context);
        await context
            .read<DiscountProvider>()
            .setCustomDiscountError(error.toString());
        showErrorDialog(error:error.toString());
        return;
      } catch (error) {
        debugPrint('Getting error : ${error.toString()}');
        if (!mounted) return;
        hideLoaderDialog(context);
        await context
            .read<DiscountProvider>()
            .setCustomDiscountError(error.toString());
        showErrorDialog(error:error.toString());
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// mark as read notifications
  _markReadNotification() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<ActiveViewProvider>().markReadNotification(
            isRead: true,
            notificationId: context.read<ActiveViewProvider>().selectedNotificationId
        );
        await context.read<NotificationProvider>().getUnReadCount();

        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// get all notification
  _getAllNotification() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<NotificationProvider>().getUnReadCount();
        await context.read<ActiveViewProvider>().resetCurrentPage();
        await context.read<ActiveViewProvider>().getActiveAllNotification();

        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }


}
