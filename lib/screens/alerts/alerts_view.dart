import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/providers/alerts/alerts_provider.dart';
import 'package:gr8tables_server_manager/screens/alerts/active_view.dart';
import 'package:gr8tables_server_manager/screens/alerts/alert_history_view.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/nav_menu.dart';
import '../../providers/homescreen/bottom_sheet_provider.dart';
import '../../utils/app_utils.dart';
import '../../utils/pref_utils.dart';

class AlertsView extends StatefulWidget {
  const AlertsView({super.key});

  @override
  State<AlertsView> createState() => _AlertsViewState();
}

class _AlertsViewState extends State<AlertsView>  with SingleTickerProviderStateMixin, WidgetsBindingObserver{
  late TabController _tabController;
  @override
  void initState() {
    _tabController = TabController(length: 2, vsync: this);

    super.initState();
    WidgetsBinding.instance.addObserver(this); // Add observer
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async{
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      debugPrint("App is in foreground!(AlertsView)");

      String? isNotificationReceived = PrefsUtils.getString(PrefKeys.fcmReceived);
      debugPrint("fcmReceived (AlertsView) $isNotificationReceived");
      if (isNotificationReceived == "true") {
        _tabController.animateTo(0); // Smoothly transitions to the first tab
      }else{
        debugPrint("isNotificationReceived false(AlertsView)");
      }

    } else if (state == AppLifecycleState.paused) {
      debugPrint("App is in background!");
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: isBigScreenResolution(context) ? 8 : 6,
                right: isBigScreenResolution(context) ? 8 : 6,
                top: isBigScreenResolution(context) ? 8 : 6,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _tabBar(),
                  Expanded(child: _tabContainer()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }


  /// Handle the navigation bar
  _handleNavigationBar() {
    return NavigationMenu(
      onMenuSelected: (selectedMenu) {
        switch (selectedMenu) {
          case BottomBarNavigationString.more:
            debugPrint('Alert in page $selectedMenu');
            context.read<BottomSheetProvider>().setDialogForDinePage(!context.read<BottomSheetProvider>().showDialogDine);
            break;
        }
      },
    );
  }

  /// tab bar
  Widget _tabBar(){
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      tabAlignment: TabAlignment.center,
      physics: const NeverScrollableScrollPhysics(),
      dividerColor: Colors.transparent,
      indicatorColor: ColorConstant.colorBlueDark,
      indicatorSize: TabBarIndicatorSize.label,
      tabs: const [
        Tab(text: AlertsString.active,),
        Tab(text: AlertsString.history,),
      ],
    );
  }

  /// tab bar container
  Widget _tabContainer(){
    return Consumer<AlertsProvider>(
      builder: (context, data, child) {
        return TabBarView(
          controller: _tabController,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            ActiveView(),
            AlertHistoryView(),
          ],
        );
      },
    );

  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
