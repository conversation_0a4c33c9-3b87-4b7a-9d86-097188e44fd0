import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:gr8tables_server_manager/constants/app_string.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';
import 'package:gr8tables_server_manager/screens/alerts/notif_cart_summary.dart';
import 'package:gr8tables_server_manager/utils/string_extension.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'dart:io' show Platform;

import '../../../../helpers/currency_text_formatter.dart';
import '../../../helpers/check_internet_connection.dart';
import '../../../helpers/http_response_helper.dart';
import '../../../models/discount/promotion_apply_on.dart';
import '../../../my_app.dart';
import '../../constants/api_constant.dart';
import '../../constants/app_color.dart';
import '../../constants/app_dimens.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/dotted_decoration.dart';
import '../../models/cart/cart_custom_discount_response.dart';
import '../../models/cart/cart_items/cart_item_modifiers.dart';
import '../../models/cart/cart_items/cart_items.dart';
import '../../models/cart/cart_promotions/cart_discount.dart';
import '../../models/cart/cart_promotions/cart_loyalty.dart';
import '../../models/cart/cart_promotions/cart_promocode.dart';
import '../../models/cart/cart_users/cart_user_data.dart';
import '../../models/notification/all_notification_response.dart';
import '../../providers/discount/discount_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import 'notif_discount_view.dart';

class NotifPendingItemDiscountView extends StatefulWidget {
  final int? cartId;
  final UserNotificationData? userNotificationData;

  const NotifPendingItemDiscountView({
    Key? key,
    this.cartId,
    this.userNotificationData,
  }) : super(key: key);

  @override
  State<NotifPendingItemDiscountView> createState() =>
      _NotifPendingItemDiscountViewState();
}

class _NotifPendingItemDiscountViewState
    extends State<NotifPendingItemDiscountView>
    with WidgetsBindingObserver {
  late TextEditingController _amountController, _noteController;
  late FocusNode _amountFieldFocus, _noteFieldFocus;
  ScrollController _scrollController = ScrollController();

  /// Determine whether the keyboard is hidden.
  Future<bool> get keyboardHidden async {
    final check = () =>
    (WidgetsBinding.instance.window.viewInsets.bottom) <= 0;
    if (!check()) return false;
    return await Future.delayed(
      Duration(milliseconds: 100),
          () => check(),
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _amountController = TextEditingController();
    _noteController = TextEditingController();
    _amountFieldFocus = FocusNode();
    _noteFieldFocus = FocusNode();

    // Load cart data if cartId is available
    if (widget.cartId != null) {

      WidgetsBinding.instance.addPostFrameCallback((_) {
        debugPrint("Loading cart data for cartId: ${widget.cartId}");
        if (context.read<DiscountProvider>().cartData != null &&
            context.read<DiscountProvider>().cartData!.cartCustomDiscount !=
                null &&
            context.read<DiscountProvider>().cartData!.cartCustomDiscount!.isNotEmpty) {
          if (ApiConstant.VALUE_TYPE_FLAT == context.read<CartViewProvider>().cartData!.cartCustomDiscount!.first.valueType) {
            context.read<DiscountProvider>().setDiscountType("\$", Alignment.centerRight);
            _amountController.text = NumberFormat.simpleCurrency().format(context .read<CartViewProvider>().cartData!.cartCustomDiscount!.first.appliedValue);
          } else {
            context.read<DiscountProvider>()
                .setDiscountType("\%", Alignment.centerLeft);
            _amountController.text = context .read<CartViewProvider>().cartData!.cartCustomDiscount!.first
                .value
                .toString();
          }

          _noteController.text = context.read<DiscountProvider>().cartData?.cartCustomDiscount?.first.note?.toString() ??"";
        }else if (context.read<DiscountProvider>().cartData != null && context.read<DiscountProvider>().cartData!.cartDiscount != null) {
          if (ApiConstant.VALUE_TYPE_FLAT == context.read<CartViewProvider>().cartData!.cartDiscount!.valueType) {
            context.read<DiscountProvider>().setDiscountType("\$", Alignment.centerRight);
            _amountController.text = NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().cartData!.cartDiscount!.appliedValue);
          } else {
            context.read<DiscountProvider>().setDiscountType("\%", Alignment.centerLeft);
            _amountController.text = context.read<CartViewProvider>().cartData!.cartDiscount!.value.toString();
          }
        }
      });

    } else {
      debugPrint("No cart ID available in notification data");
    }
  }

  @override
  void didChangeMetrics() {
    final value = WidgetsBinding.instance.window.viewInsets.bottom;
    if (value == 0) {
      // Handle keyboard dismissal
    }
    if (Platform.isAndroid) {
      keyboardHidden.then((value) =>
      value ? FocusManager.instance.primaryFocus?.unfocus() : null);
    }
    super.didChangeMetrics();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    _amountFieldFocus.dispose();
    _noteFieldFocus.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CartViewProvider>(
      builder: (context, data, child) {
        return SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.only(
              top: 10,
              bottom: 10,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                _pendingOrderView(data),
                if(context.watch<NotificationProvider>().showApproveActionButton)
                NotifDiscountView(
                  cartId: widget.cartId,
                  userNotificationData: widget.userNotificationData,
                  amountController: _amountController,
                  noteController: _noteController,
                  amountFieldFocus: _amountFieldFocus,
                  noteFieldFocus: _noteFieldFocus,
                ),
                if(context.watch<CartViewProvider>().getPendingItemsCount()>0)
                NotifCartSummaryView(),
              ],
            ),
          ),
        );
      },
    );
  }



  /// promotional view
  Widget _promotionalView({String type='',bool onCart =false,String? label= CartViewString.custom ,num? value =0, Function()? onRemove , disableAction=false, showDiscountPendingLabel=true,
    bool approved = true,}) {
    return Container(
      //margin:  EdgeInsets.only(left: 5, right: 5,),
      padding: const EdgeInsets.all(7.0,),
      decoration: DottedDecoration(shape: Shape.box ,
        strokeWidth: 0.50,
        color: ColorConstant.colorBlueDark,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon( onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp, size: 14, color: ColorConstant.colorBlueDark,),
          const SizedBox(width: 5,),
          Expanded(
            child:Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text( type ,style: AppTextStyle.mediumTextStyle.copyWith(
                  color: ColorConstant.colorBlueDark,
                  fontSize: isBigScreenResolution(context)
                      ? 14.0
                      : 12.0,
                ), ),
                Flexible(child: Text(' $label '.replaceAll("", "\u{200B}"),maxLines: 1, overflow: TextOverflow.ellipsis ,style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context)
                      ? 14.0
                      : 12.0,
                  color: ColorConstant.colorBlueDark,
                ),),),
                Text('(-${NumberFormat.simpleCurrency().format((value!))})',style: AppTextStyle.smallTextStyle.copyWith(
                  color: ColorConstant.colorBlueDark,
                  fontSize: isBigScreenResolution(context)
                      ? 14.0
                      : 12.0,
                ),),
                const SizedBox(width: 5,),
              ],
            ),
          ),

          if(context.read<NotificationProvider>().showApproveActionButton)...[
            if(showDiscountPendingLabel)...[
              if(!approved)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: ColorConstant.notifPendingBackground,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  "Pending",
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.notifPendingTextColor,
                    fontSize: 12,
                  ),
                ),
              ),
            ]else...[
              GestureDetector(
                onTap: null,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: ColorConstant.notifIconColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    size: isBigScreenResolution(context) ? 16.0 : 12.0,
                    color: ColorConstant.white,
                  ),
                ),
              )
            ],
          ]else...[
            GestureDetector(
              onTap: null,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: ColorConstant.notifIconColor,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: isBigScreenResolution(context) ? 14.0 : 12.0,
                  color: ColorConstant.white,
                ),
              ),
            )
          ],
          // if(!disableAction)...[InkWell(onTap: onRemove!,child: Icon(  Icons.cancel_sharp, size: 17, color: ColorConstant.colorBlueDark,)),],
        ],
      ),
    );
  }

  Widget get _verticalSpacing7 => SizedBox(
    height: isBigScreenResolution(context) ? 7 : 6,
  );

  /// Submitted order view
  Widget _pendingOrderView(CartViewProvider data) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (data.pendingCartItemMap.isEmpty) ...[
                _verticalSpacing7,
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        CartViewString.billIsEmpty,
                        textAlign: TextAlign.center,
                        style: AppTextStyle.smallTextStyle.copyWith(
                          color: ColorConstant.colorBlueLight_50,
                          fontSize: isBigScreenResolution(context) ? 14 : 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
              ...data.pendingCartItemMap.entries.map(
                    (e) => _userWiseCartView(e),
              ),
            ],
          ),
          cutImage(
            7.0,
            MediaQuery.of(context).size.width,
            AssetIcons.bottomCut,
          ),
        ],
      ),
    );
  }


  /// Item row view
  Widget _itemRowView(CartItems item, {bool hideModifiers = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 10,),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Checkbox(
                value:item.cartCustomDiscountItem!.isNotEmpty ? true : false,
                onChanged: (value){},
                // Hide the default border
              ),
              SizedBox(width: isBigScreenResolution(context) ? 10 : 8,),
              Expanded(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name ?? '-',
                        style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: isBigScreenResolution(context)
                              ? 16.0
                              : 14.0,
                          color: ColorConstant.colorBlueDark,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Row(
                        children: [
                          if(item.afterPromotionValue!= item.lineTotal || (item.afterPromotionValue! == item.customDiscount))...[
                            Text(
                              '${NumberFormat.simpleCurrency().format((item.lineTotal!))}',
                              style: AppTextStyle.mediumTextStyle.copyWith(
                                decoration: TextDecoration.lineThrough,
                                color: ColorConstant.colorBlueDark,
                                fontSize: isBigScreenResolution(context)
                                    ? 12.0
                                    : 10.0,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                          SizedBox(width: 5,),
                          // after discount price
                          Text(
                            _showRemainingItemDiscount(item),
                            style: AppTextStyle.largeTextStyle.copyWith(
                              fontSize: isBigScreenResolution(context)
                                  ? 12.0
                                  : 10.0,
                              letterSpacing: 0.05,
                              color: ColorConstant.bluePriceFontColor,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                      if (item.category!.isNotEmpty) ...[
                        Row(
                          children: [
                            Text(
                              item.category!,
                              textAlign: TextAlign.start,
                              style: AppTextStyle.smallTextStyle.copyWith(
                                color: ColorConstant.warningOrangeFontColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ]
                ),
              ),
              // Quantity add and remove view
              Container(
                decoration: BoxDecoration(
                  color: ColorConstant.notifItemDeleteBgColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: null,
                      child: Container(
                        padding: EdgeInsets.all(6),
                        child: Icon(
                          CupertinoIcons.delete,
                          size: 15,
                          color: ColorConstant.notifIconColor,
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Text(
                      '${item.itemQty??0}',
                      style: TextStyle(fontSize: 14),
                    ),
                    SizedBox(width: 12),
                    InkWell(
                      onTap: null,
                      child: Container(
                        padding: EdgeInsets.all(6),
                        child: Icon(
                          CupertinoIcons.add,
                          size: 15,
                          color: ColorConstant.notifIconColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 5,),
          if(item.cartCustomDiscountItem!.isNotEmpty)...[

            ...item.cartCustomDiscountItem!.map((discountItem) {
              if( (discountItem.applyOn == PromotionApplyOn.product.name ) ||
                  discountItem.applyOn == PromotionApplyOn.multiple_product_qty.name) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [

                    SizedBox(
                      height: isBigScreenResolution(context)
                          ? 9
                          : 6,),
                    _promotionalView(
                      type: CartViewString.custom,
                      label: CartViewString.discount,
                      onCart: discountItem.applyOn!
                          .toLowerCase() ==
                          PromotionApplyOn.cart.name,
                      value: discountItem.appliedValue,
                      onRemove: () {
                        // _removePromotions(forCustomDisc: true,
                        //     forPromo: false,
                        //     id: discountItem
                        //         .cartCustomDiscountId);
                      },
                      disableAction: discountItem.applyOn!
                          .toLowerCase() ==
                          PromotionApplyOn.cart.name,
                      showDiscountPendingLabel: false,
                    ),
                  ],
                );
              }else {
                return Container();
              }
            }),

          ],
          // Add special instructions view
          Container(
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'Add special instructions',
                  style: AppTextStyle.smallTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                    color: ColorConstant.notifItemNoteColor,
                  ),
                ),
                SizedBox(width: 10),
                Image.asset(
                  AssetImages.note,
                  width: isBigScreenResolution(context) ? 14 : 12,
                  height: isBigScreenResolution(context) ? 14 : 12,
                  color: ColorConstant.notifItemNoteColor,
                ),
              ],
            ),
          ),
          if (!hideModifiers) ...[
            if (item.cartItemModifiers!.isNotEmpty) ...[
              SizedBox(
                height: isBigScreenResolution(context) ? 3 : 2,
              ),
              ...item.cartItemModifiers!.map(
                    (e) => _itemModifiersView(e),
              ),
            ],
          ],
          if (item.note!.isNotEmpty) ...[
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: _noteLabelView(
                    '${CartViewString.note} ${item.note!}',
                    textAlign: TextAlign.start,
                    isBold: false,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
  /// show remaining value for
  ///item after discount
  _showRemainingItemDiscount(CartItems data) {

    debugPrint('::: inside remaining block start :::');
    debugPrint('::: custom discount ${data.customDiscount} :::');
    debugPrint('::: after promotional value ${data.afterPromotionValue} :::');
    debugPrint('::: linetotal value ${data.lineTotal} :::');
    if(data.customDiscount == 0) {
      return '${NumberFormat.simpleCurrency().format((data.afterPromotionValue!))}';
    }else {
      if( (data.afterPromotionValue! - data.customDiscount! ) == data.lineTotal!) {

        return '${NumberFormat.simpleCurrency().format((data.lineTotal! -  data.customDiscount!))}';
      }else if( data.afterPromotionValue! == 0) {
        return '\$0.00';
      }else {
        return '${NumberFormat.simpleCurrency().format(( data.afterPromotionValue!))}';
        // return '\$${roundedToTwoDigits(data.afterPromotionValue!)}';
      }
    }
  }


  /// Item modifiers View
  Widget _itemModifiersView(CartItemModifiers itemModifiers) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: isBigScreenResolution(context) ? 6 : 4,
            ),
            Expanded(
              child: Text(
                itemModifiers.name!,
                style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 11.0 : 10.0,
                  color: ColorConstant.colorBlueDark,
                ),
              ),
            ),
          ],
        ),
        if (itemModifiers.cartItemModifierItems!.isNotEmpty) ...[
          ...itemModifiers.cartItemModifierItems!.map(
                (e) => _modifierItemsView(e),
          ),
        ],
        SizedBox(
          height: isBigScreenResolution(context) ? 3 : 2,
        ),
      ],
    );
  }


  /// Modifier items view
  Widget _modifierItemsView(CartItemModifierItems modifierItems) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: isBigScreenResolution(context) ? 4 : 3,
        ),
        Expanded(
          child: Text(
            '  ${modifierItems.qty} x ${modifierItems.name!}',
            style: AppTextStyle.smallTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 10.0 : 9.0,
              color: ColorConstant.colorBlueDark,
            ),
          ),
        ),
      ],
    );
  }

  // Note label view
  Widget _noteLabelView(
      String label, {
        TextAlign textAlign = TextAlign.center,
        bool isBold = true,
      }) {
    return AutoSizeText(
      label,
      textAlign: textAlign,
      maxLines: 2,
      style: AppTextStyle.smallTextStyle.copyWith(
        color: ColorConstant.colorBlueDark,
        fontWeight: isBold ? FontWeight.bold : FontWeight.w400,
      ),
      minFontSize: isBigScreenResolution(context) ? 11 : 10,
      maxFontSize: isBigScreenResolution(context) ? 12 : 11,
    );
  }

  /// User wise cart view
  Widget _userWiseCartView(
      MapEntry<CartUserData, List<CartItems>> e, {
        bool hideModifiers = true,
      }) {
    bool isYou = e.key.name == 'You';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 16.0,
          ),
          color: ColorConstant.customerBackground,
          child: Row(
            children: [
              const Icon(
                CupertinoIcons.person_fill,
                size: 18,
                color: ColorConstant.notifIconColor,
              ),
              const SizedBox(width: 8),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: e.key.userId!.isNotEmpty
                    ? () {
                  navigateToCustomerDetailsScreen(
                    context,
                    e.key.userId!,
                    e.key.name!,
                  );
                }
                    : null,
                child: Row(
                  children: [
                    Text(
                      isYou ? '${e.key.name!} (You)' : e.key.name!,
                      style: AppTextStyle.largeTextStyle.copyWith(
                        color: ColorConstant.notifTextColor,
                        fontSize: 14.0,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Text(
                '${e.value.length} items',
                style: AppTextStyle.smallTextStyle.copyWith(
                  fontWeight: FontWeight.w400,
                  color: ColorConstant.notifItemColor,
                  fontSize: 12.0,
                ),
              ),
            ],
          ),
        ),
        e.value.isEmpty 
            ? Container(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Center(
                  child: Text(
                    NotificationsString.noItems,
                    style: AppTextStyle.mediumTextStyle.copyWith(
                      color: ColorConstant.notifTextColor,
                      fontSize: 14.0,
                    ),
                  ),
                ),
              )
            : ListView.separated(
                separatorBuilder: (context, index) {
                  return Divider(
                    height: 1,
                    color: ColorConstant.customerBackground,
                  );
                },
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: e.value.length,
                itemBuilder: (context, index) {
                  final item = e.value[index];
                  return _itemRowView(item, hideModifiers: hideModifiers);
                },
              ),
      ],
    );
  }
}
