import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_color.dart';
import 'package:gr8tables_server_manager/custom_widgets/app_text_style.dart';
import 'package:provider/provider.dart';

import '../../constants/app_string.dart';
import '../../providers/discount/discount_provider.dart';


// Widget displaying discount approval/deny note UI for notifications
class NotifDiscountApproveDenyNoteView extends StatefulWidget {


   NotifDiscountApproveDenyNoteView({
    Key? key,
  }) : super(key: key);

  @override
  State<NotifDiscountApproveDenyNoteView> createState() =>
      _NotifDiscountApproveDenyNoteViewState();
}

class _NotifDiscountApproveDenyNoteViewState
    extends State<NotifDiscountApproveDenyNoteView> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _textFieldKey = GlobalKey();
  late TextEditingController noteController;
  late FocusNode noteFieldFocus;

  @override
  void initState() {
    super.initState();
    noteController = TextEditingController();
    noteFieldFocus = FocusNode();
    noteFieldFocus.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    noteFieldFocus.removeListener(_onFocusChange);
    _scrollController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (noteFieldFocus.hasFocus == true) {
      _focusChange(noteFieldFocus);
    }
  }

  // focus change listeners
  void _focusChange(FocusNode focusNode) {

    Future.delayed(
      Duration(milliseconds: 600),
          () {
        Scrollable.ensureVisible(
          focusNode.context!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          alignment: 0.5,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text("Discount Approve/ Deny Note", style: AppTextStyle.smallTextStyle.copyWith(
        //   fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
        //   letterSpacing: 0.5,
        //   color: ColorConstant.colorBlueDark,
        //   fontWeight: FontWeight.w600,
        //   ),
        // ),
        TextField(
          key: _textFieldKey,
          controller: noteController,
          focusNode: noteFieldFocus,
          keyboardType: TextInputType.multiline,
          maxLines: 3,
          textInputAction: TextInputAction.done,
          minLines: 3,
          enabled: true,
          style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
            letterSpacing: 0.5,
            color: ColorConstant.colorBlueDark,
          ),
          cursorColor: ColorConstant.colorGrayDark,
          onChanged: (value) {
            context.read<DiscountProvider>().setDiscountProveNote(value);
          },
          decoration: InputDecoration(
            hintText: NotificationsString.notifyNoteHintText,
            hintStyle: AppTextStyle.smallTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              letterSpacing: 0.5,
              color: ColorConstant.colorGrayDark.withOpacity(0.6),
            ),
            enabledBorder: _outLineBorder,
            focusedBorder: _outLineBorder,
            disabledBorder: _outLineBorder,
            errorBorder: _outLineBorder,
            filled: true,
            border: _outLineBorder,
            fillColor: Colors.transparent,
            contentPadding: const EdgeInsets.all(15),
          ),
          onTapOutside: (event) {
            FocusManager.instance.primaryFocus?.unfocus();
          },
        ),
      ],
    );
  }
   get _outLineBorder =>OutlineInputBorder(
     borderSide: BorderSide(
       width: 1,
       color: ColorConstant.notifBorderColor,
     ),
     borderRadius: BorderRadius.circular(6,),
   );

  bool isBigScreenResolution(BuildContext context) {
    return MediaQuery.of(context).size.width > 600;
  }
}