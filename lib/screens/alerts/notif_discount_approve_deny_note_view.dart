import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_color.dart';
import 'package:gr8tables_server_manager/custom_widgets/app_text_style.dart';
import 'package:provider/provider.dart';

import '../../constants/app_string.dart';
import '../../providers/discount/discount_provider.dart';


// Widget displaying discount approval/deny note UI for notifications
class NotifDiscountApproveDenyNoteView extends StatefulWidget {
   TextEditingController? noteController;
   FocusNode? noteFieldFocus;

   NotifDiscountApproveDenyNoteView({
    Key? key,
  }) : super(key: key);

  @override
  State<NotifDiscountApproveDenyNoteView> createState() =>
      _NotifDiscountApproveDenyNoteViewState();
}

class _NotifDiscountApproveDenyNoteViewState
    extends State<NotifDiscountApproveDenyNoteView> {
  @override
  Widget build(BuildContext context) {
    return  Container(
      padding: const EdgeInsets.all(7.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color:  ColorConstant.colorBlueLight_8,
          width: 2,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Discount Approve/ Deny Note", style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
            letterSpacing: 0.5,
            color: ColorConstant.colorBlueDark,
            fontWeight: FontWeight.w600,
          ),
          ),
          const SizedBox(height: 10),
          TextField(
            controller: widget.noteController,
            focusNode: widget.noteFieldFocus,
            keyboardType: TextInputType.multiline,
            maxLines: 3,
            textInputAction: TextInputAction.done,
            minLines: 3,
            enabled: true,
            style: AppTextStyle.smallTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              letterSpacing: 0.5,
              color: ColorConstant.colorBlueDark,
            ),
            cursorColor: ColorConstant.colorGrayDark,
            onChanged: (value) {
              context.read<DiscountProvider>().setDiscountProveNote(value);
            },
            decoration: InputDecoration(
              hintText: NotificationsString.notifyNoteHintText,
              hintStyle: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                letterSpacing: 0.5,
                color: ColorConstant.colorGrayDark.withOpacity(0.6),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  width: 1,
                  color: ColorConstant.colorBlueLight_8,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  width: 1,
                  color: ColorConstant.colorBlueLight_8,
                ),
              ),
              filled: true,
              border: InputBorder.none,
              fillColor: Colors.transparent,
              contentPadding: const EdgeInsets.all(5),
            ),
            onTapOutside: (event) {
              FocusManager.instance.primaryFocus?.unfocus();
            },
          ),
        ],
      ),
    );
  }

  bool isBigScreenResolution(BuildContext context) {
    return MediaQuery.of(context).size.width > 600;
  }
}