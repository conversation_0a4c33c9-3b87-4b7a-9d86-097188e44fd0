import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_string.dart';
import 'package:gr8tables_server_manager/models/notification/activeview_provider.dart';
import 'package:gr8tables_server_manager/utils/app_routes.dart';
import 'package:gr8tables_server_manager/utils/datetime_utils.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/discount/promotion_apply_on.dart';
import '../../models/notification/all_notification_response.dart';
import '../../my_app.dart';
import '../../providers/alerts/alerts_provider.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/discount/discount_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../utils/app_utils.dart';
import '../../utils/color_pair.dart';
import '../../utils/notification_center/notification_center.dart';
import '../../utils/pref_utils.dart';

class ActiveView extends StatefulWidget {
  const ActiveView({super.key});

  @override
  State<ActiveView> createState() => _ActiveViewState();
}

class _ActiveViewState extends State<ActiveView> with WidgetsBindingObserver{
  late ScrollController _scrollController;


  Color borderColor = Colors.blue;


  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActiveViewProvider>().reset();
    });

    _scrollController = ScrollController();
    NotificationCenter().subscribe(NotificationsString.notifyAlertScreen, (bool data) {
      if(data){
        context.read<ActiveViewProvider>().resetCurrentPage();
        _getAllNotification();
      }
    });
    _getAllNotification();
    _scrollController.addListener(_loadMore);
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Add observer
  }
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async{
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      debugPrint("App is in foreground!(ActiveView)");

      String? fcmReceived = await PrefsUtils.getString(PrefKeys.fcmReceived);
      debugPrint("fcmReceived (HomeScreen) $fcmReceived");
      if (fcmReceived == "true") {
        if(mounted){
          await context.read<ActiveViewProvider>().resetCurrentPage();
          context.read<ActiveViewProvider>().userDataList.clear();
          _getAllNotification();
          PrefsUtils.setString(PrefKeys.fcmReceived, "false");
        }
      }

    } else if (state == AppLifecycleState.paused) {
      debugPrint("App is in background!(ActiveView) AppLifecycleState.paused");
    }
  }

  @override
  void dispose() {
    NotificationCenter().unsubscribe(NotificationsString.notifyAlertScreen);
    debugPrint("dispose() (ActiveView)");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  child: Row(
                    children: [
                      if(context.watch<ActiveViewProvider>().userDataList.isNotEmpty)...[
                        Expanded(
                          child: GestureDetector(
                            onTap: () async {
                              context.read<ActiveViewProvider>().resetCurrentPage();
                              await context.read<ActiveViewProvider>().sortNewestOldest();
                              _getAllNotification();
                            },
                            child: Row(
                              children: [
                                Text(
                                  context.read<ActiveViewProvider>().isNewest ? ActiveViewString.newest : ActiveViewString.oldest,
                                  style: AppTextStyle.smallTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context)
                                        ? 14.0
                                        : 12.0,
                                    fontWeight: FontWeight.w600,
                                  ), // Adjust text style if needed
                                ),
                                Icon(
                                  context.watch<ActiveViewProvider>().isNewest
                                      ? CupertinoIcons.sort_up
                                      : CupertinoIcons.sort_down,
                                  size: 20, // Ensure both icons are the same size
                                ),
                              ],
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topRight,
                          child: GestureDetector(
                            onTap: () {
                              _markAllReadNotification();
                            },
                            child: Text(
                              '${ActiveViewString.clearAll}',
                              style: AppTextStyle.smallTextStyle.copyWith(
                                  fontSize:
                                  isBigScreenResolution(context) ? 14.0 : 12.0,
                                  fontWeight: FontWeight.w600,
                                  color: ColorConstant.clearAllColor),
                            ),
                          ),
                        )
                      ],
                    ],
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 15, left: 5, right: 5),
                    // child: ListView.builder(
                    //   physics: AlwaysScrollableScrollPhysics(),
                    //   itemCount: notifications.length,
                    //   itemBuilder: (context, index) {
                    //     final item = notifications[index];
                    //     return _rowItemView(item);
                    //   },
                    // ),
                    child: listUserNotifcation(),
                  ),
                )
              ],
            ),
    );
  }

  Consumer listUserNotifcation() {
    return Consumer<ActiveViewProvider>(
      builder: (context, data, child) {
        return RefreshIndicator(
          color: ColorConstant.colorBlueDark,
          backgroundColor: ColorConstant.colorThemeWhite,
          onRefresh: () async {
            try {
              await data.resetSorting();
              await data.resetCurrentPage();
              await data.getActiveAllNotification();
              await context.read<NotificationProvider>().getUnReadCount();
            }catch (error) {
              navigatorKey.currentState!.overlay!.context
                  .read<AlertsProvider>()
                  .setNewAlert(
                alertData: AlertModel(
                  alertType: AlertType.alertDialog,
                  title: ConstantString.error,
                  message: error.toString(),
                ),
              );
            }
          },
          child: data.userDataList.isNotEmpty
              ? Container(
            color: ColorConstant.colorThemeWhite,
            child: ListView.builder(
              controller: _scrollController,
              physics: AlwaysScrollableScrollPhysics(),
              itemCount: data.userDataList.length + (data.isLoading ? 1 : 0),
              itemBuilder: (item, index) {
                if(index == data.userDataList.length){
                  debugPrint(': index == list length $index ${data.userDataList.length}:');
                  return Container(
                    padding : EdgeInsets.symmetric(vertical: isBigScreenResolution(context)?10:8),
                    child: Center(
                      child: CircularProgressIndicator(color: ColorConstant.colorBlueDark,)
                      ,)
                    ,);
                }
                return _rowItemView(data.userDataList[index]);
              },
            ),
          )
              : dataNotFound(),
        );
      },
    );
  }

  _rowItemView(UserNotificationData userData) {

    ColorPair colorPair = context.read<ActiveViewProvider>().calculateRowBackgroundColor(userData);
    Color fontColor = colorPair.fontColor;
    Color backgroundColor = colorPair.backgroundColor;

    return GestureDetector(
      onTap: () async{
        context.read<ActiveViewProvider>().selectedNotificationId = userData.id!;
        if(userData.data!.tableId != null || userData.data!.cartId != null){
          await _getCart(userData.data!.cartId!);
          final discountCartData = context.read<DiscountProvider>().cartData;
          // Custom discount
          if (discountCartData != null &&
              discountCartData.cartCustomDiscount != null &&
              discountCartData.cartCustomDiscount!.isNotEmpty &&
              userData.data!.discountType == NotificationsString.customDiscountType) {

            final cartData = context.read<CartViewProvider>().cartData;
            if(userData.data!.discountId == discountCartData.cartCustomDiscount!.first.id
                && discountCartData.cartCustomDiscount!.first.approved == false){
              context.read<NotificationProvider>().setShowApproveActionButton(true);
            } else {
              context.read<NotificationProvider>().setShowApproveActionButton(false);
            }

            context.read<ActiveViewProvider>().selectedNotificationData = userData.data;
            // Cart discount
            if (PromotionApplyOn.cart.name == cartData!.cartCustomDiscount!.first.applyOn) {
              debugPrint("Cart discount");
              navigateToNotifCartDiscountDetailsScreen(context,userData);
            } else {
              // Item discount
              debugPrint("Item discount");
              navigateToNotifItemDiscountDetailsScreen(context,userData);
            }
          }
          // Preset discount
          else if (discountCartData != null && discountCartData.cartDiscount != null
              && userData.data!.discountType == NotificationsString.presetDiscountType){
            if(userData.data!.discountId == discountCartData.cartDiscount!.id
              && discountCartData.cartDiscount!.approved! == false){
              context.read<NotificationProvider>().setShowApproveActionButton(true);
            } else {
              context.read<NotificationProvider>().setShowApproveActionButton(false);
            }
            context.read<ActiveViewProvider>().selectedNotificationData = userData.data;

            final cartData = context.read<CartViewProvider>().cartData;
            // Cart discount
            if (PromotionApplyOn.cart.name == cartData!.cartDiscount!.applyOn) {
              debugPrint("Cart discount");
              navigateToNotifCartDiscountDetailsScreen(context,userData);
            } else {
              // Item discount
              debugPrint("Item discount");
              navigateToNotifItemDiscountDetailsScreen(context,userData);
            }
          }else{
            debugPrint("No discount");
            navigateToNotifCartDiscountDetailsScreen(context,userData);
            context.read<NotificationProvider>().setShowApproveActionButton(false);
          }
        }else{
          context.read<NotificationProvider>().setShowApproveActionButton(false);
          debugPrint("Not table data found userData.data!.tableId:${userData.data!.tableId}");
        }



      },
      child: Container(
        padding: EdgeInsets.all(8.0),
        margin: EdgeInsets.symmetric(vertical: 5.0, horizontal: 10.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: fontColor, // Border color
            width: 0.1, // Border width
          ),
          borderRadius: BorderRadius.circular(8.0), // Rounded corners
          color: backgroundColor, // Background color
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    border: Border.all(color: fontColor, width: 2),
                    borderRadius: BorderRadius.circular(25), // Rounded edges
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if(userData.data!.tableLabel != null && userData.data!.tableLabel!.isNotEmpty)...[
                        Text(
                          "${userData.data?.tableLabel??"n/a"}",
                          style: _fontStyle(textColor: fontColor),
                        ),
                      ]else...[
                        Text(
                          "${userData.data?.cartId??"n/a"}",
                          style: _fontStyle(textColor: fontColor),
                        ),
                      ],
                      // SizedBox(width: 8),
                      // Container(
                      //   height: 16,
                      //   width: 1.5,
                      //   color: ColorConstant.activeFontColor, // Divider color
                      // ),
                      // SizedBox(width: 8),
                      // Text(
                      //   "02:30",
                      //   style: _fontStyle(textColor: fontColor),
                      // ),
                    ],
                  ),
                ),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        Icons.access_time,
                        color: fontColor,
                        size: 20,
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      if(userData.data!.topicKey == NotificationsString.tableActive
                          || userData.data!.topicKey == NotificationsString.discountApproval)...[
                        Text(
                          userData.createdAt != null?DateTimeUtils.timeAgo(userData.createdAt!):'n/a',
                          style: _fontStyle(textColor: fontColor),
                        ),
                      ]else if(userData.data!.topicKey == NotificationsString.serverRequest
                          || userData.data!.topicKey == NotificationsString.bills)...[
                        Text(
                          userData.data!.dateTime != null?DateTimeUtils.timeAgo(userData.data!.dateTime!):DateTimeUtils.timeAgo(userData.createdAt!),
                          style: _fontStyle(textColor: fontColor),
                        ),
                      ]else...[
                        Text(DateTimeUtils.timeAgo(DateTime.now().toUtc().toString()),
                          style: _fontStyle(textColor: fontColor),
                        )
                      ],
                      // end condition
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 20,
            ),
            Text(
              userData.body!,
              style: _fontStyle(textColor: fontColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget dataNotFound() {
    return Stack(children: [
      ListView(),
      Center(
        child: Text(
          ActiveViewString.emptyNotification,
          style: AppTextStyle.mediumTextStyle.copyWith(
            color: ColorConstant.colorBlueLight_50,
            fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
            letterSpacing: 0.3,
          ),
        ),
      ),
    ]);
  }

  _fontStyle({required Color textColor}) {
    return AppTextStyle.smallTextStyle.copyWith(
      fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
      fontWeight: FontWeight.w600,
      color: textColor,
    );
  }

  /// load more data
  void _loadMore() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent && !context.read<ActiveViewProvider>().isLoading) {
      context.read<ActiveViewProvider>().setCurrentActiveViewPage();
    }
  }

  /// get all notification
  _getAllNotification() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<ActiveViewProvider>().getActiveAllNotification();
        await context.read<NotificationProvider>().getUnReadCount();
        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }


  /// mark as read notifications
  _markAllReadNotification() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<ActiveViewProvider>().markAllReadNotification();
        context.read<ActiveViewProvider>().resetCurrentPage();
        context.read<ActiveViewProvider>().userDataList.clear();
        context.read<NotificationProvider>().getUnReadCount();

        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  Future<void> _getCart(int cartId) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        await context.read<CartViewProvider>().getCart(id: cartId);
        // Set order after successfully loading cart data
        await context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);

        if (!mounted) return;
        hideLoaderDialog(context);


        // WidgetsBinding.instance.addPostFrameCallback((_) {
        //   if (context.read<DiscountProvider>().cartData != null &&
        //       context.read<DiscountProvider>().cartData!.cartCustomDiscount != null &&
        //       context.read<DiscountProvider>().cartData!.cartCustomDiscount!.isNotEmpty) {
        //
        //       final cartData = context.read<CartViewProvider>().cartData;
        //       // Cart discount
        //       if (PromotionApplyOn.product.name == cartData!.cartCustomDiscount!.first.applyOn) {
        //
        //       } else {
        //         // Item discount
        //
        //       }
        //
        //   }
        // });
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
          navigatorKey.currentState!.overlay!.context,
          error,
        );
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ${error.toString()}');
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ${error.toString()}');
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ${error.toString()}');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

}
