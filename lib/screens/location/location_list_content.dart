import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/utils/pref_utils.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/location/location_model.dart';
import '../../my_app.dart';
import '../../providers/auth/auth_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';

class LocationListContent extends StatefulWidget {

  const LocationListContent({super.key});

  @override
  State<LocationListContent> createState() => _LocationListContentState();
}

class _LocationListContentState extends State<LocationListContent> {

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width / 2,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 10.0),
            child: Text(
              BusinessLocationString.selectLocation,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 20.0 : 18.0,
              ),
            ),
          ),
          _divider,
          Flexible(child: _locationListView()),
        ],
      ),
    );
  }

  /// Location list view
  Widget _locationListView() {
    return Consumer<AuthProvider>(
      builder: (context, data, child) {
        return ListView.separated(
          shrinkWrap: true,
          itemCount: data.businessLocations.length,
          padding: EdgeInsets.zero,
          itemBuilder: (BuildContext context, index) {
            return _locationListItem(data.businessLocations[index], index);
          },
          separatorBuilder: (BuildContext context, int index) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: isBigScreenResolution(context) ? 10 : 8),
              child: _divider,
            );
          },
        );
      },
    );
  }

  /// Location list item
  Widget _locationListItem(LocationModel businessLocation, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        context
            .read<AuthProvider>()
            .setSelectedLocation(businessLocation);
        _callRefreshToken();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 16 : 14,
            horizontal: isBigScreenResolution(context) ? 20 : 18),
        child: Text(
          businessLocation.name ?? "",
          style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 16: 14,
          ),
          textAlign: TextAlign.start,
        ),
      ),
    );
  }

  /// horizontal divider
  Widget get _divider =>  const Divider(
    color: ColorConstant.colorBlueLight_16,
    height: 1,
  );

  /// call refresh token api
  void _callRefreshToken() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);

      try {
        await context.read<AuthProvider>().refreshToken();
        await context.read<NotificationProvider>().initFirebase();
        await context.read<NotificationProvider>().getUnReadCount();

        // todo: remove this code
        debugPrint(':: get floors ----::');
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getFloorWithTables();
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getCartList();

        if (!mounted) return;
        hideLoaderDialog(context);
        Navigator.of(context).pop();
        await context.read<NotificationProvider>().requestPermissions();
        // navigateToDineInTakeoutScreen(context);

      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error :$error');
        if (!mounted) return;
        hideLoaderDialog(context);
        // errorAlertDialog(error.toString());
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }
}
