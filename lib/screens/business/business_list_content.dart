import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/business/user_business_model.dart';
import '../../models/location/location_model.dart';
import '../../my_app.dart';
import '../../providers/alerts/alerts_provider.dart';
import '../../providers/auth/auth_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import '../../utils/pref_utils.dart';
import '../location/location_list_content.dart';

class BusinessListContent extends StatefulWidget{
  const BusinessListContent({super.key});

  @override
  State<BusinessListContent> createState() => _BusinessListContentState();
}

class _BusinessListContentState extends State<BusinessListContent> {

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width / 2,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(vertical: 10.0),
            child: Text(
              BusinessLocationString.selectBusiness,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 20.0 : 18.0,
              ),
            ),
          ),
          _divider,
          Flexible(child: _businessListView()),
        ],
      ),
    );
  }

  /// Business list view
  Widget _businessListView() {
    return Consumer<AuthProvider>(
      builder: (context, data, child) {
        return ListView.separated(
          shrinkWrap: true,
          itemCount: data.userBusinesses.length,
          padding: EdgeInsets.zero,
          itemBuilder: (BuildContext context, index) {
            return _businessListItem(data.userBusinesses[index], index);
          },
          separatorBuilder: (BuildContext context, int index) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: isBigScreenResolution(context) ? 10 : 8),
              child: _divider,
            );
          },
        );
      },
    );
  }

  /// Business list item
  Widget _businessListItem(UserBusinessModel userBusiness, int index){
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap:!userBusiness.status! ? null : () {
        context
            .read<AuthProvider>()
            .setSelectedBusiness(userBusiness);
        _callRefreshToken();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 16 : 14,
            horizontal: isBigScreenResolution(context) ? 20 : 18),
        child: Text(
          userBusiness.name ?? "",
          style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 16: 14,
            color: userBusiness.status!
                ? ColorConstant.colorBlueDark :  ColorConstant.colorBlueLight_32,
          ),
          textAlign: TextAlign.start,
        ),
      ),
    );
  }

  /// horizontal divider
  Widget get _divider =>  const Divider(
    color: ColorConstant.colorBlueLight_16,
    height: 1,
  );

  /// call refresh token api
  void _callRefreshToken() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        await context.read<AuthProvider>().refreshToken();

        if (!mounted) return;
        hideLoaderDialog(context);

        _callGetAllLocation();


      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error : $error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// call refresh token api
  void _callRefreshTokenLocation() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);

      try {
        await context.read<AuthProvider>().refreshToken();
        await context.read<NotificationProvider>().initFirebase();
        await context.read<NotificationProvider>().getUnReadCount();

        // todo: remove this code
        debugPrint(':: get floors ----::');
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getFloorWithTables();
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getCartList();

        if (!mounted) return;
        hideLoaderDialog(context);
        await context.read<NotificationProvider>().requestPermissions();
        /// close the business dialog first
        Navigator.pop(context);

      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error :$error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error.toString());
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }


  /// get all location list
  Future<void> _callGetAllLocation() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      try {
        if (!mounted) return;
        List<LocationModel> locations = await context.read<AuthProvider>().getBusinessLocation();
        if (!mounted) return;




        if(context.read<AuthProvider>().businessLocations.length == 1){
          context
              .read<AuthProvider>()
              .setSelectedLocation(context.read<AuthProvider>().businessLocations.first);
          _callRefreshTokenLocation();
        }else{
          /// close the business dialog first
          Navigator.pop(context);
          if(locations.isEmpty) {
            PrefsUtils.removeKey(PrefKeys.authToken);
            context.read<AuthProvider>().reset();
            navigateToLoginAutoLogout(context);
            _showSnackBar(message: AuthString.errorLocationNotFound,duration: 3);
          }else if (locations.isNotEmpty) {
            /// opens the location dialog
            showContentDialog(context, const LocationListContent());
          }
        }


      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      }catch (error) {
        debugPrint('Getting error :$error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error.toString());
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  _showSnackBar({required String message, int duration = 3,}) {
    navigatorKey.currentState!.overlay!.context
        .read<AlertsProvider>()
        .setNewAlert(
      alertData: AlertModel(
        alertType: AlertType.snackBar,
        title: ConstantString.success,
        message: message,
        duration: duration,
      ),
    );
  }

}