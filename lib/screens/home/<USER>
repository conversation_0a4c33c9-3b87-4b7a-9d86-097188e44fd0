import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gr8tables_server_manager/custom_widgets/nav_menu.dart';
import 'package:gr8tables_server_manager/models/notification/activeview_provider.dart';
import 'package:gr8tables_server_manager/providers/auth/auth_provider.dart';
import 'package:gr8tables_server_manager/providers/homescreen/home_screen_provider.dart';
import 'package:gr8tables_server_manager/services/graph_ql/graph_ql_service.dart';
import 'package:gr8tables_server_manager/utils/app_utils.dart';
import 'package:provider/provider.dart';
import 'package:pubnub/pubnub.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../custom_widgets/bottom_sheet_widget.dart';
import '../../custom_widgets/logout/logout_dialog_overlay.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../helpers/messaging_service.dart';
import '../../helpers/notification_navigation.dart';
import '../../models/location/location_model.dart';
import '../../models/pubnub/pubnub_order_emit.dart';
import '../../my_app.dart';
import '../../providers/alerts/alerts_provider.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/common/shareable_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/homescreen/bottom_sheet_provider.dart';
import '../../providers/homescreen/bottomview_provider.dart';
import '../../providers/menuview/menu_view_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../providers/package_info/package_info_provider.dart';
import '../../providers/payment/payment_provider.dart';
import '../../providers/pubnub/pubnub_provider.dart';
import '../../pubnub/pubnub_constants.dart';
import '../../pubnub/pubnub_service.dart';
import '../../utils/app_routes.dart';
import '../../utils/pref_utils.dart';
import '../business/business_list_content.dart';
import '../location/location_list_content.dart';


class HomeScreen extends StatefulWidget {
  final String selectedPage;
  const HomeScreen({super.key, this.selectedPage = NavigationString.homeView});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver{
  Subscription? _orderSubscription;
  final _messagingService = MessagingService(); // Instance of MessagingService for handling notifications
  @override
  void initState() {
    debugPrint("HomeScreen initState()");

    WidgetsBinding.instance.addPostFrameCallback((_) async{

      var businessLocationData = context.read<AuthProvider>();
      if (!businessLocationData.isBusinessSelected()) {
        _callGetAllBusinessList();
      } else if (businessLocationData.isBusinessSelected() && !businessLocationData.isLocationSelected()) {
        _callGetAllLocation();
        _pubNubSubScribe();
        // FCM notification
        _messagingService.init(context); // Initialize MessagingService to handle notifications
        context.read<NotificationProvider>().getUnReadCount();
      }else {
         if(businessLocationData.isBusinessSelected() && businessLocationData.isLocationSelected()) {
          // navigateToDineInTakeoutScreen(context);
           _pubNubSubScribe();
           // FCM notification
           _messagingService.init(context); // Initialize MessagingService to handle notifications
           context.read<NotificationProvider>().getUnReadCount();
         }

      }

      //context.read<HomeScreenProvider>().updateSelectedPage(widget.selectedPage);
      context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.home);

      await NotificationNavigation().navigateToPage(context: context);
      String? fcmReceived = await PrefsUtils.getString(PrefKeys.fcmReceived);
      debugPrint("fcmReceived (HomeScreen) init(): $fcmReceived");
      if (fcmReceived !=null && fcmReceived == "true") {
        PrefsUtils.setString(PrefKeys.fcmReceived, "false");
        context.read<BottomSheetProvider>().setDialogForHomePage(false);
        context.read<HomeScreenProvider>().updateSelectedIndex(3);
      }

    });

    FirebaseMessaging.instance.onTokenRefresh.listen((String _token) {
      debugPrint("FCM Token re-generated: $_token");
      PrefsUtils.setString(PrefKeys.fcmToken, _token);
      context.read<NotificationProvider>().registerDevice();
    },);
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Add observer
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async{
    super.didChangeAppLifecycleState(state);
    if (!mounted) return; // Prevent context usage after unmount
    if (state == AppLifecycleState.resumed) {
      debugPrint("App is in foreground!(HomeScreen)");

      String? fcmReceived = await PrefsUtils.getString(PrefKeys.fcmReceived);
      debugPrint("fcmReceived (HomeScreen) $fcmReceived");
      if (fcmReceived == "true") {
        context.read<BottomSheetProvider>().setDialogForHomePage(false);
        context.read<HomeScreenProvider>().updateSelectedIndex(3);
      }
    } else if (state == AppLifecycleState.paused) {
      debugPrint("App is in background!(HomeScreen) AppLifecycleState.paused");
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          // Custom logic for managing backstack
          final currentIndex = context.read<HomeScreenProvider>().currentIndex;

          // If we're not on the first tab, navigate back to the first tab
          if (currentIndex != 0) {
            context.read<HomeScreenProvider>().manageNavigationStack();
          } else {
            // Exit the app if already on the first tab
            SystemNavigator.pop();
          }
        }
      },
      child: SafeArea(
        child: Scaffold(
          appBar: CustomAppBar(
            context: context,
            appBarTitleText: context.read<HomeScreenProvider>().appBarTitle,
          ),
          body: Stack(
              children: [
                // Responsible for displaying the contents of each navigation tab
                context.watch<HomeScreenProvider>().pageWidget,
                // Bottom sheet menu
                if(context.watch<BottomSheetProvider>().showDialogHome)...[
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: _handleBottomSheetMenu(),
                  ),
                ],
              ],
          ),
          bottomNavigationBar: _handleNavigationBar(),
        ),
      ),
    );
  }

  /// pubnub subscribe
  _pubNubSubScribe(){
    PubNubService().initPubnub().then((value) async{
      String locationId = LocationModel.fromJson(PrefsUtils.getObject(PrefKeys.location)).id!;

      if(null  == _orderSubscription) {
        if(mounted) {
          _orderSubscription = await PubNubService().subScribeToOrderChannel(
              channelName: '${PubNubConstants.CHANNEL_NAME_PREFIX}${PubNubConstants.CHANNEL_NAME_PREFIX_ORDER}-$locationId',context: context);
        }
      }
    }
    );

    context.read<PubNubProvider>().reOpenStream();
    _pubnubEmitListener();
  }

  /// Get all business list
  void _callGetAllBusinessList() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        await context.read<AuthProvider>().getUserBusinesses();
        if (!mounted) return;
        hideLoaderDialog(context);
        if(context.read<AuthProvider>().userBusinesses.length == 1){
          context
              .read<AuthProvider>()
              .setSelectedBusiness(context.read<AuthProvider>().userBusinesses.first);
          _callRefreshTokenForBusiness();
        }else{
          showContentDialog(context, const BusinessListContent());
        }
        if(context.read<AuthProvider>().userBusinesses.isEmpty) {
          PrefsUtils.removeKey(PrefKeys.authToken);
          context.read<AuthProvider>().reset();
          context.read<NotificationProvider>().removeDevice();
          navigateToLoginAutoLogout(context);
          _showSnackBar(message: AuthString.errorBusinessNotFound,duration: 3);
        }
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error : $error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error.toString());
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// get all location list
  void _callGetAllLocation() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        await context.read<AuthProvider>().getBusinessLocation();
        if (!mounted) return;
        hideLoaderDialog(context);
        if(context.read<AuthProvider>().businessLocations.length == 1){
          context
              .read<AuthProvider>()
              .setSelectedLocation(context.read<AuthProvider>().businessLocations.first);
          _callRefreshTokenLocation();
        }else{
          showContentDialog(context, const LocationListContent());
          if(context.read<AuthProvider>().businessLocations.isEmpty) {
            PrefsUtils.removeKey(PrefKeys.authToken);
            context.read<AuthProvider>().reset();
            navigateToLoginAutoLogout(context);
            _showSnackBar(message: AuthString.errorLocationNotFound,duration: 3);
          }
        }
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      }catch (error) {
        debugPrint('Getting error :$error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error.toString());
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// call refresh token api
  void _callRefreshTokenForBusiness() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        await context.read<AuthProvider>().refreshToken();

        if (!mounted) return;
        hideLoaderDialog(context);

        _callGetAllLocation();

      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error : $error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// call refresh token api
  void _callRefreshTokenLocation() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);

      try {
        await context.read<AuthProvider>().refreshToken();
        await context.read<NotificationProvider>().initFirebase();
        await context.read<NotificationProvider>().getUnReadCount();

        // todo: remove this code
        debugPrint(':: get floors ----::');
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getFloorWithTables();
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getCartList();

        if (!mounted) return;
        hideLoaderDialog(context);
        await context.read<NotificationProvider>().requestPermissions();
        // navigateToDineInTakeoutScreen(context);

      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error :$error');
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error.toString());
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }


  _showSnackBar({required String message, int duration = 3,}) {
    navigatorKey.currentState!.overlay!.context
        .read<AlertsProvider>()
        .setNewAlert(
      alertData: AlertModel(
        alertType: AlertType.snackBar,
        title: ConstantString.success,
        message: message,
        duration: duration,
      ),
    );
  }

  /// Bottom navigation
  _handleNavigationBar(){
      return NavigationMenu(onMenuSelected: (selectedMenu) {
          switch (selectedMenu) {
            case BottomBarNavigationString.order:
              context.read<BottomSheetProvider>().setDialogForHomePage(false);
              context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.order);
              break;
            case BottomBarNavigationString.more:
              debugPrint('home page selected $selectedMenu');
              context.read<BottomSheetProvider>().setDialogForHomePage(!context.read<BottomSheetProvider>().showDialogHome) ;
              break;
            case BottomBarNavigationString.history:
              context.read<BottomSheetProvider>().setDialogForHomePage(false);
              context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.history);
              break;
            case BottomBarNavigationString.alerts:
              debugPrint('alert page selected $selectedMenu');
              context.read<NotificationProvider>().getUnReadCount();
              context.read<BottomSheetProvider>().setDialogForHomePage(false);
              context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.alerts);
              break;
            default:
              context.read<BottomSheetProvider>().setDialogForHomePage(false);
              context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.order);
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }
          }
        },
      );
  }

  /// handle bottom sheet menu
  _handleBottomSheetMenu(){
    final Map<String, bool> menuItems = {
      BottomSheetString.logout: true,
    };

    return BottomSheetContent(menuItems: menuItems, onMenuItemSelected: (selectedMenu) {
      debugPrint("Selected Menu: $selectedMenu");
      context.read<BottomSheetProvider>().setDialogForDinePage(false);
      Future.delayed(const Duration(milliseconds: 100), () {
        showGeneralDialog(
            context: context,
            barrierDismissible: false,
            barrierColor: Colors.black.withOpacity(0.5),
            transitionDuration: const Duration(milliseconds: 200),
            pageBuilder: (BuildContext buildContext,
                Animation animation, Animation secondaryAnimation) {
              return LogOutDialogOverlay(
                onPositiveButtonClick: ()  async{
                  // /// in case of user log out from app
                  await context.read<NotificationProvider>().removeDevice();
                  _clearAll();
                  navigateToLogin(context);
                },
                onNegativeButtonClick: () {
                  Navigator.of(context).pop();
                },
              );
            });
      });

      },
    );

  }

  /// pubnub emit listener
  _pubnubEmitListener(){

    context.read<PubNubProvider>().dataStream.listen((event) async {
      debugPrint(':: stream listener order $event ::');
      if (event.isNotEmpty) {
        if (event.containsKey(PubNubConstants.EVENT_ORDER_PLACED)) {
          debugPrint('::  got order_placed emit serving ::');
          PubNubOrderPayload? payload =
          event[PubNubConstants.EVENT_ORDER_PLACED] as PubNubOrderPayload;
          if ((payload.cartStatus == OrderStatus.placed ) &&
              payload.platform!.toLowerCase() == ConstantString.platformWeb) {
            debugPrint(
                'order status ::: ${payload
                    .cartStatus}');

            /// try to refresh takeout tab
            _handlePickUpDeliveryOrderEmit();

          }
          else if(payload.cartStatus == OrderStatus.processed &&
              payload.platform!.toLowerCase() == ConstantString.platformWeb){
            debugPrint(
                'order status ::: ${payload
                    .cartStatus}');
            if(payload.orderReceiveMethod!.toLowerCase() == OrderReceiveMethod.dineIn){
              _handleDineInOrderEmit();
            }
          }
        }
        else if (event.containsKey(PubNubConstants.EVENT_ORDER_CREATED)) {
          debugPrint(':: dine in order_created ::');
          PubNubOrderPayload? payload =
          event[PubNubConstants.EVENT_ORDER_CREATED] as PubNubOrderPayload;

          switch(payload.orderReceiveMethod!.toLowerCase()){
            case OrderReceiveMethod.pickup:
            case OrderReceiveMethod.delivery:
              _handlePickUpDeliveryOrderEmit();
              break;
            case OrderReceiveMethod.dineIn:
              _handleDineInOrderEmit();
              break;
          }

        }
        else if (event.containsKey(PubNubConstants.EVENT_TABLE_ACTIVATED)) {
          debugPrint(':: table_activated ::');
          PubNubOrderPayload? payload =
          event[PubNubConstants.EVENT_TABLE_ACTIVATED] as PubNubOrderPayload;

          switch(payload.orderReceiveMethod!.toLowerCase()){
            case OrderReceiveMethod.dineIn:
              _handleDineInTableEmit();
              break;
          }

        }
        // else if (event.containsKey(PubNubConstants.EVENT_USER_JOINED_TABLE)) {
        //   debugPrint(':: user_joined_table ::');
        //   PubNubOrderPayload? payload =
        //   event[PubNubConstants.EVENT_USER_JOINED_TABLE] as PubNubOrderPayload;
        //
        //   switch(payload.orderReceiveMethod!.toLowerCase()){
        //     case OrderReceiveMethod.pickup:
        //     case OrderReceiveMethod.delivery:
        //       _handlePickUpDeliveryOrderEmit();
        //       break;
        //     case OrderReceiveMethod.dineIn:
        //       _handleDineInOrderEmit();
        //       break;
        //   }
        //
        // }
        else if (event.containsKey(PubNubConstants.EVENT_ORDER_PROCESSED)) {
          debugPrint(':: order_proceed (home) page::');
          PubNubOrderPayload? payload =
          event[PubNubConstants.EVENT_ORDER_PROCESSED] as PubNubOrderPayload;

          switch(payload.orderReceiveMethod!.toLowerCase()){
            case OrderReceiveMethod.dineIn:
              _handleDineInTableEmit();
              context.read<CartViewProvider>().resetCartUserSelection();
              break;
          }
        }
        else if (event.containsKey(PubNubConstants.EVENT_CALL_SERVER)) {
          debugPrint(':: call_server ::');
          PubNubOrderEmit? pubNubOrderEmit =
          event[PubNubConstants.EVENT_CALL_SERVER] as PubNubOrderEmit;

          showSeverCallDialog(message: pubNubOrderEmit.title!);
        }
      }
    });
  }

  /// handle pickup delivery order emit
  void _handlePickUpDeliveryOrderEmit() async{
    var data = context.read<DineInTakeoutProvider>();
    data.resetCurrentPage();
    await data.getCartList();
  }

  /// handle dine in order emit
  void _handleDineInOrderEmit() async{
    debugPrint(':: Handle homescreen in cart ::');
    var data = context.read<DineInTakeoutProvider>();
    await data.getFloorWithTables();
  }

  /// handle dine in table emit
  void _handleDineInTableEmit() async{
    if(navigatorKey.currentContext != null) {
      debugPrint(':: Handle homescreen in cart ::');
      var data = navigatorKey.currentContext?.read<DineInTakeoutProvider>();
      await data!.getFloorWithTables();
    }

  }

  /// unsubscribe from pubnub channels
  _unsubscribeFromDineInTakeOutChannel(){
    if(null !=_orderSubscription ){
      if(!_orderSubscription!.isCancelled)  {
        _orderSubscription?.cancel().then((value) {
          debugPrint(':: Subscription cancelled ::');
          _orderSubscription= null;
          debugPrint(' DineInTakeoutView subscription list :: ${_orderSubscription?.channels}');
        });
      }
      return;
    }
  }

  _clearAll(){


    context.read<AuthProvider>().reset();
    context.read<HomeScreenProvider>().reset();
    context.read<CartViewProvider>().reset();
    context.read<ShareableProvider>().reset();
    context.read<DineInTakeoutProvider>().reset();
    context.read<BottomSheetProvider>().reset();
    context.read<BottomViewProvider>().reset();
    context.read<MenuViewProvider>().reset();
    context.read<PackageInfoProvider>().reset();
    context.read<PubNubProvider>().reset();
    context.read<PaymentProvider>().reset();
    context.read<NotificationProvider>().reset();
    context.read<ActiveViewProvider>().reset();
    GraphQLService().reset();
    PrefsUtils.removeKey(PrefKeys.authToken);
    // Clear shared preferences
    PrefsUtils.clear();
  }

  @override
  void dispose() {
    debugPrint("HomeScreen dispose()");
    PrefsUtils.setString(PrefKeys.fcmReceived, "false");
    _unsubscribeFromDineInTakeOutChannel();
    super.dispose();
  }
}
