import 'package:flutter/material.dart';

class HomeScreenContentView extends StatefulWidget {
  const HomeScreenContentView({super.key});

  @override
  State<HomeScreenContentView> createState() => _HomeScreenContentViewState();
}

class _HomeScreenContentViewState extends State<HomeScreenContentView> {
  @override
  void initState() {
    debugPrint("HomeScreenContentView initState()");
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        child: Text("Home Screen Content View"),
      ),
    );
  }

  @override
  void dispose() {
    debugPrint("HomeScreenContentView dispose()");
    super.dispose();
  }
}
