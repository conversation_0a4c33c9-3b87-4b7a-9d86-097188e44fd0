import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/screens/history/frequently_order_view.dart';
import 'package:gr8tables_server_manager/screens/history/past_order_view.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../my_app.dart';
import '../../providers/customer/customer_order_history_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../utils/app_utils.dart';


class CustomerOrderHistoryView extends StatefulWidget {
  String userId = '';
  String userName = '';
  CustomerOrderHistoryView({super.key,this.userId = '', this.userName= ''});

  @override
  State<CustomerOrderHistoryView> createState() => _CustomerOrderHistoryViewState();
}

class _CustomerOrderHistoryViewState extends State<CustomerOrderHistoryView> with SingleTickerProviderStateMixin{
  late TabController _tabController;

  @override
  void initState() {
    debugPrint("CustomerOrderHistoryView initState()");
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      context.read<CustomerOrderHistoryProvider>().reset();
      context.read<CustomerOrderHistoryProvider>().setCustomerId(widget.userId);
      _getCustomerFrequentOrders();
      _getCustomerPastOrders();
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child:Scaffold(
        appBar: CustomAppBar(
          context: context,
          appBarTitleText: toTitleCase(widget.userName),
          backButtonEnabled: true,
        ),
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: isBigScreenResolution(context) ? 8 : 6,
                right: isBigScreenResolution(context) ? 8 : 6,
                top: isBigScreenResolution(context) ? 8 : 6,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _tabBar(),
                  _height15,
                  Expanded(child: _tabContainer()),
                ],
              ),
            ),

          ],
        ),
      ),
    );
  }

  /// tab bar
  Widget _tabBar(){
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      tabAlignment: TabAlignment.center,
      physics: const NeverScrollableScrollPhysics(),
      dividerColor: Colors.transparent,
      indicatorColor: ColorConstant.colorBlueDark,
      indicatorSize: TabBarIndicatorSize.label,
      labelStyle: AppTextStyle.smallTextStyle.copyWith(
        fontSize: 14.0,
        fontWeight: FontWeight.w600,
        color: ColorConstant.colorBlueDark,
      ),
      unselectedLabelStyle: AppTextStyle.smallTextStyle.copyWith(
        fontSize: 14.0,
        fontWeight: FontWeight.w500,
        color: ColorConstant.colorBlueLight_50,
      ),
      tabs: const [
        Tab(text: CustomerOrderHistoryString.tabFrequentlyOrder, ),
        Tab(text: CustomerOrderHistoryString.tabPastOrdered,),
      ],
    );
  }

  /// tab bar container
  Widget _tabContainer(){
    return Consumer<DineInTakeoutProvider>(
      builder: (context, data, child) {
        return TabBarView(
          controller: _tabController,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            RefreshIndicator(
              onRefresh: () async {
                context.read<CustomerOrderHistoryProvider>().reset();
                context.read<CustomerOrderHistoryProvider>().setCustomerId(widget.userId);
                _getCustomerFrequentOrders();
                _getCustomerPastOrders();
              },
              child: FrequentlyOrderedView(),
            ),
            RefreshIndicator(
              onRefresh: () async {
                context.read<CustomerOrderHistoryProvider>().reset();
                context.read<CustomerOrderHistoryProvider>().setCustomerId(widget.userId);
                _getCustomerFrequentOrders();
                _getCustomerPastOrders();
              },
              child: PastOrderedView(),
            ),

          ],
        );
      },
    );

  }

  /// horizontal space between past order header text
  Widget get _height15 => SizedBox(
    height: isBigScreenResolution(context) ? 15 : 12,
  );

  /// Get Customer Frequent Orders
  Future<void> _getCustomerFrequentOrders() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context.read<CustomerOrderHistoryProvider>().getCustomerFrequentOrders();

        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }


  /// Get Customer Past Orders
  Future<void> _getCustomerPastOrders() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context.read<CustomerOrderHistoryProvider>().getCustomerPastOrders();

        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

}
