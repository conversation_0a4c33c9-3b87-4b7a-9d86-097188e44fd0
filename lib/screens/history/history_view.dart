import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/utils/app_routes.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/cart/cart_data.dart';
import '../../my_app.dart';
import '../../providers/alerts/alerts_provider.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/historyview/historyview_provider.dart';
import '../../utils/app_utils.dart';
import '../../utils/datetime_utils.dart';


class HistoryView extends StatefulWidget {
  const HistoryView({Key? key}) : super(key: key);

  @override
  State<HistoryView> createState() => _HistoryView();
}

class _HistoryView extends State<HistoryView> {

  late TextEditingController _searchOrderController;

  late ScrollController _scrollController;

  @override
  void initState() {
    _searchOrderController = TextEditingController();
    _scrollController = ScrollController();
    _getHistoryCartList();

    _scrollController.addListener(_loadMore);
    super.initState();
  }

  @override
  void dispose() {
    debugPrint("History dispose()");
    _scrollController.dispose();
    super.dispose();
  }

  /// load more data
  void _loadMore() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent && !context.read<HistoryViewProvider>().isLoading) {
      context.read<HistoryViewProvider>().setCurrentPage(_searchOrderController.text.trim());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorConstant.colorThemeWhite,
      margin: EdgeInsets.only(
        left: isBigScreenResolution(context) ? 12 : 10,
        right: isBigScreenResolution(context) ? 12 : 10,
        top: isBigScreenResolution(context) ? 12 : 10,
      ),
      child: listHistoryOrders(),
    );
  }

  Consumer listHistoryOrders() {
    return Consumer<HistoryViewProvider>(
      builder: (context, data, child) {
        return RefreshIndicator(
          color: ColorConstant.colorBlueDark,
          backgroundColor: ColorConstant.colorThemeWhite,
          onRefresh: () async {
            try {
              _searchOrderController.clear();
              await data.resetSorting();
              await data.resetCurrentPage();
              await data.getHistoryCartOrder(search: _searchOrderController.text.toString());
            }catch (error) {
              navigatorKey.currentState!.overlay!.context
                  .read<AlertsProvider>()
                  .setNewAlert(
                alertData: AlertModel(
                  alertType: AlertType.alertDialog,
                  title: ConstantString.error,
                  message: error.toString(),
                ),
              );
            }
          },
          child: data.cartList.isNotEmpty
              ? Container(
            color: ColorConstant.colorThemeWhite,
            child: ListView.builder(
              controller: _scrollController,
              physics: AlwaysScrollableScrollPhysics(),
              itemCount: data.cartList.length + (data.isLoading ? 1 : 0),
              itemBuilder: (item, index) {
                if(index == data.cartList.length){
                  debugPrint(': index == list length $index ${data.cartList.length}:');
                  return Container(
                    padding : EdgeInsets.symmetric(vertical: isBigScreenResolution(context)?10:8),
                    child: Center(
                      child: CircularProgressIndicator(color: ColorConstant.colorBlueDark,)
                      ,)
                    ,);
                }
                return orderItemsCell(data.cartList[index],
                    orderStatus: data.cartList[index].status!, refundStatus: 0);
              },
            ),
          )
              : orderNotFound(),
        );
      },
    );
  }

  Widget orderItemsCell(CartData cartData,
      {required String orderStatus, int refundStatus=0}) {
    return GestureDetector(
      onTap: () async {

        context.read<HistoryViewProvider>().setCartSelected(cartData);
        navigateToOrderDetailsScreen(context, cartData.id!,cartData.tableId!);

      },
      child: Container(
        padding: EdgeInsets.all(8.0),
        margin: EdgeInsets.symmetric(vertical: 5.0, horizontal: 10.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey, // Border color
            width: 1.0,        // Border width
          ),
          borderRadius: BorderRadius.circular(8.0), // Rounded corners
          color: Colors.white, // Background color
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded( // Ensures the Column takes up available space
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start, // Aligns text to the left
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween, // Proper spacing between items
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              child: cartData.platform!.toLowerCase() == ConstantString.platformWeb
                                  ?_plateFormImageView(AssetImages.onlinePurchase)
                                  :_plateFormImageView(AssetImages.inStore),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              '#${cartData.id}',
                              style: AppTextStyle.smallTextStyle.copyWith(
                                fontSize:  isBigScreenResolution(context) ? 14.0 : 12.0,
                                fontWeight: FontWeight.w600,
                                color: ColorConstant.colorBlueDark,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        DateTimeUtils.utcTimeToConvertLocalTime(
                            cartData.submittedAt ??
                                DateTime.now().toUtc().toString(),formattedString: DateTimeUtils.dd_mm_yyyy_kk_mm_a),
                        style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: 14.0,
                          fontWeight: FontWeight.w600,
                          color: ColorConstant.colorBlueDark,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: isBigScreenResolution(context) ? 5 : 4),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          context.read<CartViewProvider>().getCustomerName(cartData.user,isRemoveNextLine : true),
                          style: AppTextStyle.smallTextStyle.copyWith(
                            fontSize:
                            isBigScreenResolution(context) ? 14.0 : 12.0,
                            color: ColorConstant.colorBlueDark,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.all(isBigScreenResolution(context) ? 6: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              margin: EdgeInsets.only(left: isBigScreenResolution(context) ? 6:5,),
                              child: Image.asset(
                                orderStatus == OrderStatus.refunded
                                    ? AssetImages.refund
                                    : cartData.orderReceiveMethod ==
                                    OrderReceiveMethod.delivery
                                    ? AssetImages.delivery
                                    : cartData.orderReceiveMethod ==
                                    OrderReceiveMethod.dineIn
                                    ? AssetImages.dish_fill
                                    : AssetImages.pickup,
                                color: ColorConstant.colorBlueLight_50,
                                width: 25.0,
                                height: 25.0,
                              ),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: isBigScreenResolution(context) ? 5 : 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween, // Ensures alignment with text above
                    children: [
                      Expanded(
                        child: Container(
                          child: Text(
                            "${_loyaltyPoints(cartData)}",
                            style: AppTextStyle.smallTextStyle.copyWith(
                              color:  orderStatus == OrderStatus.refunded  ? _loyaltyPoints(cartData) == 'N/A'? ColorConstant.colorBlueDark : ColorConstant.refundColor : ColorConstant.colorBlueDark,
                              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                            ),
                          ),
                        ),
                      ),
                      Container(
                        child: Text(
                          NumberFormat.simpleCurrency().format(cartData.total),
                          style: AppTextStyle.smallTextStyle.copyWith(
                            color: orderStatus == OrderStatus.refunded ? ColorConstant.refundColor : ColorConstant.colorBlueDark,
                            fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _plateFormImageView(String image){
    return Image.asset(image,
      width: isBigScreenResolution(context) ?18.0:16.0,
      height: isBigScreenResolution(context) ?18.0:16.0,
      color: ColorConstant.colorBlueDark,);
  }

  Widget orderNotFound() {
    return Stack(children: [
      ListView(),
      Center(
        child: Text(
          OrdersViewString.errorOrderNotFound,
          style: AppTextStyle.mediumTextStyle.copyWith(
            color: ColorConstant.colorBlueLight_50,
            fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
            letterSpacing: 0.3,
          ),
        ),
      ),
    ]);
  }

  /// Calculate the loyalty points
  String _loyaltyPoints(CartData data) {
    num totalPointsUsed =0;
    num totalPointsEarned=0;

    if(data.cartLoyalty !=null){
      totalPointsUsed = totalPointsUsed + data.cartLoyalty!.pointsRedeemed!;
      totalPointsEarned = totalPointsEarned + data.cartLoyalty!.pointsEarned!;
    }
    if(data.cartReward !=null){
      totalPointsUsed = totalPointsUsed + data.cartReward!.points!;
    }
    if(totalPointsUsed == 0 && totalPointsEarned ==0) {
      return 'N/A';
    }

    return '$totalPointsEarned/$totalPointsUsed';

  }

  /// get cart list
  Future<void> _getHistoryCartList({String sort = '', String sortBy= ''}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<HistoryViewProvider>().resetCurrentPage();
        await context.read<HistoryViewProvider>().getHistoryCartOrder(sort: sort,sortBy: sortBy);

        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

}

