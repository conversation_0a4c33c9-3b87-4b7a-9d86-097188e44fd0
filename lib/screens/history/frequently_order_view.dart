import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../models/customer/customer_order/customer_frequent_orders.dart';
import '../../providers/customer/customer_order_history_provider.dart';
import '../../utils/app_utils.dart';

class FrequentlyOrderedView extends StatefulWidget {
  const FrequentlyOrderedView({super.key});

  @override
  State<FrequentlyOrderedView> createState() => _FrequentlyOrderedViewState();
}

class _FrequentlyOrderedViewState extends State<FrequentlyOrderedView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Consumer<CustomerOrderHistoryProvider>(
          builder: (context, data, child) {
            if(data.customerFrequentOrders.isNotEmpty){
              return _frequentOrderList(data);
            }
            if(data.customerFrequentOrders.isEmpty) {
              return Expanded(
                child: Center(
                  child: Text(
                    CustomerOrderHistoryString.noItemYetErrorMessage,
                    style: AppTextStyle.smallTextStyle.copyWith(
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            }
             return Container();
            },
        ),
      ],
    );
  }

  /// frequent order list
  _frequentOrderList(CustomerOrderHistoryProvider data) {
    return Flexible(
      child: ListView.builder(
        itemBuilder: (context, index) {
          return _frequentOrderItem(data.customerFrequentOrders[index], index);
        },
        itemCount: data.customerFrequentOrders.length,
        shrinkWrap: true,
        physics: AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// frequent order item
  Widget _frequentOrderItem(CustomerFrequentOrderData data, int index) {
    if(data.itemData != null){
      return Container(
        color: ColorConstant.colorBlueLight_8,
        margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 12 : 10,),
        padding: EdgeInsets.all(isBigScreenResolution(context) ? 12 : 10,),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            IntrinsicHeight(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Text(
                            '${index + 1}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyle.largeTextStyle.copyWith(
                              fontSize:
                              isBigScreenResolution(context) ? 24.0 : 22.0,
                              color: ColorConstant.colorBlueDark,
                              letterSpacing: 1.50,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: isBigScreenResolution(context) ? 7 : 5,),
                      Row(
                        children: [
                          Text(
                            'x${data.purchasedCount}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyle.smallTextStyle.copyWith(
                              fontSize:
                              isBigScreenResolution(context) ? 14.0 : 12.0,
                              color: ColorConstant.colorBlueDark,
                              letterSpacing: 1.50,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(width: isBigScreenResolution(context) ? 10 : 8,),
                  VerticalDivider(
                    width: 1,
                    color: ColorConstant.colorBlueDark,
                    thickness: 1,
                  ),
                  SizedBox(width: isBigScreenResolution(context) ? 10 : 8,),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () {},
                          child: Text(
                            '${data.itemData!.category!.categoryName ?? ''} - ${data.itemData!.name!}',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyle.largeTextStyle.copyWith(
                              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                              color: ColorConstant.colorBlueDark,
                              letterSpacing: 1.15,
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 8 : 6),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              GestureDetector(
                                onTap: () async {},
                                child: Container(
                                  constraints: BoxConstraints(
                                    minWidth: isBigScreenResolution(context) ? 100 : 95,
                                    maxWidth: isBigScreenResolution(context) ? 100 : 95,
                                    minHeight: isBigScreenResolution(context) ? 35 : 32,
                                    maxHeight: isBigScreenResolution(context) ? 35 : 32,
                                  ),
                                  decoration: BoxDecoration(
                                    color: ColorConstant.colorBlueDark,
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(4),
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      NumberFormat.simpleCurrency().format(
                                          data.itemData!.itemPriceInventory!.price ?? 0),
                                      style: AppTextStyle.mediumTextStyle.copyWith(
                                        color: ColorConstant.colorThemeWhite,
                                        fontSize: isBigScreenResolution(context) ? 14 : 12,
                                        letterSpacing: 1.15,
                                      ),
                                      maxLines: 1,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return Container();
  }

}
