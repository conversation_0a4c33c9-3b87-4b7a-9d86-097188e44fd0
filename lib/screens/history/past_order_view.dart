import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../models/cart/cart_data.dart';
import '../../models/cart/cart_items/cart_items.dart';
import '../../models/menu/menu_data.dart';
import '../../providers/customer/customer_order_history_provider.dart';
import '../../utils/app_utils.dart';
import '../../utils/datetime_utils.dart';

class PastOrderedView extends StatefulWidget {
  const PastOrderedView({super.key});

  @override
  State<PastOrderedView> createState() => _PastOrderedViewState();
}

class _PastOrderedViewState extends State<PastOrderedView> {
  @override
  Widget build(BuildContext context) {
    return Column(
        children: [
            Consumer<CustomerOrderHistoryProvider>(
              builder: (context, data, child) {
                if (data.pastOrders.isNotEmpty) {
                  return _pastOrderList(data.pastOrders);
                }
                if(data.pastOrders.isEmpty) {
                  return Expanded(
                    child: Center(
                      child: Text(
                          CustomerOrderHistoryString.noOrderYetErrorMessage,
                          style: AppTextStyle.smallTextStyle.copyWith(
                            fontSize: 14,
                          ),
                      ),
                    ),
                  );
                }
                return Container();
              },
            ),

        ],
    );
  }

  /// past order list
  _pastOrderList(List<CartData> pastOrders) {
    return Flexible(
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        child: Column(
           mainAxisSize: MainAxisSize.min,
           mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ...pastOrders.map((e) => _pastOrderListItem(e),),
            ],
        ),
      ),
    );
  }
  
  /// past order item
  Widget _pastOrderListItem(CartData data) {
    return Theme(
      data: Theme.of(context).copyWith(
        unselectedWidgetColor: ColorConstant.colorBlueDark,
        dividerColor: Colors.transparent,
      ),
      child: ExpansionTile(
        maintainState: true,
          title:  Column(
              mainAxisAlignment: MainAxisAlignment.start,
             mainAxisSize: MainAxisSize.min,
              children: [

                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        '${DateTimeUtils.utcTimeToConvertLocalTime(
                          (data.platform!.toLowerCase() == ConstantString.platformWeb
                              ? data.submittedAt ?? data.createdAt
                              : data.createdAt) ?? DateTime.now().toUtc().toString(),
                          formattedString: DateTimeUtils.MMMM_d_yyyy,
                        )}',
                        style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: 14.0,
                          fontWeight: FontWeight.w600,
                          color: ColorConstant.colorBlueDark,
                        ),
                      ),
                    ),
                    Text(
                      '${data.itemCount} Item(s)',
                      style: AppTextStyle.smallTextStyle.copyWith(
                        fontSize: 14.0,
                        fontWeight: FontWeight.w600,
                        color: ColorConstant.colorBlueDark,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isBigScreenResolution(context) ? 5 : 4,),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,

                  children: [
                    Flexible(
                      child: Text(
                        '${PastOrderViewString.order} #${data.id!}',
                        textAlign: TextAlign.start,
                        style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: 14.0,
                          color: ColorConstant.colorBlueDark,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isBigScreenResolution(context) ? 5 : 4,),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        data.locationName!,
                        style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: 14.0,
                          color: ColorConstant.colorBlueDark,
                        ),
                      ),
                    ),
                    Text(
                      '${NumberFormat.simpleCurrency().format(data.total!)}',
                      maxLines: 1,
                      style: AppTextStyle.smallTextStyle.copyWith(
                        fontSize: 14.0,
                        color: ColorConstant.colorBlueDark,
                      ),
                    ),
                  ],
                ),

              ],

          ),
          children: [
            _orderItemsView(data.cartItems!),
          ],
      ),
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            // check if expanded or collapsed
            if (data.isExpanded) {
              context.read<CustomerOrderHistoryProvider>().setOrderCollapsed(data.id!);
            } else {
              context.read<CustomerOrderHistoryProvider>().setOrderExpanded(data.id!);
            }
          },
          child: Container(
            decoration: BoxDecoration(
              color: data.isExpanded
                  ? ColorConstant.colorBlueLight_16
                  : Colors.transparent,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(
                  data.isExpanded ? 3 : 0,
                ),
                topRight: Radius.circular(
                  data.isExpanded ? 3 : 0,
                ),
              ),
            ),
            padding: EdgeInsets.symmetric(
              vertical: isBigScreenResolution(context) ? 16 : 14,
            ),
            child:Row(
              children: [
                // Left side (Date, Order #, Location)
                Expanded(
                  flex: 4, // Adjusts space allocation between left and right sections
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start, // Aligns text to the left
                    children: [
                      Padding(
                        padding: EdgeInsets.only(bottom: 5, left: 7),
                        child: Text(
                          '${DateTimeUtils.utcTimeToConvertLocalTime(
                            (data.platform!.toLowerCase() == ConstantString.platformWeb
                                ? data.submittedAt ?? data.createdAt
                                : data.createdAt) ?? DateTime.now().toUtc().toString(),
                            formattedString: DateTimeUtils.MMMM_d_yyyy,
                          )}',
                          style: AppTextStyle.smallTextStyle.copyWith(
                            fontSize: 14.0,
                            color: ColorConstant.colorBlueDark,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(bottom: 5, left: 7),
                        child: AutoSizeText(
                          '${PastOrderViewString.order} #${data.id!}',
                          style: TextStyle(
                            fontFamily: AppFonts.roboto,
                            fontWeight: FontWeight.w400,
                            color: ColorConstant.colorBlueDark,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          minFontSize: 12.0,
                          maxFontSize: 14.0,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(bottom: 5, left: 7),
                        child: Text(
                          data.locationName!,
                          style: AppTextStyle.smallTextStyle.copyWith(
                            fontSize: 14.0,
                            color: ColorConstant.colorBlueDark,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Right side (Items, Amount, Dropdown Icon)
                Expanded(
                  flex: 5,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Column(
                            children: [
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(bottom: 5, right: 7),
                                    child: Text(
                                      '${data.itemCount} Items',
                                      style: AppTextStyle.smallTextStyle.copyWith(
                                        fontSize: 14.0,
                                        color: ColorConstant.colorBlueDark,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(bottom: 5, right: 7),
                                    child: AutoSizeText(
                                      '${NumberFormat.simpleCurrency().format(data.total!)}',
                                      maxLines: 1,
                                      style: TextStyle(
                                        fontFamily: AppFonts.roboto,
                                        fontWeight: FontWeight.w400,
                                        color: ColorConstant.colorBlueDark,
                                      ),
                                      minFontSize: 12.0,
                                      maxFontSize: 14.0,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          SizedBox(width: 10), // Space between text and icon
                          Icon(
                            data.isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                            size: 24,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Expanded(
                //   child: Row(
                //     mainAxisSize: MainAxisSize.min,
                //     children: [
                //       Expanded(
                //         child: _refundButtonView(
                //             color: (data.status!.toLowerCase() ==
                //                 OrderStatus.cancelled)
                //                 ? ColorConstant.colorDarkYellow
                //                 : (data.status!.toLowerCase() ==
                //                 OrderStatus.REFUNDED)
                //                 ? ColorConstant.colorRedDark
                //                 : ColorConstant.colorBlueDark,
                //             label: (data.status!.toLowerCase() ==
                //                 OrderStatus.cancelled)
                //                 ? OrderStatus.cancelled
                //                 : (data.status!.toLowerCase() ==
                //                 OrderStatus.REFUNDED)
                //                 ? OrderStatus.REFUNDED
                //                 : OrderStatus.PROCESSED),
                //       ),
                //     ],
                //   ),
                // ),
              ],
            ),
          ),
        ),
       /* if(data.orderItems != null)...[
          if(data.isExpanded)...[
            SizedBox(height: isBigScreenResolution(context) ? 6 :3,),
            _orderItemsView(data.orderItems!, data.cartItems!),
          ],
        ],*/
      ],
    );
  }

  /// order item view
  Widget _orderItemsView(List<CartItems> cartItem) {
    return ListView.builder(
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 3 : 2),
          padding: EdgeInsets.all(isBigScreenResolution(context) ? 10 : 9,),
          decoration: BoxDecoration(
            color: ColorConstant.colorBlueLight_8,
            borderRadius: BorderRadius.all(Radius.circular(3.0,),),
          ),
          child: _itemRowView(cartItem[index]),
        );
      },
      itemCount: cartItem.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
    );
  }

  /// item row view
  Widget _itemRowView(CartItems cartItem) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Text(
                '${cartItem.category!} - ${cartItem.name}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.largeTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                  color: ColorConstant.colorBlueDark,
                  letterSpacing: 1.15,
                ),
              ),
            ),
            Text(
              'x ${cartItem.itemQty}',
              style: AppTextStyle.largeTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                color: ColorConstant.colorBlueDark,
                letterSpacing: 1.15,
              ),
            ),
          ],
        ),
        SizedBox(
          height: isBigScreenResolution(context) ? 9 : 7,
        ),
        Container(
          margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 8 : 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {},
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: isBigScreenResolution(context) ? 10 : 8,
                    horizontal: isBigScreenResolution(context) ? 30 : 28,
                  ),
                  decoration: BoxDecoration(
                    color: ColorConstant.colorBlueDark,
                    borderRadius: BorderRadius.all(Radius.circular(4),),
                  ),
                  child: Text(
                    NumberFormat.simpleCurrency()
                        .format(cartItem.actualPrice ?? 0),
                    style: AppTextStyle.mediumTextStyle.copyWith(
                        color: ColorConstant.colorThemeWhite,
                        letterSpacing: 1.15,
                        fontSize: isBigScreenResolution(context) ? 14 : 12),
                    maxLines: 1,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// refund button view
  Widget _refundButtonView(
      {String? label = '',
        Color? color = Colors.transparent,
        Function()? onPress}) {
    return GestureDetector(
      onTap: onPress ?? () {},
      child: Container(
        margin: EdgeInsets.symmetric(
          vertical: isBigScreenResolution(context) ? 4 : 3,
          horizontal: isBigScreenResolution(context) ? 20 : 18.50,
        ),
        decoration: BoxDecoration(
          color: color,
          border: Border.all(color: color!, width: 1),
          borderRadius: BorderRadius.all(
            Radius.circular(
              4.0,
            ),
          ),
        ),
        padding: EdgeInsets.symmetric(
          vertical: isBigScreenResolution(context) ? 5 : 4,
          horizontal: isBigScreenResolution(context) ? 10 : 9,
        ),
        child: Text(
          label!,
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
            color: ColorConstant.colorThemeWhite,
          ),
        ),
      ),
    );
  }

  /// horizontal space between past order header text
  Widget get _width7 => SizedBox(
    width: isBigScreenResolution(context) ? 7 : 5.70,
  );
}
