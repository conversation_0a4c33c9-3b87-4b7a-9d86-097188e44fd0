import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../custom_widgets/common_widget.dart';
import '../../helpers/check_internet_connection.dart';
import '../../providers/auth/auth_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import '../../utils/app_validator.dart';

class ForgotPassword extends StatefulWidget {
  const ForgotPassword({super.key});

  @override
  State<ForgotPassword> createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  static final _formKey = GlobalKey<FormState>();

  late TextEditingController _emailController;

  @override
  void initState() {
    _emailController = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: CustomAppBar(
          context: context,
          appBarTitleText: ResetPasswordViewString.resetPassword,
        ),
        body: _resetPasswordView(),
      ),
    );
  }

  /// reset password view
  Widget _resetPasswordView() {
    return Container(
      margin: EdgeInsets.all(isBigScreenResolution(context) ? 20 : 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: isBigScreenResolution(context) ? 30.0 : 28.0,
          ),
          _headerTextView(),
          _verticalSpace40,
          Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _emailController,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
                  ),
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: ColorConstant.colorThemeWhite,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      vertical: isBigScreenResolution(context) ? 18 : 16,
                      horizontal: isBigScreenResolution(context) ? 10 : 8,
                    ),
                    border: outLineBorder(context),
                    enabledBorder: outLineBorder(context),
                    focusedBorder: outLineBorder(context),
                    errorBorder: outLineBorder(context),
                    focusedErrorBorder: outLineBorder(context),
                    disabledBorder: outLineBorder(context),
                    hintText: ResetPasswordViewString.email,
                    hintStyle: AppTextStyle.smallTextStyle.copyWith(
                        fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
                        color: ColorConstant.colorBlueLight_50),
                  ),
                  validator: (value) {
                    return Validator.validateEmail(value);
                  },
                  onChanged: (value){
                    context.read<AuthProvider>().setForgotEmail(value);
                  },
                ),
                SizedBox(
                  height: isBigScreenResolution(context) ? 20.0 : 18.0,
                ),
                commonButton(
                  labelText: ResetPasswordViewString.submit,
                  onPressed: _callApiForgotPassword,
                  context: context,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// vertical space = height 40:38
  Widget get _verticalSpace40 => SizedBox(
        height: isBigScreenResolution(context) ? 40.0 : 38.0,
      );

  /// header message text view
  Widget _headerTextView() {
    return Text(
      ResetPasswordViewString.resetPasswordMessage,
      style: AppTextStyle.smallTextStyle.copyWith(
        color: ColorConstant.colorBlueLight_50,
        fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// forgot password api
  void _callApiForgotPassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    FocusScope.of(context).unfocus();

    var hasConnected = await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        if (!mounted) return;
        await context.read<AuthProvider>().forgotPassword();
        if (!mounted) return;
        hideLoaderDialog(context);
        navigateToBack(context);
      } catch (error) {
        debugPrint('Getting error : $error');
        if (!mounted) return;
        hideLoaderDialog(context);

        errorAlertDialog(error);
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }
}
