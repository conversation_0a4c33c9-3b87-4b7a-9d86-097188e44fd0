import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/common_widget.dart';
import '../../helpers/check_internet_connection.dart';
import '../../providers/alerts/alerts_provider.dart';
import '../../providers/auth/auth_provider.dart';
import '../../providers/package_info/package_info_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import '../../utils/app_validator.dart';
import '../../utils/pref_utils.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  static final _formKey = GlobalKey<FormState>();

   late TextEditingController _emailController, _passwordController;

  @override
  void initState() {
    PrefsUtils.printAllSharedPreferences();
    /// To get app version info
    getAppVersionInfo();

    _emailController = TextEditingController();
    _passwordController = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: _loginView(),
      ),
    );
  }

  /// login view
  Widget _loginView() {
    return Container(
      margin: EdgeInsets.all(isBigScreenResolution(context) ? 20 : 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _appLogoView(),
          _logInFormView(),
          _versionTextView(),
        ],
      ),
    );
  }

  /// app logo view
  Widget _appLogoView() {
    return Container(
      margin: EdgeInsets.only(
          top: isBigScreenResolution(context) ? 30 : 28,
          bottom: isBigScreenResolution(context) ? 26 : 24),
      child: Image.asset(
        AssetIcons.iconLogo,
        width: isBigScreenResolution(context) ? 120 : 110,
        height: isBigScreenResolution(context) ? 120 : 110,
      ),
    );
  }

  /// login form view
  Widget _logInFormView(){
    return Consumer<AuthProvider>(builder: (_, data, child) {
      return Expanded(
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _emailTextField(data),
                _verticalSpace16,
                _passwordTextField(data),
                commonButton(
                  labelText: LoginViewString.login,
                  onPressed: _login,
                  context: context,
                ),
                GestureDetector(
                  onTap:()=> navigateToForgotPassword(context),
                  child: Text(
                    LoginViewString.forgotPassword,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.smallTextStyle.copyWith(
                      color: ColorConstant.colorBlueDark,
                      fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },

    );
  }

  /// vertical space = height 16:14
  Widget get _verticalSpace16 => SizedBox(
    height: isBigScreenResolution(context) ? 16.0 : 14.0,
  );

  /// version text view
  Widget _versionTextView(){
    return Text(
      'v ${context.watch<PackageInfoProvider>().versionName ?? ''}'
          '(${context.watch<PackageInfoProvider>().versionCode ?? ''})',
      style: AppTextStyle.smallTextStyle.copyWith(
        color: ColorConstant.colorBlueLight_32,
        fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
      ),
    );
  }

  /// input decoration
  _inputDecoration(AuthProvider data, String hintText, {needSuffix = false}) => InputDecoration(
        filled: true,
        fillColor: ColorConstant.colorThemeWhite,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(
          vertical: isBigScreenResolution(context) ? 18 : 16,
          horizontal: isBigScreenResolution(context) ? 10 : 8,
        ),
        border: outLineBorder(context),
        enabledBorder: outLineBorder(context),
        focusedBorder: outLineBorder(context),
        errorBorder: outLineBorder(context),
        focusedErrorBorder: outLineBorder(context),
        disabledBorder: outLineBorder(context),
        hintText: hintText,
        hintStyle: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
            color: ColorConstant.colorBlueLight_50),
        suffixIcon: needSuffix
            ? IconButton(
                icon: Icon(
                  data.showPassword ? Icons.visibility
                      : Icons.visibility_off,
                  color: ColorConstant.colorBlueDark,
                ),
                onPressed: () {
                  data.setPasswordShowHide();
                },
              )
            : null,
        suffixIconConstraints: needSuffix ? const BoxConstraints() : null,
      );


  /// get app version info using package info
  void getAppVersionInfo() async{
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    if (!mounted) return;
    context.read<PackageInfoProvider>()
        .setVersionNameAndCode(packageInfo.version, packageInfo.buildNumber);
  }

  /// login api
  void _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    FocusScope.of(context).unfocus();

    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      if (!mounted) return;
      showLoaderDialog(context);
      try {
        if (!mounted) return;
          await context.read<AuthProvider>().loginWithEmail();
        _callGetUserProfile();
      } catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);

        context.read<AlertsProvider>().setNewAlert(
            alertData: AlertModel(
              alertType: AlertType.alertDialog,
              title: ConstantString.error,
              message: error.toString(),
            ));
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// Get user profile api
  void _callGetUserProfile() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      try {
        if (!mounted) return;
        await context.read<AuthProvider>().getProfileResponse();

        if (!mounted) return;
        hideLoaderDialog(context);
        navigateToHomeScreen(context);
      } catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        context.read<AlertsProvider>().setNewAlert(
            alertData: AlertModel(
              alertType: AlertType.alertDialog,
              title: ConstantString.error,
              message: error.toString(),
            ));
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// email text field
  Widget _emailTextField(AuthProvider data) {
    return Semantics(
      identifier: "email",
      child: TextFormField(
        controller: _emailController,
        style: AppTextStyle.smallTextStyle.copyWith(
          fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
        ),
        keyboardType: TextInputType.emailAddress,
        decoration: _inputDecoration(data, LoginViewString.email),
        validator: (value) {
          return Validator.validateEmail(value);
        },
        onChanged: (value) {
          data.setEmail(value);
        },
      ),
    );
  }

  /// password text field
  Widget _passwordTextField(AuthProvider data) {
    return Semantics(
      identifier: "password",
      child: TextFormField(
        controller: _passwordController,
        obscureText: !data.showPassword,
        style: AppTextStyle.smallTextStyle.copyWith(
          fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
        ),
        decoration: _inputDecoration(data, LoginViewString.password, needSuffix: true),
        validator: (value) {
          return Validator.validatePassword(value);
        },
        onChanged: (value) {
          data.setPassword(value);
        },
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

}
