import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:gr8tables_server_manager/constants/app_string.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../../helpers/currency_text_formatter.dart';
import '../../../helpers/check_internet_connection.dart';
import '../../../helpers/http_response_helper.dart';
import '../../../models/discount/promotion_apply_on.dart';
import '../../../models/loyalty/reward_types.dart';
import '../../../my_app.dart';
import 'dart:io' show Platform;

import '../../constants/app_color.dart';
import '../../constants/app_dimens.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/dotted_decoration.dart';
import '../../models/cart/cart_custom_discount_response.dart';
import '../../providers/discount/discount_provider.dart';
import '../../utils/app_utils.dart';



class CartCustomDiscountView extends StatefulWidget {
  const CartCustomDiscountView({Key? key}) : super(key: key);

  @override
  State<CartCustomDiscountView> createState() => _CartCustomDiscountViewState();
}

class _CartCustomDiscountViewState extends State<CartCustomDiscountView> with WidgetsBindingObserver {

  late TextEditingController _amountController,_noteController;
  late FocusNode _amountFieldFocus, _noteFieldFocus;
  ScrollController _scrollController = ScrollController();

  /// Determine whether the keyboard is hidden.
  Future<bool> get keyboardHidden async {
    // If the embedded value at the bottom of the window is not greater than 0, the keyboard is not displayed.
    final check = () => (WidgetsBinding.instance.window.viewInsets.bottom ) <= 0;
    // If the keyboard is displayed, return the result directly.
    if (!check()) return false;
    // If the keyboard is hidden, in order to cope with the misjudgment caused by the keyboard display/hidden animation process, wait for 0.1 seconds and then check again and return the result.
    return await Future.delayed(Duration(milliseconds: 100), () => check());
  }

  @override
  void initState() {
      WidgetsBinding.instance.addObserver(this);
     _amountController = TextEditingController();
      _noteController = TextEditingController();
     _amountFieldFocus = FocusNode();
     _noteFieldFocus = FocusNode();

      _amountFieldFocus.addListener(() {
        _onFocusChange(forNote: false);

        if(_amountFieldFocus.hasFocus) {
          SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
            // remove error message if any when focus on text field
            if (context
                .read<DiscountProvider>()
                .customDiscountError
                .isNotEmpty) {
              await context.read<DiscountProvider>().setCustomDiscountError("");
            }
          });
        }
      });
      _noteFieldFocus.addListener(() {
        _onFocusChange(forNote: true);
      });

      Future.delayed(Duration(milliseconds: 300,), () async{
        // set order
       await  context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);
      });

    super.initState();
  }

  void _onFocusChange({forNote=false}) {
    Future.delayed(Duration(seconds: 1),()
    {
         if(_scrollController.hasClients) {
           if(!forNote) {
             debugPrint("amount Focus: ${_amountFieldFocus.hasFocus.toString()}");
             _scrollController.jumpTo(_amountFieldFocus.hasFocus ? _scrollController.position.maxScrollExtent : _scrollController.position.minScrollExtent);
           }else {
             debugPrint("Note Focus: ${_noteFieldFocus.hasFocus.toString()}");
             _scrollController.jumpTo(_noteFieldFocus.hasFocus ? _scrollController.position.maxScrollExtent : _scrollController.position.minScrollExtent);
           }

         }
    });
  }

  @override
  void didChangeMetrics() {
    final value = WidgetsBinding.instance.window.viewInsets.bottom;
    if (value == 0) {
     // _amountFieldFocus.unfocus();
      //_noteFieldFocus.unfocus();
    }
    if (Platform.isAndroid) {
      keyboardHidden.then((value) =>
      value ? FocusManager.instance.primaryFocus?.unfocus() : null);
    }
    super.didChangeMetrics();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    _amountFieldFocus.dispose();
    _noteFieldFocus.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.only(left: 15,right: 15,top: 10, bottom: 10,),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            _backToPreviousView(),
            // Discount amount view
            _inputView(),
            // Cart summary view
            _cartView(),
            // Apply button
            GestureDetector(
              onTap: !(context.read<DiscountProvider>().checkCustomDiscountOnCart()) && (context.read<DiscountProvider>().customCartDiscount !=0) ? (){
                _applyCustomDiscount();
              } : null,
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: 10,bottom: 10,left: 20,right: 20),
                padding:  EdgeInsets.symmetric(
                    vertical: isBigScreenResolution(context) ? 12.0 : 10.0),
                decoration: BoxDecoration(
                    color: context.watch<DiscountProvider>().customCartDiscount!=0 ? ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_16,
                    borderRadius: BorderRadius.circular(5.0)),
                child: Text(
                  CartCustomDiscountString.apply.toUpperCase(),
                  textAlign: TextAlign.center,
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                    letterSpacing: 0.5,
                    color:  context.watch<DiscountProvider>().customCartDiscount!=0 ? Colors.white : ColorConstant.colorBlueDark,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// input view
  Widget _inputView(){

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(7.0),
                decoration: BoxDecoration(
                  color: context.read<DiscountProvider>().customDiscountError.isNotEmpty ? ColorConstant.colorLightRed_20 : Colors.transparent,
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: context.read<DiscountProvider>().customDiscountError.isNotEmpty  ?  ColorConstant.colorLightRed_20 :
                  ColorConstant.colorBlueLight_8,
                    width: 2,),
                ),
                child: Container(
                  margin: const EdgeInsets.only(top: 5,left: 5),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(CartCustomDiscountString.discountAmount, style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                          letterSpacing: 0.3,
                          color: ColorConstant.colorBlueDark,
                        ),
                      ),
                      const SizedBox(height: 15,),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          _switchDiscount(),
                          Expanded(
                            child: TextField(
                              enabled: !(context.read<DiscountProvider>().checkCustomDiscountOnCart()),
                              textAlign: TextAlign.right,
                              showCursor: true,
                              keyboardType: TextInputType.numberWithOptions(decimal: true,),
                              maxLines: 1,
                              controller: _amountController,
                              focusNode: _amountFieldFocus,
                              autofocus: true,
                              cursorColor: ColorConstant.colorBlueDark,
                              onChanged: (value){
                                context.read<DiscountProvider>().hasApplyCustomDiscountError(0,forCart: true);
                                if(value.isNotEmpty) {
                                  var tmp = value.replaceAll("\$", "").replaceAll(",", "").trim();
                                  tmp = tmp.isEmpty ? "0" : tmp;
                                  context.read<DiscountProvider>().hasApplyCustomDiscountError(num.parse(tmp), forCart: true);

                                }else {
                                  context.read<DiscountProvider>().hasApplyCustomDiscountError(0, forCart: true);
                                }
                              },
                              onSubmitted: (value) {
                              },
                              inputFormatters: [
                                CurrencyTextInputFormatter(
                                    locale: 'en_US',
                                    decimalDigits: 2,
                                    name:  context.read<DiscountProvider>().discountType == '\$' ?'\$' : null,
                                    symbol:  context.read<DiscountProvider>().discountType == '\$' ?'\$' : ''
                                ),

                                /*LengthLimitingTextInputFormatter(8),*/],
                              style: AppTextStyle.largeTextStyle.copyWith(
                                color: context.read<DiscountProvider>().customDiscountError.isNotEmpty  ? ColorConstant.colorLightRed_20 : ColorConstant.colorBlueDark,
                                fontSize: isBigScreenResolution(context) ? 24.0 : 14.0,
                                letterSpacing: 0.5,
                              ),
                              //textDirection: TextDirection.rtl,
                              decoration: InputDecoration(
                                filled: false,
                                border: InputBorder.none,
                                hintText: context.read<DiscountProvider>().discountType =='\$' ? "\$0.00" : "0.00",
                                contentPadding: EdgeInsets.zero,
                                hintStyle: AppTextStyle.largeTextStyle.copyWith(
                                  letterSpacing: 0.5,
                                  fontSize: isBigScreenResolution(context) ? 24.0 : 14.0,
                                  color: ColorConstant.colorBlueDark,
                                ),
                              ),
                              onTapOutside: (event){
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                            ),
                          ),
                          if(context.read<DiscountProvider>().discountType =='\%')...[
                            Text('\%', style: AppTextStyle.largeTextStyle.copyWith(
                              fontSize: isBigScreenResolution(context) ? 24.0 : 14.0,
                              letterSpacing: 0.5,
                              color: ColorConstant.colorBlueDark,
                            ),),
                          ],
                        ],
                      ),

                    ],
                  ),
                ),
              ),
            )
          ],
        ),
        const SizedBox(height: 7,),
        if(context.read<DiscountProvider>().customDiscountError.isNotEmpty)...[

          Container(
            margin:  const EdgeInsets.only(left: 5,),
            child: Row(
               mainAxisSize: MainAxisSize.min,
               mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Icon(Icons.info_sharp, color: ColorConstant.colorLightRed_20, size: 13,),
                WidthDimens().getWidth2(),
                Text(context.read<DiscountProvider>().customDiscountError,
                    style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorLightRed_20,
                    fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                  ),
                ),
              ],
            ),
          ),
        ],
        _notesSection(),
        if(_amountFieldFocus.hasFocus)...[
          const SizedBox(height: 60,),
        ],
        if(_noteFieldFocus.hasFocus)...[
          const SizedBox(height: 120,),
        ],
      ],
    );
  }

  /// switch discount
  Widget _switchDiscount(){

    return GestureDetector(
      onTap: (){
           context.read<DiscountProvider>().setDiscountType(context.read<DiscountProvider>().discountType =='\$' ? '\%' : '\$',
               context.read<DiscountProvider>().knobAlignment == Alignment.centerRight ? Alignment.centerLeft : Alignment.centerRight);
           _amountController.clear();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 70,
        height: 28,
        curve: Curves.decelerate,
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstant.colorBlueDark, width: 1),
          borderRadius: BorderRadius.circular(3),
          color: Colors.white,
        ),
        child: Stack(
            children: [
              _symbolTextView(label: '\%', alignment: Alignment.centerLeft, padding: EdgeInsets.only(left: 10.0)),
              _symbolTextView(label: '\$', alignment: Alignment.centerRight, padding: EdgeInsets.only(right: 10.0)),
              AnimatedAlign(
                duration: const Duration(milliseconds: 300),
                alignment: context.read<DiscountProvider>().knobAlignment,
                curve: Curves.decelerate,
                child: Container(
                  decoration: BoxDecoration(color: ColorConstant.colorBlueDark, borderRadius: BorderRadius.circular(5)),
                  margin : const EdgeInsets.only(top:3, bottom : 3,left: 3, right : 3,),
                  padding: const EdgeInsets.only(left: 12, right: 12,top: 2, bottom: 2,),
                  child:Text(context.read<DiscountProvider>().discountType,textAlign: TextAlign.center,
                      style : const TextStyle(color: Colors.white,fontSize:12,fontFamily: AppFonts.roboto,),
                  ),

                ),
              ),
            ],
        ),
      ),
    );
  }

  /// common switch discount view
  Widget _symbolTextView({String label='',AlignmentGeometry alignment = Alignment.center, required EdgeInsetsGeometry padding}){
    return Padding(
      padding: padding,
      child: Align(
        alignment: alignment,
        child: Text(label, style: TextStyle(color : ColorConstant.colorGrayDark, fontSize: 12, fontFamily: AppFonts.roboto,),),
      ),
    );
}

  ///cart view
  Widget _cartView() {
      return Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            CartCustomDiscountString.cartSummary,
            style: AppTextStyle.mediumTextStyle.copyWith(
              fontSize: isBigScreenResolution(context)
                  ? 16.0
                  : 14.0,
              letterSpacing: 1.2,
              color: ColorConstant.colorBlueDark,
            ),
          ),
          Column(
            children: [
               SizedBox(height: isBigScreenResolution(context) ? 20 : 15,),

              if(context.watch<DiscountProvider>().cartData !=null) ...[
                _height15(),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: _cartHeaderTextView(
                        CartCustomDiscountString.subTotal,
                      ),
                    ),
                    _cartHeaderTextView(
                      '${NumberFormat.simpleCurrency().format(context.read<DiscountProvider>().subTotal < 0 ? 0 : context.read<DiscountProvider>().subTotal)}',
                    ),
                  ],
                ),
                _height15(),

                if(context.read<DiscountProvider>().cartData!.cartCustomDiscount!.isNotEmpty)...[

                  ...context.read<DiscountProvider>().cartData!.cartCustomDiscount!.map((customDiscount) =>
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _promotionalView(type: CartCustomDiscountString.custom,onCart: customDiscount.applyOn!.toLowerCase() ==PromotionApplyOn.cart.name,
                              label: CartCustomDiscountString.discount ,value:  customDiscount.appliedValue ,onRemove: (){
                                _removePromotions(forCustomDisc: true,forPromo: false, id: customDiscount.id);
                              }),
                          _height15(),
                        ],
                      ),
                  ),
                ],

                if(context.read<DiscountProvider>().cartData?.cartPromoCode!=null)...[
                  _promotionalView(type: CartCustomDiscountString.code,onCart: context.read<DiscountProvider>().cartData?.cartPromoCode!.applyOn!.toLowerCase() ==PromotionApplyOn.cart.name,
                      label: context.read<DiscountProvider>().cartData?.cartPromoCode!.code! ,value:  context.read<DiscountProvider>().cartData?.cartPromoCode!.appliedValue ,onRemove: (){
                        _removePromotions(forCustomDisc: false,forPromo: true, id: context.read<DiscountProvider>().cartData?.cartPromoCode!.id);
                      }),
                  _height15(), ],

                if(context.read<DiscountProvider>().cartData?.cartDiscount!=null)...[
                  _promotionalView(type: CartCustomDiscountString.preset,onCart: context.read<DiscountProvider>().cartData?.cartDiscount!.applyOn!.toLowerCase() == PromotionApplyOn.cart.name,
                      label: context.read<DiscountProvider>().cartData?.cartDiscount!.title! ,value:  context.read<DiscountProvider>().cartData?.cartDiscount!.appliedValue, onRemove: (){
                        _removePromotions(forCustomDisc: false,forPromo: false, id: context.read<DiscountProvider>().cartData?.cartDiscount!.id);
                      }),
                  _height15(), ],

                if(context.read<DiscountProvider>().cartData?.cartLoyalty !=null)...[

                  if(context.read<DiscountProvider>().cartData!.cartLoyalty!.pointsRedeemed! > 0)...[
                    _redemptionView(type: CartCustomDiscountString.redeemPoints,onCart: true, label: CartCustomDiscountString.membershipPoints ,
                        value:  context.read<DiscountProvider>().cartData?.cartLoyalty!.rewardAmount!,
                        onRemove: (){
                          _removeRedeemPoints(id: context.read<DiscountProvider>().cartData?.cartLoyalty!.id!);
                        }),
                    _height15(),
                  ],
                ],

                if(context.read<DiscountProvider>().cartData?.cartReward != null)...[
                  _redemptionView(type: CartCustomDiscountString.reward,onCart: ( (context.read<DiscountProvider>().cartData!.cartReward!.rewardType!.toLowerCase() ==
                      RewardType.discount_flat.name) || (context.read<DiscountProvider>().cartData!.cartReward!.rewardType!.toLowerCase() ==
                      RewardType.discount_percentage.name) ), label:  context.read<DiscountProvider>().cartData!.cartReward!.name!.isNotEmpty ?
                  '(${context.read<DiscountProvider>().cartData!.cartReward!.name!})': '',
                      value:  context.read<DiscountProvider>().cartData!.cartReward!.rewardAmount!,
                      onRemove: (){
                        _removeTierRewards(id: context.read<DiscountProvider>().cartData?.cartReward!.id!);
                      }),
                  _height15(),
                ],

                if(context.read<DiscountProvider>().customCartDiscount!=0)...[
                  _promotionalView(type: CartCustomDiscountString.custom,onCart: true, label: CartCustomDiscountString.discount ,value:  context.read<DiscountProvider>().customCartDiscount,
                  onRemove: (){
                    context.read<DiscountProvider>().hasApplyCustomDiscountError(0, forCart: true);
                    _amountController.clear();
                  }),
                  _height15(),],
                if(context.read<DiscountProvider>().totalSavings!=0)...[
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [

                    Expanded(
                      child: _cartHeaderTextView(
                        CartCustomDiscountString.totalSavings,
                      ),
                    ),
                    _cartHeaderTextView(
                      '${NumberFormat.simpleCurrency().format(context.read<DiscountProvider>().totalSavings)}',
                    ),

                  ],
                ),
                  _height15(),],

                if(context.read<DiscountProvider>().newSubTotal!=0.0 || context.read<DiscountProvider>().customCartDiscount!=0)...[
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          CartCustomDiscountString.newSubTotal,
                          style: AppTextStyle.smallTextStyle.copyWith(
                            fontSize: isBigScreenResolution(context)
                                ? 16.0
                                : 14.0,
                            color: ColorConstant.colorBlueDark,
                          ),
                        ),
                      ),
                      Text(
                        '${NumberFormat.simpleCurrency().format(context.read<DiscountProvider>().newSubTotal)}',
                        style: AppTextStyle.largeTextStyle.copyWith(
                          color: ColorConstant.colorBlueDark,
                          fontSize: isBigScreenResolution(context)
                              ? 16.0
                              : 14.0,
                        ),
                      ),
                    ],
                  ),
                  HeightDimens().getHeight15(),
                ],
                _height15(),
              ],
            ],
          ),
        ],
      );
  }

  /// common text view for cart header
  Widget _cartHeaderTextView(String label){
    return Text(
      label,
      style: AppTextStyle.smallTextStyle.copyWith(
        fontSize: isBigScreenResolution(context)
            ? 16.0
            : 14.0,
        color: ColorConstant.colorBlueDark,
      ),
    );
  }

  Widget _height15(){
    return SizedBox(height: isBigScreenResolution(context) ?15 : 10,);
  }

 /// promotional view
  Widget _promotionalView({String type='', bool onCart =false,String? label= CartCustomDiscountString.custom ,num? value =0 ,Function()? onRemove}) {
    return Container(
      margin: const EdgeInsets.only(left: 5, right: 5,),
       padding: const EdgeInsets.all(10.0,),
      decoration: DottedDecoration(shape: Shape.box , strokeWidth: 0.70, color: ColorConstant.colorBlueDark,),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon( onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp, size: 16, color: ColorConstant.colorBlueDark,),
          const SizedBox(width: 5,),
          Expanded(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text( type ,style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context)
                        ? 14.0
                        : 12.0,
                  ), ),
                  Flexible(child: Text(' $label '.replaceAll("", "\u{200B}"),maxLines: 1, overflow: TextOverflow.ellipsis ,style: AppTextStyle.smallTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context)
                        ? 14.0
                        : 12.0,
                    color: ColorConstant.colorBlueDark,
                  ),),),
                  Text('(-${NumberFormat.simpleCurrency().format(value??0)})',style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context)
                        ? 14.0
                        : 12.0,
                  ),),
                  const SizedBox(width: 5,),
                ],
              ),
          ),
          InkWell(onTap: onRemove ,child: Icon(  Icons.cancel_sharp, size: 17, color: ColorConstant.colorBlueDark,)),
        ],
      ),
    );
  }

  /// notes section
  Widget _notesSection() {
    return Container(
      margin: const EdgeInsets.only(top: 15,),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(CartCustomDiscountString.notes, style: AppTextStyle.largeTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
            letterSpacing: 0.5,
            color: ColorConstant.colorBlueDark,
          ),),
          HeightDimens().getHeight10(),
          TextField(
             controller: _noteController,
             focusNode: _noteFieldFocus,
              keyboardType:TextInputType.multiline,
              maxLines: 5,
              textInputAction: TextInputAction.done,
              minLines: 5,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                letterSpacing: 0.5,
                color: ColorConstant.colorBlueDark,
              ),
              cursorColor: ColorConstant.colorGrayDark,
              decoration: InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                      width: 1, color: ColorConstant.colorBlueLight_8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                      width: 1, color: ColorConstant.colorBlueLight_8),
                ),
                filled: true,
                border: InputBorder.none,
                fillColor: Colors.transparent,
                contentPadding: EdgeInsets.all(5),
                hintText: CartCustomDiscountString.describeReason,
                hintStyle: AppTextStyle.smallTextStyle.copyWith(
                  color: ColorConstant.colorBlueLight_50,
                  letterSpacing: 0.5,
                  fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                ),
              ),
              onTapOutside: (event){
              FocusManager.instance.primaryFocus?.unfocus();
            },
          ),
        ],
      ),
    );
  }

  /// back to previous view
  _backToPreviousView() {
    return Container(
     margin: const EdgeInsets.only(bottom: 15,),
      child: InkWell(
         onTap: (){
           context.read<DiscountProvider>().setCustomNextPressed(false);
           context.read<DiscountProvider>().resetCustomDiscount();
         },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(Icons.arrow_back_ios_outlined, size:  isBigScreenResolution(context)
                ? 15.0
                : 13.0, color: ColorConstant.colorBlueDark,),
            WidthDimens().getWidth12(),
            Expanded(
              flex: 10,
              child: RichText(
                 text: TextSpan(
                   text: CartCustomDiscountString.discountOption,
                   style:  AppTextStyle.smallTextStyle.copyWith(
                     color: ColorConstant.colorBlueLight_50,
                     fontSize: isBigScreenResolution(context)
                         ? 16.0
                         : 14.0,
              
                   ),
                   children: [
                     TextSpan(
                       text: '  /  ${CartCustomDiscountString.optionCart}',
                       style: AppTextStyle.largeTextStyle.copyWith(
                         fontSize: isBigScreenResolution(context)
                             ? 16.0
                             : 14.0,
                           color: ColorConstant.colorBlueDark,
                       ),
                     ),
                   ],
                 ),
               ),
            ),
            GestureDetector(
              onTap: (){
                context.read<DiscountProvider>().setCustomNextPressed(false);
              },
              child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                color: ColorConstant.colorBlueDark,),
            ),
          ],
        ),
      ),
    );
  }

  /// remove promotions
  void _removePromotions({required bool forCustomDisc ,required bool forPromo , required num? id}) async {

    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {


        if(forCustomDisc){
          await context.read<DiscountProvider>().removeCustomDiscount(customDiscountId: id!);
        } else if(forPromo) {
          await context.read<DiscountProvider>().removePromoCode(id: id!);
        }else {
          await context.read<DiscountProvider>().removeDiscount(id: id!);
        }
        /// recall order and save data to local for order
        if(null != context.read<DiscountProvider>().cartData) {
          /// recall order and save data to local for order
          await context.read<CartViewProvider>().getCart(id:context.read<DiscountProvider>().cartData!.id!);
         // await context.read<TakeOutDeliveryViewProvider>().resetCurrentPage();
         // await context.read<TakeOutDeliveryViewProvider>().getCartList();
        }
        // set order
        await context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);
        if(!forCustomDisc) await _reCalculateCustomDiscount();

        hideLoaderDialog(context);
      }  on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        //context.read<DiscountProvider>().setDiscountInputError(true,error.toString());
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        //context.read<DiscountProvider>().setDiscountInputError(true,error.toString());
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        //context.read<DiscountProvider>().setDiscountInputError(true,error.toString());
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// apply custom discount
  _applyCustomDiscount() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        context.read<DiscountProvider>().setCustomDiscountNote(_noteController.text.trim());
        CartCustomDiscountResponse _cartCustomDiscountResponse =  await context.read<DiscountProvider>().applyCustomDiscount();

        if (!mounted) return;
        hideLoaderDialog(context);
        context.read<DiscountProvider>().setSuccessMsg(_cartCustomDiscountResponse.message!);
        if(Navigator.canPop(context)){
          Navigator.of(context).pop(true);
        }
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('apply custom discount data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        await context.read<DiscountProvider>().setCustomDiscountError(error.toString());
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        await context.read<DiscountProvider>().setCustomDiscountError(error.toString());
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        await context.read<DiscountProvider>().setCustomDiscountError(error.toString());
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }


   /// re calculate custom discount if any
  /// already applied promotions removed
  _reCalculateCustomDiscount() async{
    context.read<DiscountProvider>().hasApplyCustomDiscountError(0, forCart: true);
    String value = _amountController.text.trim();
    if(value.isNotEmpty) {
      var tmp = value.replaceAll("\$", "").replaceAll(",", "").trim();
      tmp = tmp.isEmpty ? "0" : tmp;
      context.read<DiscountProvider>().hasApplyCustomDiscountError(num.parse(tmp), forCart: true);

    }else {
      context.read<DiscountProvider>().hasApplyCustomDiscountError(0, forCart: true);
    }
  }

  /// redemption view
  Widget _redemptionView({String type='', bool onCart =false,String? label= CartCustomDiscountString.custom ,num? value =0 ,Function()? onRemove}) {
    return Container(
      margin: const EdgeInsets.only(left: 5, right: 5,),
      padding: const EdgeInsets.all(10.0,),
      decoration: DottedDecoration(shape: Shape.box , strokeWidth: 0.70, color: ColorConstant.colorBlueDark,),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Image.asset(
            AssetImages.loyaltyImage,
            width: isBigScreenResolution(context) ? 16 : 12,
            height: isBigScreenResolution(context) ? 16 : 12,
            color: ColorConstant.colorBlueDark,
          ),
          const SizedBox(width: 5,),
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text( type ,style: AppTextStyle.mediumTextStyle.copyWith(
                  color: ColorConstant.colorBlueDark,
                  fontSize: isBigScreenResolution(context)
                      ? 14.0
                      : 12.0,
                ), ),
                Flexible(child: Text(' $label '.replaceAll("", "\u{200B}"),maxLines: 1, overflow: TextOverflow.ellipsis ,style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context)
                      ? 14.0
                      : 12.0,
                  color: ColorConstant.colorBlueDark,
                ),),),
                Text('(-${NumberFormat.simpleCurrency().format(value??0)})',style: AppTextStyle.smallTextStyle.copyWith(
                  color: ColorConstant.colorBlueDark,
                  fontSize: isBigScreenResolution(context)
                      ? 14.0
                      : 12.0,
                ),),
                const SizedBox(width: 5,),
              ],
            ),
          ),
          InkWell(onTap: onRemove ,child: Icon(  Icons.cancel_sharp, size: 17, color: ColorConstant.colorBlueDark,)),
        ],
      ),
    );
  }

  /// remove membership points
  void _removeRedeemPoints({required num? id}) async {

    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        // await context
        //     .read<MembershipPointsProvider>()
        //     .removeLoyaltyFromOrder(loyaltyId: id!);

        /// recall order and save data to local for order
        if(null != context.read<DiscountProvider>().cartData) {
          /// recall order and save data to local for order
          // await context.read<CartDataViewProvider>().getCart(id:context.read<DiscountProvider>().cartData!.id!);
          // await context.read<TakeOutDeliveryViewProvider>().resetCurrentPage();
          // await context.read<TakeOutDeliveryViewProvider>().getCartList();
        }
        // set order
        await context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);
        await _reCalculateCustomDiscount();

        hideLoaderDialog(context);
      }  on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove tier rewards
  void _removeTierRewards({required num? id}) async {

    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        // await context
        //     .read<MembershipPointsProvider>()
        //     .removeReward(rewardId: id!);
        //
        // /// recall order and save data to local for order
        // if(null != context.read<DiscountProvider>().cartData) {
        //   /// recall order and save data to local for order
        //   await context.read<CartDataViewProvider>().getCart(id:context.read<DiscountProvider>().cartData!.id!);
        //   await context.read<TakeOutDeliveryViewProvider>().resetCurrentPage();
        //   await context.read<TakeOutDeliveryViewProvider>().getCartList();
        // }
        // set order
        await context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);
        await _reCalculateCustomDiscount();

        hideLoaderDialog(context);
      }  on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

}
