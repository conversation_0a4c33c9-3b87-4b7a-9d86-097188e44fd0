import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/currency_text_formatter.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../utils/app_utils.dart';

class AddServiceChargeContent extends StatefulWidget {

  final Function() onButtonPressed;

  AddServiceChargeContent({super.key, required this.onButtonPressed});

  @override
  State<AddServiceChargeContent> createState() => _AddServiceChargeContentState();
}

class _AddServiceChargeContentState extends State<AddServiceChargeContent> {

  late TextEditingController _chargeController;
  late FocusNode _chargeFieldFocus;

  @override
  void initState() {
    _chargeController = TextEditingController();
    _chargeFieldFocus = FocusNode();
    super.initState();
  }

  @override
  void dispose() {
    _chargeController.dispose();
    _chargeFieldFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(isBigScreenResolution(context)? 18 : 16,),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 6.0 : 4.0,),
                  child: Text(
                    AddServiceChargeViewString.serviceCharge,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.largeTextStyle.copyWith(
                      color: ColorConstant.colorBlueDark,
                      fontSize: isBigScreenResolution(context) ? 16 : 14,
                      letterSpacing: 0.15,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: (){
                   // context.read<CartViewProvider>().resetAddServiceChargeDialog();
                  if(Navigator.canPop(context)){
                    Navigator.pop(context);
                  }
                },
                child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                  color: ColorConstant.colorBlueDark,),
              ),
            ],
          ),

          Container(
            margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 10.0 : 8.0,
            bottom: isBigScreenResolution(context)? 4: 2,),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(child: _inputView()),
              ],
            ),
          ),

          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(child: _addButtonView()),
            ],
          ),
        ],
      ),
    );
  }

  /// input view
  Widget _inputView(){

    return Container(
      margin: const EdgeInsets.only(left: 15,right: 15,top: 5, bottom: 2,),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(3.0),
                  decoration: BoxDecoration(
                    color: context.read<CartViewProvider>().serviceChargeError.isNotEmpty ? ColorConstant.colorLightRed_20 : Colors.transparent,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(color: context.read<CartViewProvider>().serviceChargeError.isNotEmpty  ? ColorConstant.colorLightRed_20 :
                    ColorConstant.colorBlueLight_8 ,
                      width: 1,),
                  ),
                  child: Container(
                    margin: const EdgeInsets.only(top: 5,left: 5),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            _switchServiceCharge(),
                            Expanded(
                              child: TextField(
                                textAlign: TextAlign.right,
                                showCursor: true,
                                keyboardType: TextInputType.numberWithOptions(decimal: true,),
                                maxLines: 1,
                                controller: _chargeController,
                                focusNode: _chargeFieldFocus,
                                cursorColor: ColorConstant.colorBlueDark,
                                onChanged: (value){

                                  if(value.isNotEmpty) {
                                    var tmp = value.replaceAll("\$", "").replaceAll(",", "").trim();
                                    tmp = tmp.isEmpty ? "0" : tmp;
                                    context.read<CartViewProvider>().setServiceCharge(num.parse(tmp),);
                                  }else {
                                    context.read<CartViewProvider>().setServiceCharge(0,);
                                  }
                                },
                                inputFormatters: [
                                  CurrencyTextInputFormatter(
                                      locale: 'en_US',
                                      decimalDigits: 2,
                                      name:  context.watch<CartViewProvider>().serviceChargeType == '\$' ?'\$' : null,
                                      symbol:  context.watch<CartViewProvider>().serviceChargeType == '\$' ?'\$' : ''
                                  ),
                                ],
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  color: context.watch<CartViewProvider>().serviceChargeError.isNotEmpty  ? ColorConstant.errorColor : ColorConstant.colorGrayDark,
                                  fontSize: isBigScreenResolution(context) ? 18.0 : 12.0,
                                  letterSpacing: 0.5,
                                ),
                                //textDirection: TextDirection.rtl,
                                decoration: InputDecoration(
                                  filled: false,
                                  border: InputBorder.none,
                                  hintText: context.watch<CartViewProvider>().serviceChargeType =='\$' ? "\$0.00" : "0.00",
                                  contentPadding: EdgeInsets.zero,
                                  hintStyle: AppTextStyle.largeTextStyle.copyWith(
                                    letterSpacing: 0.5,
                                    fontSize: isBigScreenResolution(context) ? 18.0 : 12.0,
                                    color: ColorConstant.colorBlueDark,
                                  ),
                                ),
                                onTapOutside: (event){
                                  FocusManager.instance.primaryFocus?.unfocus();
                                },
                              ),
                            ),
                            if(context.watch<CartViewProvider>().serviceChargeType =='\%')...[
                              Container(
                                padding: EdgeInsets.only(top: 5),
                                child: Text('\%', style: AppTextStyle.largeTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context) ? 18.0 : 12.0,
                                    letterSpacing: 0.5,
                                    color: ColorConstant.colorBlueDark,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),

                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 7,),
          if(context.read<CartViewProvider>().serviceChargeError.isNotEmpty)...[

            Container(
              margin:  const EdgeInsets.only(left: 5,),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(Icons.info_sharp, color: ColorConstant.colorRedDark_5, size: 14,),
                  const SizedBox(width: 2,),
                  Flexible(
                    child: Text(context.read<CartViewProvider>().serviceChargeError, style: AppTextStyle.smallTextStyle.copyWith(
                      color: ColorConstant.colorRedDark_5,
                      fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// switch service charge
  Widget _switchServiceCharge(){

    return GestureDetector(
      onTap: (){
        final orderDetailProvider = context.read<CartViewProvider>();
        orderDetailProvider.setServiceChargeType(
          orderDetailProvider.serviceChargeType == '\$' ? '\%' : '\$', // Toggle the service charge type
          orderDetailProvider.knobAlignment == Alignment.centerRight
              ? Alignment.centerLeft
              : Alignment.centerRight, // Toggle the knob alignment
        );
        _chargeController.clear();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 70,
        height: 28,
        curve: Curves.decelerate,
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstant.colorBlueDark, width: 1),
          borderRadius: BorderRadius.circular(3),
          color: Colors.white,
        ),
        child: Stack(children: [
          _symbolTextView(label: '\%', alignment: Alignment.centerLeft, padding: EdgeInsets.only(left: 10.0)),
          _symbolTextView(label: '\$', alignment: Alignment.centerRight, padding: EdgeInsets.only(right: 10.0)),
          AnimatedAlign(
            duration: const Duration(milliseconds: 300),
            alignment: context.read<CartViewProvider>().knobAlignment,
            curve: Curves.decelerate,
            child: Container(
              decoration: BoxDecoration(color: ColorConstant.colorBlueDark, borderRadius: BorderRadius.circular(5)),
              margin : const EdgeInsets.only(top:3, bottom : 3,left: 3, right : 3,),
              padding: const EdgeInsets.only(left: 12, right: 12,top: 2, bottom: 2,),
              child:Text(context.read<CartViewProvider>().serviceChargeType,textAlign: TextAlign.center,
                  style : const TextStyle(color: Colors.white,fontSize:12,fontFamily: AppFonts.roboto,)),

            ),
          ),
        ]),
      ),
    );
  }

  /// common switch discount view
  Widget _symbolTextView({String label='',AlignmentGeometry alignment = Alignment.center, required EdgeInsetsGeometry padding}){
    return Padding(
      padding: padding,
      child: Align(
        alignment: alignment,
        child: Text(label, style: TextStyle(color : ColorConstant.colorGrayDark, fontSize: 12, fontFamily: AppFonts.roboto,),),
      ),
    );
  }

  /// add button view
  Widget _addButtonView() {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _chargeController,
      builder: (context, value, child) {
        return GestureDetector(
          onTap: value.text.isEmpty? null : (){
            widget.onButtonPressed();
          },
          child: Container(
            margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 10.0 : 8.0,
             left: 15, right: 15,),
            decoration: BoxDecoration(
              color: value.text.isEmpty ?
              ColorConstant.colorBlueLight_16 : ColorConstant.colorBlueDark,
              borderRadius: BorderRadius.all(
                Radius.circular(4.0),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(isBigScreenResolution(context) ? 8.0 : 7.0),
              child: Text(
                AddServiceChargeViewString.add.toUpperCase(),
                textAlign: TextAlign.center,
                style: AppTextStyle.mediumTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 10.0 : 9.0,
                  letterSpacing: 1.15,
                  color:  value.text.isEmpty ?
                  ColorConstant.colorBlueDark : ColorConstant.colorThemeWhite,
                ),
              ),
            ),
          ),
        );
      },
    );

  }

}
