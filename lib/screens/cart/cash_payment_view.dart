import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gr8tables_server_manager/providers/payment/payment_provider.dart';
import 'package:gr8tables_server_manager/utils/app_utils.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/currency_text_formatter.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/cart/cart_promotions/cart_discount.dart';
import '../../models/cart/cart_promotions/cart_promocode.dart';
import '../../models/discount/promotion_apply_on.dart';
import '../../models/loyalty/reward_types.dart';
import '../../my_app.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';

class CashPaymentView extends StatefulWidget {
  const CashPaymentView({super.key});

  @override
  State<CashPaymentView> createState() => _CashPaymentViewState();
}

class _CashPaymentViewState extends State<CashPaymentView> {
  bool _isPopScreenCalled = false;
  FocusNode _amountTextFieldFocus = FocusNode();

  ScrollController _scrollController = ScrollController();

  final TextEditingController _receivedAmountController = TextEditingController();

  /// button border radius
  BorderRadiusGeometry get _buttonBorderRadius =>
      BorderRadius.circular(isBigScreenResolution(context) ? 6 : 4);


  @override
  void initState() {

    _amountTextFieldFocus.addListener(() {
      _onFocusChange();
    });
    context.read<PaymentProvider>();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if(_isPopScreenCalled){
      return;
    }
    _isPopScreenCalled = true;
    final cartData = context.watch<CartViewProvider>().cartData;

    if (cartData == null && Navigator.canPop(context)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debugPrint("CashPaymentView - didChangeDependencies");
        Navigator.pop(context);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: CustomAppBar(
          context: context,
          appBarTitleText: NavigationString.menuCash,
        ),
        body: Consumer<CartViewProvider>(builder: (context, data, child) {

          if(data.cartData == null) {
            return SizedBox.shrink();
          }
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children:[
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    padding: EdgeInsets.only(
                      top: isBigScreenResolution(context)? 10:8,
                      left: isBigScreenResolution(context)? 10:8,
                      right: isBigScreenResolution(context)? 10:8,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Items
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: Text(
                                PaymentString.totalItems.replaceAll("%d", "${data.cartData!.itemCount}"),
                                style: AppTextStyle.smallTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                                  letterSpacing: 0.3,
                                  color: ColorConstant.colorBlueDark,
                                ),
                              ),
                            ),
                            Text(
                              NumberFormat.simpleCurrency().format(data.cartData!.total),
                              style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context) ? 22.0 : 20.0,
                                  color: ColorConstant.colorBlueDark
                              ),
                            ),
                          ],
                        ),
                        _verticalSpace5,
                        _divider(),
                        // Sub total
                        _subTotalLabelWithValueRow(label: PaymentString.cartTotalBoxSubTotal, value: data.cartData!.subtotal!,),
                        //_showCartPromotions(data,disableAction: true, alterColor : true),
                        if(context.watch<CartViewProvider>().cartData!.cartPromoCode != null)...[
                           _promoCodeView(label: _promoCodeText()),
                        ],
                        if(context.read<CartViewProvider>().cartData!.cartDiscount != null)...[
                           _presetCodeView(label: _presetCodeText()),
                        ],
                        if(context.read<CartViewProvider>().cartData!.cartCustomDiscount!.isNotEmpty)...[
                          ...context.read<CartViewProvider>().cartData!.cartCustomDiscount!.map((customDiscount) {
                               return _customDiscountView(label: _customDiscountText(appliedValue: customDiscount.appliedValue!)
                            );
                          }),
                        ],

                        SizedBox(
                          height: isBigScreenResolution(context) ? 4.0 : 2.0,
                        ),
                        // New sub total
                        Row(
                          mainAxisAlignment:
                          MainAxisAlignment.start,
                          crossAxisAlignment:
                          CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: Text(
                                PaymentString.newSubTotal,
                                style: AppTextStyle.smallTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context)
                                      ? 16.0
                                      : 14.0,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ),
                            Text(
                              NumberFormat.simpleCurrency().format(data.subtotalAfterPromotions),
                              style: AppTextStyle.largeTextStyle.copyWith(
                                fontSize: isBigScreenResolution(context)
                                    ? 16.0
                                    : 14.0,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: isBigScreenResolution(context) ? 4.0 : 2.0,
                        ),
                        if(data.cartData!.paidAmount! > 0)...[
                          // Tax
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.start,
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Text(
                                  PaymentString.tax,
                                  style: AppTextStyle.smallTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context)
                                        ? 16.0
                                        : 14.0,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              ),
                              Text(
                                NumberFormat.simpleCurrency().format(data.cartData!.tax),
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context)
                                      ? 16.0
                                      : 14.0,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ],
                          ),
                          // Paid amount
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.start,
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Text(
                                  PaymentString.paidAmount,
                                  style: AppTextStyle.smallTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context)
                                        ? 16.0
                                        : 14.0,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              ),
                              Text(
                                NumberFormat.simpleCurrency().format(data.cartData!.paidAmount),
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context)
                                      ? 16.0
                                      : 14.0,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ],
                          ),
                          // Due amount
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.start,
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Text(
                                  PaymentString.dueAmount,
                                  style: AppTextStyle.smallTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context)
                                        ? 16.0
                                        : 14.0,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              ),
                              Text(
                                NumberFormat.simpleCurrency().format(data.cartData!.dueAmount),
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context)
                                      ? 16.0
                                      : 14.0,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ],
                          ),
                        ]else...[
                          // Tax
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.start,
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Text(
                                  PaymentString.tax,
                                  style: AppTextStyle.smallTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context)
                                        ? 16.0
                                        : 14.0,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              ),
                              Text(
                                NumberFormat.simpleCurrency().format(data.cartData!.tax),
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context)
                                      ? 16.0
                                      : 14.0,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ],
                          ),
                          // Due amount
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.start,
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Text(
                                  PaymentString.dueAmount,
                                  style: AppTextStyle.smallTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context)
                                        ? 16.0
                                        : 14.0,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              ),
                              Text(
                                NumberFormat.simpleCurrency().format(data.cartData!.dueAmount),
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context)
                                      ? 16.0
                                      : 14.0,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ],
                          ),
                        ],
                        SizedBox(
                          height: isBigScreenResolution(context) ? 4.0 : 2.0,
                        ),
                        // Amount received
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: isBigScreenResolution(context)
                                ? 16.0
                                : 14.0,
                            vertical: isBigScreenResolution(context)
                                ? 10.0
                                : 9.0,
                          ),
                          decoration: BoxDecoration(
                            //color: ColorConstant.colorBlueLight_16,
                            borderRadius: BorderRadius.circular(
                              8.0,
                            ),
                            border: Border.all(
                              color: ColorConstant.colorBlueLight_8,
                              width: 2.0,
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                PaymentString.amountReceived,
                                style: AppTextStyle.smallTextStyle.copyWith(
                                  letterSpacing: 0.3,
                                  fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                                  color: ColorConstant.colorBlueDark,
                                ),
                              ),
                              Flexible(child: _amountReceivedTextField()),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
              _bottomButtonView(),
            ],
          );
        }),
      ),
    );
  }

  /// Amount received text field
  Widget _amountReceivedTextField() {
    return TextField(
      cursorColor: ColorConstant.colorBlueDark,
      focusNode: _amountTextFieldFocus,
      textAlign: TextAlign.right,
      showCursor: true,
      keyboardType: TextInputType.number,
      inputFormatters: <TextInputFormatter>[
        CurrencyTextInputFormatter(
            locale: 'en_US', decimalDigits: 2, name: '\$', symbol: '\$'),
      ],
      controller: _receivedAmountController,
      style: AppTextStyle.largeTextStyle.copyWith(
        fontSize: isBigScreenResolution(context) ? 28.0 : 20.0,
        letterSpacing: 0.3,
        color: ColorConstant.colorBlueDark,
      ),
      maxLines: 1,
      decoration: InputDecoration(
        filled: true,
        isDense: true,
        fillColor: Colors.transparent,
        contentPadding: EdgeInsets.zero,
        hintText: "\$0.00",
        hintStyle: AppTextStyle.largeTextStyle.copyWith(
          fontSize: isBigScreenResolution(context) ? 28.0 : 20.0,
          letterSpacing: 0.3,
          color: ColorConstant.colorBlueLight_50,
        ),
        prefixIconConstraints: BoxConstraints(minWidth: 0, minHeight: 0),
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.zero,
        ),
      ),
    );
  }

  void _onFocusChange() {
    debugPrint("Focus amount: ${_amountTextFieldFocus.hasFocus.toString()}");
    Future.delayed(Duration(milliseconds: 600), () {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_amountTextFieldFocus.hasFocus
            ? _scrollController.position.maxScrollExtent
            : _scrollController.position.minScrollExtent);
      }
    });
  }


  /// vertical spacing of 5 pixel
  Widget get _verticalSpace5 =>  SizedBox(height: isBigScreenResolution(context)? 5: 4,);

  /// Divider view
  Widget _divider() {
    return Divider(color: ColorConstant.colorBlueLight_16, thickness: 1.3, height: 0,);
  }

  /// row of subtotal label and their values
  Widget _subTotalLabelWithValueRow({String label='', num value = 0, Color color = ColorConstant.colorBlueDark}){
    return Container(
      margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 5 : 4,),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: Text(
              label,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                letterSpacing: 0.3,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            NumberFormat.simpleCurrency().format(value),
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
              letterSpacing: 0.3,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// promo code view
  Widget _promoCodeView({String label = '', String value = ''}) {
    CartPromoCode? cartPromoCode = context.read<CartViewProvider>().cartData?.cartPromoCode;
    bool onCart = cartPromoCode?.applyOn!.toLowerCase() == PromotionApplyOn.cart.name;
    bool alterColor = true;

    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Text(
              label,
              textAlign: TextAlign.start,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
        ],
      ),
    );
  }

  /// promo code view
  Widget _presetCodeView({String label = '', String value = ''}) {
    CartDiscount? cartDiscount = context.read<CartViewProvider>().cartData?.cartDiscount;
    bool onCart = cartDiscount?.applyOn!.toLowerCase() == PromotionApplyOn.cart.name;
    bool alterColor = true;

    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Text(
              label,
              textAlign: TextAlign.start,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
        ],
      ),
    );
  }


  // custom discount view
  Widget _customDiscountView({String label = '', String value = ""}) {
    bool alterColor = true;

    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            Icons.shopping_cart_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Text(
              label,
              textAlign: TextAlign.start,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
        ],
      ),
    );
  }

  /// Pro code label
  _promoCodeText(){
    String label = CartViewString.code;
    // String? applyOn = context.read<CartViewProvider>().cartData!.cartPromoCode?.applyOn?.replaceAll("", "\u{200B}");
    String? code = context.read<CartViewProvider>().cartData!.cartPromoCode?.code;
    num? value = context.read<CartViewProvider>().cartData!.cartPromoCode?.appliedValue;
    return ("$label $code (-\$${roundDouble(value!)})");
  }

  /// Preset code label
  _presetCodeText(){
    String label = CartViewString.preset;
    String? code = context.read<CartViewProvider>().cartData!.cartDiscount?.title;
    num? value = context.read<CartViewProvider>().cartData!.cartDiscount?.appliedValue;
    return ("$label $code (-\$${roundDouble(value!)})");
  }

  /// Custom discount code label
  _customDiscountText({required num appliedValue}){
    String label = CartViewString.custom;
    String title = CartViewString.discount;
    return ("$label $title (-\$${roundDouble(appliedValue)})");
  }

  /// submit order button view
  Widget _bottomButtonView() {
    return const _BottomButtonView();
  }

  /// cart payment api
  Future<void> _callCartPayment(
      {required String paymentType,
        required num cartAmount,
        String cardType = 'other'}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context.read<PaymentProvider>().cartPayment(
            paymentType: paymentType,
            cartAmount: cartAmount,
            cardType: cardType);
        hideLoaderDialog(context);

        // Update cartData
        if(context.read<CartViewProvider>().cartData != null) {
          context.read<CartViewProvider>().getCart(id: context.read<CartViewProvider>().cartData!.id!);
        }
        Navigator.pop(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      } catch (error, stacktrace) {
        debugPrint("stacktrace caught: $stacktrace");
        debugPrint('Getting error test: ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(
            error: 'Something went wrong. Please check the payment device.');
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  @override
  void dispose() {

    super.dispose();
  }
}

class _BottomButtonView extends StatelessWidget {
  const _BottomButtonView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CartViewProvider>(
      builder: (context, data, child) {
        if (data.cartData == null) {
          // Always return a Widget, never fallthrough.
          return SizedBox.shrink();
        } else {
          debugPrint(':: else button view :: ${data.cartData!.status}');
          final cashPaymentViewState =
              context.findAncestorStateOfType<_CashPaymentViewState>();
          return Container(
            margin: EdgeInsets.only(
              bottom: isBigScreenResolution(context) ? 16.0 : 14.0,
              left: isBigScreenResolution(context) ? 16.0 : 14.0,
              right: isBigScreenResolution(context) ? 16.0 : 14.0,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      if (cashPaymentViewState == null) return;
                      String _convertToAmount = cashPaymentViewState
                          ._receivedAmountController.text
                          .toString()
                          .replaceAll('\$', '')
                          .replaceAll(",", "")
                          .trim();
                      if (_convertToAmount.isNotEmpty) {
                        num _cartAmount = double.parse(_convertToAmount);
                        debugPrint("Order Amount \\$_cartAmount");
                        if (_cartAmount > 0) {
                          await cashPaymentViewState._callCartPayment(
                            paymentType: PaymentType.cash,
                            cartAmount: _cartAmount,
                          );
                        }
                      } else {
                        showErrorDialog(error: PaymentString.errorOrderAmount);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          vertical: isBigScreenResolution(context) ? 24 : 22,
                          horizontal: isBigScreenResolution(context) ? 36 : 34),
                      decoration: BoxDecoration(
                        color: ColorConstant.colorBlueDark,
                        borderRadius:
                            cashPaymentViewState?._buttonBorderRadius ??
                                BorderRadius.circular(4),
                      ),
                      child: Text(
                        PaymentString.continueLabel,
                        style: AppTextStyle.largeTextStyle.copyWith(
                          fontSize:
                              isBigScreenResolution(context) ? 14.0 : 12.0,
                          color: ColorConstant.colorThemeWhite,
                          letterSpacing: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }
}