
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../models/cart/cart_users/cart_user_data.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../utils/app_utils.dart';

class CartUserSelectionContent extends StatefulWidget {

  Function() onAddButtonPressed, onDoneButtonPressed;

  CartUserSelectionContent({super.key, required this.onAddButtonPressed,
    required this.onDoneButtonPressed});

  @override
  State<CartUserSelectionContent> createState() => _CartUserSelectionContentState();
}

class _CartUserSelectionContentState extends State<CartUserSelectionContent> {

  late TextEditingController _userNameController;

  @override
  void initState() {
    _userNameController = TextEditingController()..text ='';
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(isBigScreenResolution(context)? 15 : 13,),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 8.0 : 6.0,),
                  child: Text(
                    CartUserSelectionContentString.addItemTo,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.largeTextStyle.copyWith(
                      color: ColorConstant.colorBlueDark,
                      fontSize: isBigScreenResolution(context) ? 18 : 16,
                      letterSpacing: 0.15,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: (){
                  if(Navigator.canPop(context)){
                    Navigator.pop(context);
                  }
                },
                child: Icon(Icons.close,size: isBigScreenResolution(context) ? 24.0 : 22.0,
                  color: ColorConstant.colorBlueDark,),
              ),
            ],
          ),
          Consumer<CartViewProvider>(
            builder: (context, data, child) {
              if(data.cartUserList.isNotEmpty) {
                return _cartUserListView(data);
              }
              return Container();
            },
          ),
          _addGuestUserView(),
          _doneButtonView(),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _userNameController.dispose();
    super.dispose();
  }
  /// cart user list view
  Widget _cartUserListView(CartViewProvider providerData){
    return Flexible(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
             ...providerData.cartUserList.map((e) =>
                 _cartUserListItem(e,providerData),),
          ],
        ),
      ),
    );
  }

  /// cart user list item
  Widget _cartUserListItem(CartUserData data, CartViewProvider providerData) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstant.colorBlueLight_8,
        borderRadius: BorderRadius.circular(4.0,),
      ),
      padding: const EdgeInsets.only(left: 7.0,top: 7.0,bottom: 7.0,),
      margin: const EdgeInsets.only(bottom: 5.0,),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if(data.userType!.toLowerCase() == 'guest') ...[
            shapeRound(Center(
              child: Text( getInitials(data.name!.trim()), style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
                color: ColorConstant.colorBlueDark,
              ),),),backColor: ColorConstant.colorOrangeLight,),
          ],

          if(data.userType!.toLowerCase() != 'guest')...[
            shapeRound(Center(
              child: Image.asset( AssetIcons.guestIcon, width: 22.0, color:
              ColorConstant.colorBlueDark,),),backColor:ColorConstant.colorOrangeLight,),
          ],

           const SizedBox(width: 7.0,),
           Expanded(
             child: Text(data.name!, style: AppTextStyle.smallTextStyle.copyWith(
               fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
               color: ColorConstant.colorBlueDark,
             ),),
           ),
          Radio(
            visualDensity: const VisualDensity(vertical:-3.0,horizontal: -4.0),
            value: data.id!,
            groupValue: providerData.selectedCartUser,
            onChanged: (value) {
              debugPrint(':: value =>> $value::');
              providerData.selectCartUser(value as int);
            },
          ),
        ],
      ),
    );
  }

  /// add guest user view
  Widget _addGuestUserView(){
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _userNameController,
      builder: (context, value, child) {
         return Container(
           decoration: BoxDecoration(
             border: Border.all(color:ColorConstant.colorBlueLight_16,
               width: 0.5,),
           ),
           padding: const EdgeInsets.symmetric(horizontal: 7,),
           child: Row(
             mainAxisSize: MainAxisSize.min,
             mainAxisAlignment: MainAxisAlignment.start,
             children: [
                  Expanded(
                    child: TextField(
                      style: AppTextStyle.smallTextStyle.copyWith(
                        color: ColorConstant.colorBlueDark,
                        fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                        letterSpacing: 0.5,
                      ),
                      controller: _userNameController,
                      onChanged: (value){
                        context.read<CartViewProvider>().setGuestUser(value);
                      },
                      decoration: InputDecoration(
                        filled: false,
                        border: InputBorder.none,
                        hintText: CartUserSelectionContentString.addGuest,
                        contentPadding: EdgeInsets.zero,
                        hintStyle: AppTextStyle.smallTextStyle.copyWith(
                          letterSpacing: 0.5,
                          fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                          color: ColorConstant.colorBlueLight_32,
                        ),
                      ),
                      onTapOutside: (event){
                        FocusManager.instance.primaryFocus?.unfocus();
                      },
                   ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: value.text.isEmpty ? null : (){
                      widget.onAddButtonPressed();
                      _userNameController.clear();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5,
                      vertical: 7,),
                      child: Text(
                          CartUserSelectionContentString.add.toUpperCase(),
                          style: AppTextStyle.smallTextStyle.copyWith(
                          color: ColorConstant.colorBlueDark,
                          fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ),

             ],
           ),
         );
      },
    );
  }

  /// done button view
  Widget _doneButtonView(){
    return GestureDetector(
      onTap: /*!context.watch<CartViewProvider>().checkAnyCartUserSelected() ? null : */(){
        widget.onDoneButtonPressed();
      },
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 16.0 : 14.0,),
        decoration: BoxDecoration(
          color: /*!context.watch<CartViewProvider>().checkAnyCartUserSelected() ?
          ColorConstant.colorBlueLight_16 :*/ ColorConstant.colorBlueDark,
          borderRadius: const BorderRadius.all(
            Radius.circular(4.0),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(isBigScreenResolution(context) ? 14.0 : 12.0),
          child: Text(
            CartUserSelectionContentString.done.toUpperCase(),
            textAlign: TextAlign.center,
            style: AppTextStyle.mediumTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
              letterSpacing: 1.15,
              color:  /*!context.watch<CartViewProvider>().checkAnyCartUserSelected() ?
              ColorConstant.colorBlueDark :*/ ColorConstant.colorThemeWhite,
            ),
          ),
        ),
      ),
    );
  }

}
