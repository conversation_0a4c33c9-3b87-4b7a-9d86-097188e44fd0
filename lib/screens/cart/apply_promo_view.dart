import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../providers/discount/discount_provider.dart';
import '../../utils/app_utils.dart';

class ApplyPromoView extends StatefulWidget {
  final Function(String promoCode) onButtonPressed;
  ApplyPromoView({super.key, required this.onButtonPressed});

  @override
  State<ApplyPromoView> createState() => _ApplyPromoViewState();
}

class _ApplyPromoViewState extends State<ApplyPromoView> {
  // for not lose focus when value change
  static final _promoCodeFormKey = GlobalKey<FormFieldState<String>>();
  late TextEditingController _promoCodeController;
  late FocusNode _focusNodePromoCode;
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    _promoCodeController = TextEditingController();
    _focusNodePromoCode = FocusNode();
    // Add a listener to the FocusNode to hide error message when the TextField is focused
    _focusNodePromoCode.addListener(() {
      _onFocusChange();
    });
    super.initState();
  }

  @override
  void dispose() {
    _promoCodeController.dispose();
    _focusNodePromoCode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller:  _scrollController,
      child: Padding(
        padding: EdgeInsets.all(isBigScreenResolution(context)? 14 : 12,),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 8.0 : 6.0,),
                    child: Text(
                      ApplyPromoViewString.promoCode,
                      textAlign: TextAlign.center,
                      style: AppTextStyle.largeTextStyle.copyWith(
                        color: ColorConstant.colorBlueDark,
                        fontSize: isBigScreenResolution(context) ? 16 : 14,
                        letterSpacing: 0.15,
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: (){
                   // context.read<CartViewProvider>().resetPromoDialog();
                    if(Navigator.canPop(context)){
                      Navigator.pop(context);
                    }
                  },
                  child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                    color: ColorConstant.colorBlueDark,),
                ),
              ],
            ),

            Container(
              margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 10.0 : 8.0,
                bottom: isBigScreenResolution(context)? 4: 2,),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(child: _promoCodeInputView()),
                ],
              ),
            ),

            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(child: _addButtonView()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// promo code input view
  Widget _promoCodeInputView() {
    return   Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if(context.watch<DiscountProvider>().hasPromoCodeInputError) ...[
          Container(
            decoration: BoxDecoration(color: ColorConstant.errorColorLight,borderRadius: BorderRadius.circular(4,)),
            padding: const EdgeInsets.all(10.0,),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                const SizedBox(width: 10,),
                Icon(Icons.info_sharp, color: ColorConstant.errorColorDark, size: 18,),
                const SizedBox(width: 10,),
                Text(context.watch<DiscountProvider>().promoCodeErrorMsg,maxLines: 1, textAlign: TextAlign.center, style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                  color:  ColorConstant.errorColorDark,
                ),),
              ],
            ),
          ),  SizedBox(height: isBigScreenResolution(context) ? 30 : 20,)
        ],
        Container(
          child: TextField(
              controller: _promoCodeController,
              keyboardType:TextInputType.text,
              maxLines: 1,
              autofocus: false,
              key: _promoCodeFormKey,
              focusNode: _focusNodePromoCode,
              style: AppTextStyle.largeTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                letterSpacing: 0.3,
                color: ColorConstant.colorBlueDark,
              ),
              cursorColor: ColorConstant.colorBlueDark,
              onTap: () {
                context.read<DiscountProvider>().setPromoCodeInputError(false, '');
              },
              decoration: InputDecoration(
                filled: true,
                isDense: true,
                border: _outLineBorder,
                focusedBorder: _outLineBorder,
                errorBorder: _outLineBorder,
                enabledBorder: _outLineBorder,
                fillColor: ColorConstant.colorThemeWhite,
                contentPadding: EdgeInsets.only(left: 15,right: 0,top: isBigScreenResolution(context) ? 20.0 :15.0,bottom: isBigScreenResolution(context) ?20.0 :15.0),
                hintText: ApplyPromoViewString.promoCodeHintLabel,
                hintStyle: AppTextStyle.largeTextStyle.copyWith(
                  color: ColorConstant.colorBlueLight_16,
                  fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                ),
              )
          ),
        ),
      ],
    );
  }

  /// add button view
  Widget _addButtonView() {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _promoCodeController,
      builder: (context, value, child) {
        return GestureDetector(
          onTap: value.text.isEmpty? null : (){
            widget.onButtonPressed(value.text);
          },
          child: Container(
            margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 10.0 : 8.0,
              left: 15, right: 15,),
            decoration: BoxDecoration(
              color: value.text.isEmpty ?
              ColorConstant.colorBlueLight_16 : ColorConstant.colorBlueDark,
              borderRadius: BorderRadius.all(
                Radius.circular(4.0),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(isBigScreenResolution(context) ? 8.0 : 7.0),
              child: Text(
                ApplyPromoViewString.apply,
                textAlign: TextAlign.center,
                style: AppTextStyle.mediumTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 10.0 : 9.0,
                  letterSpacing: 1.15,
                  color:  value.text.isEmpty ?
                  ColorConstant.colorBlueDark : ColorConstant.colorThemeWhite,
                ),
              ),
            ),
          ),
        );
      },
    );

  }

  void _onFocusChange() {
    Future.delayed(Duration(seconds: 1),() {
      debugPrint("promo code Focus: ${_focusNodePromoCode.hasFocus.toString()}");
      if (_scrollController.hasClients)
        _scrollController.jumpTo(_focusNodePromoCode.hasFocus ? _scrollController.position.maxScrollExtent : _scrollController.position.minScrollExtent);
    });
  }

  /// input decoration outline border
  get _outLineBorder => OutlineInputBorder(
    borderSide: BorderSide(
      color: ColorConstant.colorBlueLight_8,
      width: 1,
    ),
    borderRadius: BorderRadius.circular(isBigScreenResolution(context) ? 6 : 4,),
  );


}
