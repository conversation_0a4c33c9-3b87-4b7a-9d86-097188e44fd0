import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';
import 'package:provider/provider.dart';
import '../../constants/app_color.dart';
import '../../constants/app_dimens.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/discount/cart_discount_response.dart';
import '../../models/discount/discount_response.dart';
import '../../my_app.dart';
import '../../providers/discount/discount_provider.dart';
import '../../utils/app_utils.dart';

class ApplyPresetDiscountView extends StatefulWidget {
  final Function(String promoCode) onButtonPressed;

  ApplyPresetDiscountView({super.key, required this.onButtonPressed});

  @override
  State<ApplyPresetDiscountView> createState() =>
      _ApplyPresetDiscountViewState();
}

class _ApplyPresetDiscountViewState extends State<ApplyPresetDiscountView> {
  @override
  void initState() {
    debugPrint("ApplyPresetDiscountView initState()");
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context
          .read<DiscountProvider>()
          .setOrder(context.read<CartViewProvider>().cartData);
    });
    _retrieveDiscounts();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("ApplyPresetDiscountView build()");
    return Consumer<DiscountProvider>(
        builder: (context, discountProvider, child) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if(context.watch<DiscountProvider>().discountList.isNotEmpty)...[
            Align(
              alignment: Alignment.bottomRight,
              child: Padding(
                padding: const EdgeInsets.only(right: 15,top: 10),
                child: GestureDetector(
                  onTap: (){
                    if(Navigator.canPop(context)){
                      Navigator.pop(context);
                    }
                  },
                  child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                    color: ColorConstant.colorBlueDark,),
                ),
              ),
            ),
          ],
          if(context.watch<DiscountProvider>().hasDiscount)...[
            if(context.watch<DiscountProvider>().preSetDiscountErrorMsg.isNotEmpty)...[
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 10,),
                decoration: BoxDecoration(color: ColorConstant.errorColorLight,borderRadius: BorderRadius.circular(4,)),
                padding: const EdgeInsets.all(10.0,),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    WidthDimens().getWidth10(),
                    Icon(Icons.info_sharp, color: ColorConstant.errorColorDark, size: 18,),
                    WidthDimens().getWidth10(),
                      Expanded(
                        child: Text(
                          context.watch<DiscountProvider>().preSetDiscountErrorMsg,
                          maxLines: 1,
                          textAlign: TextAlign.center, style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                          color:  ColorConstant.errorColorDark,
                         ),
                        ),
                      ),
                  ],
                ),
              ),  SizedBox(height: isBigScreenResolution(context) ? 15 : 10,),
            ],
          ],
          ListView.builder(
            itemBuilder: (context, index) {
              return _rowItemsNew(
                  context.watch<DiscountProvider>().discountList[index]);
            },
            itemCount: context.watch<DiscountProvider>().discountList.length,
            shrinkWrap: true,
          ),
        ],
      );
    });
  }

  Widget _rowItemsNew(DiscountsData? data) {
    debugPrint("refresh _rowItemsNew");
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      padding: EdgeInsets.symmetric(
        vertical: isBigScreenResolution(context) ? 15.0 : 13.0,
        horizontal: isBigScreenResolution(context) ? 13.0 : 12.0,
      ),
      decoration: BoxDecoration(
        color: ColorConstant.colorBlueLight_8,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Row(
            children: [
              Text(
                data!.title!,
                style: AppTextStyle.largeTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 18.0 : 16.0,
                  letterSpacing: 1.15,
                  color: data.status!
                      ? ColorConstant.colorBlueDark
                      : ColorConstant.colorBlueLight_32,
                ),
              ),
            ],
          ),
          Row(
            children: [
              // Type
              _discountPropertyTileView(
                value: _checkDiscountType(data.discountType!),
                label: PresetDiscountString.type,
                enabled: data.status!,
              ),
              // value
              _discountPropertyTileView(
                value: _checkDiscountTypeValue(data.discountType!, data.discount!),
                label: PresetDiscountString.value,
                enabled: data.status!,
              ),
              // apply and remove button
              _buildApplyOrRemoveButton(data),
            ],
          ),
          Row(
            children: [
              _discountPropertyTileView(
                value: data.maxDiscountAmount! != 0
                    ? "\$${data.maxDiscountAmount!.toStringAsFixed(2)}"
                    : "N/A",
                label: PresetDiscountString.maxDiscount,
                enabled: data.status!,
              )
            ],
          ),
          Row(
            children: [
              _discountPropertyTileView(
                value:'',
                label: data.description ?? '',
                enabled: data.status!,
              )
            ],
          ),
        ],
      ),
    );
  }

// Helper widget for Apply or Remove button
  Widget _buildApplyOrRemoveButton(DiscountsData data) {
    return Expanded(
      child: Container(
        constraints: BoxConstraints(
          minHeight: isBigScreenResolution(context) ? 55 : 52,
        ),
        child: !context
                .read<DiscountProvider>()
                .checkIfPresetAlreadyAppliedFromTheList(data.id!)
            ? TextButton(
                onPressed: data.status!
                    ? () async {
                        context.read<DiscountProvider>().setApplied(true, data.id!);
                        _applyDiscount();
                      }
                    : null,
                style: _buildButtonStyle(data.status!),
                child:
                    _buildButtonText(PresetDiscountString.apply, data.status!),
              )
            : TextButton(
                onPressed: () {
                   _removeDiscount();
                },
                style: _buildButtonStyle(false),
                child: _buildButtonText(PresetDiscountString.remove, false),
              ),
      ),
    );
  }

// Helper for button style
  ButtonStyle _buildButtonStyle(bool isEnabled) {
    return TextButton.styleFrom(
      padding: EdgeInsets.symmetric(
        vertical: isBigScreenResolution(context) ? 16 : 14,
        horizontal: 15,
      ),
      backgroundColor: isEnabled
          ? ColorConstant.colorBlueDark
          : ColorConstant.redColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(4.0)),
      ),
    );
  }

// Helper for button text
  Text _buildButtonText(String text, bool isEnabled) {
    return Text(
      text.toUpperCase(),
      style: AppTextStyle.mediumTextStyle.copyWith(
        color: Colors.white,
        fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
        letterSpacing: 1.15,
      ),
    );
  }

  /// check discount type
  _checkDiscountType(String type) {
    if (type.toLowerCase() == 'percentage') {
      return '%';
    }
    if (type.toLowerCase() == 'flat') {
      return '\$';
    }
    if (type.toLowerCase() == 'price_match') {
      return '=';
    }
    return '=';
  }

  /// check discount type value
  _checkDiscountTypeValue(String type, num value) {
    if (type.toLowerCase() == 'percentage') {
      return '${value.toStringAsFixed(2)}%';
    }
    if (type.toLowerCase() == 'flat') {
      return '\$${value.toStringAsFixed(2)}';
    }
    if (type.toLowerCase() == 'price_match') {
      return '\$${value.toStringAsFixed(2)}';
    }
    return '\$${value.toStringAsFixed(2)}';
  }

  /// discount property tile view
  Widget _discountPropertyTileView(
      {String label = '', String value = '', bool enabled = true}) {
    return Expanded(
      child: Row(
        children: [
          Text(
            textAlign: TextAlign.center,
            label.isEmpty?"":"$label: ",
            style: AppTextStyle.smallTextStyle.copyWith(
              color: ColorConstant.colorBlueDark,
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
            ),
          ),
          AutoSizeText(
            maxFontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
            minFontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
            textAlign: TextAlign.center,
            maxLines: 1,
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              color: enabled
                  ? ColorConstant.colorBlueDark
                  : ColorConstant.colorBlueLight_32,
              letterSpacing: 1.15,
            ),
          ),
        ],
      ),
    );
  }

  /// retrieve discounts
  void _retrieveDiscounts() async {
    var hasConnected =
        await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        context.read<DiscountProvider>().reset();
        await context.read<DiscountProvider>().retrieveDiscounts();
        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        hideLoaderDialog(context);
        if (!mounted) return;
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// apply discount
  _applyDiscount() async {
    var hasConnected =
        await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        CartDiscountResponse _cartDiscountResponse =
            await context.read<DiscountProvider>().applyDiscount(
                  discountId: context.read<DiscountProvider>().discountId,
                );
        hideLoaderDialog(context);
        context
            .read<DiscountProvider>()
            .setSuccessMsg(_cartDiscountResponse.message!);

        await context.read<CartViewProvider>()
            .getCart(id: context.read<DiscountProvider>().cartData!.id!);

        // set order
        context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop(true);
        }
      } on UnauthorisedException catch (error) {
        hideLoaderDialog(context);
        if (!mounted) return;
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        print('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        context
            .read<DiscountProvider>()
            .setPreSetDiscountErrorMsg(error.toString());
        _resetErrorMsg();
        return;
      } on BadRequestException catch (error) {
        print('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        context
            .read<DiscountProvider>()
            .setPreSetDiscountErrorMsg(error.toString());
        _resetErrorMsg();
        return;
      } catch (error) {
        print('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        context
            .read<DiscountProvider>()
            .setPreSetDiscountErrorMsg(error.toString());
        _resetErrorMsg();
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove discount
  void _removeDiscount() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        if(null != context.read<DiscountProvider>().cartData) {
          await context.read<DiscountProvider>().removeDiscount(id: context.read<DiscountProvider>().cartData!.cartDiscount!.id!);
        }


        hideLoaderDialog(context);

        if(null != context.read<DiscountProvider>().cartData) {
          /// recall order and save data to local for order
          await context.read<CartViewProvider>().getCart(id:context.read<DiscountProvider>().cartData!.id!);
        }
        context.read<DiscountProvider>().removeCustomDiscountFromItem(context.read<DiscountProvider>().cartData!.cartDiscount!.id!);
        // set order
        await context.read<DiscountProvider>().setOrder(context.read<CartViewProvider>().cartData);
        await context.read<DiscountProvider>().markDiscountAsUnApplied();
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }


  /// reset error message
  _resetErrorMsg() {
    Future.delayed(Duration(milliseconds: 1800), () {
      if (!mounted) return;
      context.read<DiscountProvider>().setPreSetDiscountErrorMsg("");
    });
  }
}
