import 'package:flutter/material.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../utils/app_utils.dart';

class VoidItemConfirmationContent extends StatefulWidget {

  Function(String value) onButtonPressed;
  VoidItemConfirmationContent({super.key, required this.onButtonPressed});

  @override
  State<VoidItemConfirmationContent> createState() => _VoidItemConfirmationContentState();
}

class _VoidItemConfirmationContentState extends State<VoidItemConfirmationContent> {

  late TextEditingController _reasonController;

  @override
  void initState() {
    _reasonController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
     _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(isBigScreenResolution(context)? 24 : 22,),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 8.0 : 6.0,),
                  child: Text(
                    VoidItemConfirmationViewString.header,
                    textAlign: TextAlign.center,
                    style: AppTextStyle.largeTextStyle.copyWith(
                      color: ColorConstant.colorBlueDark,
                      fontSize: isBigScreenResolution(context) ? 20 : 18,
                      letterSpacing: 0.15,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: (){
                  if(Navigator.canPop(context)){
                    Navigator.pop(context);
                  }
                },
                child: Icon(Icons.close,size: isBigScreenResolution(context) ? 24.0 : 22.0,
                  color: ColorConstant.colorBlueDark,),
              ),
            ],
          ),

          Container(
            margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 16.0 : 14.0,
            bottom: isBigScreenResolution(context)? 6: 4,),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                    VoidItemConfirmationViewString.sub_header,
                  textAlign: TextAlign.center,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context) ? 14 : 13,
                  ),
                ),
              ],
            ),
          ),
          _reasonTextField(),
          Row(
           mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(child: _confirmButtonView()),
            ],
          ),
        ],
      ),
    );
  }


  /// reason text field
  Widget _reasonTextField() {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
            color: ColorConstant.colorBlueLight_8,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(isBigScreenResolution(context)? 6: 4,)

      ),
      margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 16.0 : 14.0,),
      padding: EdgeInsets.all(isBigScreenResolution(context) ? 12.0 : 10.0),
      child: TextField(
        maxLines: 3,
        autofocus: false,
        textInputAction: TextInputAction.done,
        cursorColor: ColorConstant.colorBlueDark,
        style:  AppTextStyle.smallTextStyle.copyWith(
          color: ColorConstant.colorBlueDark,
          fontSize: isBigScreenResolution(context) ? 14 : 12,
        ),
        controller: _reasonController,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          contentPadding: EdgeInsets.zero,
          isDense: true,
          hintText: VoidItemConfirmationViewString.voidItemReasonHint,
          hintStyle: AppTextStyle.smallTextStyle.copyWith(
              fontSize: isBigScreenResolution(context)
                  ? 14.0
                  : 12.0,
              color: ColorConstant.colorBlueLight_50),
        ),
        onChanged: (value) {

        },
      ),
    );
  }

  /// confirm button view
  Widget _confirmButtonView() {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _reasonController,
      builder: (context, value, child) {
        return GestureDetector(
           onTap: value.text.isEmpty? null :  (){
             widget.onButtonPressed(value.text.trim());
           },
          child: Container(
            margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 16.0 : 14.0,),
            decoration: BoxDecoration(
              color: value.text.isEmpty ?
              ColorConstant.colorBlueLight_16 : ColorConstant.colorBlueDark,
              borderRadius: BorderRadius.all(
                Radius.circular(4.0),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(isBigScreenResolution(context) ? 16.0 : 14.0),
              child: Text(
                VoidItemConfirmationViewString.voidThisItem.toUpperCase(),
                textAlign: TextAlign.center,
                style: AppTextStyle.mediumTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 14.0 : 13.0,
                  letterSpacing: 1.15,
                  color:  value.text.isEmpty ?
                  ColorConstant.colorBlueDark : ColorConstant.colorThemeWhite,
                ),
              ),
            ),
          ),
        );
      },
    );

  }
}
