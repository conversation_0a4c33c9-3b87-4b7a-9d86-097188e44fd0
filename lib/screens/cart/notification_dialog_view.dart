import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/api_constant.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../providers/homescreen/home_screen_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';

class NotificationDialogView extends StatefulWidget {
  final Function(String promoCode) onButtonPressed;
  final RemoteMessage messageObject;
  NotificationDialogView({super.key, required this.messageObject, required this.onButtonPressed});

  @override
  State<NotificationDialogView> createState() => _NotificationDialogViewState();
}

class _NotificationDialogViewState extends State<NotificationDialogView> {
  // for not lose focus when value change
  static final _promoCodeFormKey = GlobalKey<FormFieldState<String>>();
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller:  _scrollController,
      child: Padding(
        padding: EdgeInsets.all(isBigScreenResolution(context)? 14 : 12,),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 8.0 : 6.0,),
                    child: Text(
                      '${widget.messageObject.notification!.title.toString()}',
                      textAlign: TextAlign.center,
                      style: AppTextStyle.largeTextStyle.copyWith(
                        color: ColorConstant.colorBlueDark,
                        fontSize: isBigScreenResolution(context) ? 16 : 14,
                        letterSpacing: 0.15,
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: (){

                    if(Navigator.canPop(context)){
                      Navigator.pop(context);
                    }
                  },
                  child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                    color: ColorConstant.colorBlueDark,),
                ),
              ],
            ),

            Container(
              margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 10.0 : 8.0,
                bottom: isBigScreenResolution(context)? 4: 2,),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(child: Text(
                    '${widget.messageObject.notification!.body.toString()}',
                    textAlign: TextAlign.center,
                    style: AppTextStyle.mediumTextStyle.copyWith(
                      color: ColorConstant.colorBlueDark,
                      fontSize: isBigScreenResolution(context) ? 16 : 14,
                      letterSpacing: 0.15,
                    ),
                  ),
                  ),
                ],
              ),
            ),

            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if(widget.messageObject.data[ApiConstant.TOPIC_KEY] == NotificationsString.tableActive)...[
                  Container(
                    padding: EdgeInsets.only(bottom: 14.0,),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        InkWell(
                          onTap: () {
                            if(Navigator.canPop(context)){
                              Navigator.pop(context);
                            }
                            _screenNavigation(buildContext: context, message: widget.messageObject);
                          },
                          child: Container(
                            width: isBigScreenResolution(context) ? 170.0 : 140.0,
                            height: 50.0,
                            padding: EdgeInsets.symmetric(
                              vertical: 10.0,
                              horizontal: isBigScreenResolution(context) ? 16.0 : 8.0,
                            ),
                            margin: EdgeInsets.only(
                              top: 16.0,
                            ),
                            child: Center(
                              child: Text(
                                NotificationsString.viewTable,
                                style: AppTextStyle.mediumTextStyle.copyWith(
                                  color: ColorConstant.colorThemeWhite,
                                  fontSize: isBigScreenResolution(context)
                                      ? 14.0
                                      : 12.0,
                                  letterSpacing: 2.0,
                                ),
                              ),
                            ),
                            decoration: BoxDecoration(
                              color: ColorConstant.colorBlueDark,
                              borderRadius: BorderRadius.circular(
                                4.0,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            if(Navigator.canPop(context)){
                              Navigator.pop(context);
                            }
                          },
                          child: Container(
                            width: isBigScreenResolution(context) ? 170.0 : 140.0,
                            height: 50.0,
                            padding: EdgeInsets.symmetric(
                              vertical: 10.0,
                              horizontal: isBigScreenResolution(context) ? 16.0 : 8.0,
                            ),
                            margin: EdgeInsets.only(
                              left: 16.0,
                              top: 16.0,
                            ),
                            child: Center(
                              child: Text(
                                NotificationsString.cancel,
                                style: AppTextStyle.mediumTextStyle.copyWith(
                                  color: ColorConstant.colorThemeWhite,
                                  fontSize: isBigScreenResolution(context)
                                      ? 14.0
                                      : 12.0,
                                  letterSpacing: 2.0,
                                ),
                              ),
                            ),
                            decoration: BoxDecoration(
                              color: ColorConstant.colorRedDark,
                              borderRadius: BorderRadius.circular(
                                4.0,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ]
                else if(widget.messageObject.data[ApiConstant.TOPIC_KEY] == NotificationsString.bills)...[
                  Container(
                    padding: EdgeInsets.only(bottom: 14.0,),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        InkWell(
                          onTap: () {
                            if(Navigator.canPop(context)){
                              Navigator.pop(context);
                            }
                            _screenNavigation(buildContext: context, message: widget.messageObject);
                          },
                          child: Container(
                            width: isBigScreenResolution(context) ? 170.0 : 140.0,
                            height: 50.0,
                            padding: EdgeInsets.symmetric(
                              vertical: 10.0,
                              horizontal: isBigScreenResolution(context) ? 16.0 : 8.0,
                            ),
                            margin: EdgeInsets.only(
                              top: 16.0,
                            ),
                            child: Center(
                              child: Text(
                                NotificationsString.viewOrder,
                                style: AppTextStyle.mediumTextStyle.copyWith(
                                  color: ColorConstant.colorThemeWhite,
                                  fontSize: isBigScreenResolution(context)
                                      ? 14.0
                                      : 12.0,
                                  letterSpacing: 2.0,
                                ),
                              ),
                            ),
                            decoration: BoxDecoration(
                              color: ColorConstant.colorBlueDark,
                              borderRadius: BorderRadius.circular(
                                4.0,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            if(Navigator.canPop(context)){
                              Navigator.pop(context);
                            }
                          },
                          child: Container(
                            width: isBigScreenResolution(context) ? 170.0 : 140.0,
                            height: 50.0,
                            padding: EdgeInsets.symmetric(
                              vertical: 10.0,
                              horizontal: isBigScreenResolution(context) ? 16.0 : 8.0,
                            ),
                            margin: EdgeInsets.only(
                              left: 16.0,
                              top: 16.0,
                            ),
                            child: Center(
                              child: Text(
                                NotificationsString.cancel,
                                style: AppTextStyle.mediumTextStyle.copyWith(
                                  color: ColorConstant.colorThemeWhite,
                                  fontSize: isBigScreenResolution(context)
                                      ? 14.0
                                      : 12.0,
                                  letterSpacing: 2.0,
                                ),
                              ),
                            ),
                            decoration: BoxDecoration(
                              color: ColorConstant.colorRedDark,
                              borderRadius: BorderRadius.circular(
                                4.0,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ] else...[Expanded(child: _okayButtonView()),]
              ],
            ),
          ],
        ),
      ),
    );
  }


  /// add button view
  Widget _okayButtonView() {

    return GestureDetector(
      onTap: (){
        if(Navigator.canPop(context)){
          Navigator.pop(context);
        }
        _screenNavigation(buildContext: context, message: widget.messageObject);
      },
      child: Container(
        margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 10.0 : 8.0,
          left: 15, right: 15,),
        decoration: BoxDecoration(
          color: ColorConstant.colorBlueDark,
          borderRadius: BorderRadius.all(
            Radius.circular(4.0),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(isBigScreenResolution(context) ? 8.0 : 7.0),
          child: Text(
            _buttonLabel(),
            textAlign: TextAlign.center,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 10.0 : 9.0,
              letterSpacing: 1.15,
              color:  ColorConstant.colorThemeWhite,
            ),
          ),
        ),
      ),
    );

  }

  _buttonLabel(){
    // Extract the topicKey from the message data
    String? topicKey = widget.messageObject.data[ApiConstant.TOPIC_KEY];
    debugPrint("topicKey: $topicKey");
    String label="Close";
    if (topicKey != null) {
      if( topicKey == NotificationsString.tableActive){
        label = NotificationsString.viewTable;
      }else if( topicKey == NotificationsString.serverRequest){
        label = NotificationsString.viewTable;
      }
    }
    return label;
  }
}

_screenNavigation({required BuildContext buildContext, required RemoteMessage message}){
  String? topicKey = message.data[ApiConstant.TOPIC_KEY];
  if(topicKey == NotificationsString.tableActive || topicKey == NotificationsString.serverRequest ){
    String? cartId = message.data[ApiConstant.CART_ID_NOTIFICATION];
    String? tableId = message.data[ApiConstant.TABLE_ID_NOTIFICATION];
    try {
      if( cartId != null && tableId != null){
        navigateToCartScreen(buildContext,int.parse(cartId) , int.parse(tableId) );
      }else{
        debugPrint("Not table and cart data found ");
      }
    }catch(e){
      debugPrint("Error cartId and table id data type ");
    }

  }else if(topicKey == NotificationsString.bills){
    String? cartId = message.data[ApiConstant.CART_ID_NOTIFICATION];
    String? tableId = message.data[ApiConstant.TABLE_ID_NOTIFICATION];
    try {
      if( cartId != null && tableId != null){
        navigateToOrderDetailsScreen(buildContext, int.parse(cartId) , int.parse(tableId));
      }else{
        debugPrint("Not table and cart data found ");
      }
    }catch(e){
      debugPrint("Error cartId and table id data type ");
    }
  }
}