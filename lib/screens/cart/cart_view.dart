import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:gr8tables_server_manager/models/cart/cart_promotions/cart_discount.dart';
import 'package:gr8tables_server_manager/models/cart/cart_users/cart_user_data.dart';
import 'package:gr8tables_server_manager/providers/payment/payment_provider.dart';
import 'package:gr8tables_server_manager/screens/cart/apply_promo_view.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:pubnub/pubnub.dart';

import '../../constants/api_constant.dart';
import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../custom_widgets/bottom_sheet_widget.dart';
import '../../custom_widgets/common_widget.dart';
import '../../custom_widgets/dotted_decoration.dart';
import '../../custom_widgets/nav_menu.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/currency_text_formatter.dart';
import '../../helpers/http_response_helper.dart';
import '../../helpers/text_onchange_debounce.dart';
import '../../models/cart/cart_data.dart';
import '../../models/cart/cart_items/cart_item_modifiers.dart';
import '../../models/cart/cart_items/cart_items.dart';
import '../../models/cart/cart_promotions/cart_loyalty.dart';
import '../../models/cart/cart_promotions/cart_promocode.dart';
import '../../models/cart/create_cart_response.dart';
import '../../models/discount/promotion_apply_on.dart';
import '../../models/location/location_model.dart';
import '../../models/loyalty/reward_types.dart';
import '../../models/orders/cart_promotions/cart_promocode_response.dart';
import '../../models/payment_options/other_payment_options.dart';
import '../../models/pubnub/pubnub_order_emit.dart';
import '../../my_app.dart';
import '../../providers/alerts/alerts_provider.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/customer/customer_order_history_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/discount/discount_provider.dart';
import '../../providers/homescreen/bottom_sheet_provider.dart';
import '../../providers/homescreen/home_screen_provider.dart';
import '../../providers/notification/notification_provider.dart';
import '../../providers/pubnub/pubnub_provider.dart';
import '../../pubnub/pubnub_constants.dart';
import '../../pubnub/pubnub_service.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import '../../utils/datetime_utils.dart';
import '../../utils/pref_utils.dart';
import 'add_service_charge_content.dart';
import 'add_tip_content.dart';
import 'discount_option_view.dart';
import 'apply_preset_discount_view.dart';
import 'cart_user_selection_content.dart';
import 'void_item_confirmation_content.dart';

class CartView extends StatefulWidget {

  int cartId, tableId;
  CartView({super.key, required this.cartId,
    required this.tableId});

  @override
  State<CartView> createState() => _CartViewState();
}

class _CartViewState extends State<CartView> {
  late final Debounce _debounce;

  Subscription? _cartSubscription;
  StreamSubscription? _cartStreamSubscription;
  StreamSubscription? _orderStreamSubscription;

  FocusNode _otherPaymentAmountTextFieldFocus = FocusNode();
  late StreamController<bool> _otherPaymentAmountFocusStreamController = StreamController();
  late Stream<bool> _otherPaymentAmountFocusStream = _otherPaymentAmountFocusStreamController.stream.asBroadcastStream();

  ScrollController _otherPaymentAmountScrollController = ScrollController();
  TextEditingController _otherPaymentAmountController = TextEditingController();

   @override
  void initState() {
     _debounce = Debounce(Duration(milliseconds: 200));

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        context.read<CartViewProvider>().resetCartUserSelection();
         if(widget.cartId != -1) {
           _getCart(widget.cartId);
         }else{
           context.read<CartViewProvider>().setCartData(null);
         }
      });

      _otherPaymentAmountTextFieldFocus.addListener(() {
        _onFocusChangeInOtherPaymentAmount();
      });

      _subscribeToCartChannel();

      _pubnubEmitListener();

     super.initState();
  }
  @override
  Widget build(BuildContext context) {

     return SafeArea(
         child:Scaffold(
           appBar: CustomAppBar(
             context: context,
             appBarTitleText: NavigationString.cartView,
             backButtonEnabled: true,
           ),
           body: Stack(
             children: [
               Padding(
                 padding: EdgeInsets.only(
                   left: isBigScreenResolution(context) ? 8 : 6,
                   right: isBigScreenResolution(context) ? 8 : 6,
                   top: isBigScreenResolution(context) ? 8 : 6,
                 ),
                 child: Column(
                   mainAxisSize: MainAxisSize.min,
                   mainAxisAlignment: MainAxisAlignment.start,
                   crossAxisAlignment: CrossAxisAlignment.center,
                   children: [
                     /*if(widget.cartId ==-1)...[
                       Expanded(
                         child: Column(
                           mainAxisSize: MainAxisSize.min,
                           mainAxisAlignment: MainAxisAlignment.center,
                           crossAxisAlignment: CrossAxisAlignment.stretch,
                           children: [
                             Text(CartViewString.noActiveCartYet,
                               textAlign: TextAlign.center,
                               style: AppTextStyle.smallTextStyle.copyWith(
                                 color: ColorConstant.colorBlueLight_50,
                                 fontSize: isBigScreenResolution(context) ? 16 : 14.0,
                                 letterSpacing: 0.3,
                               ),
                             ),
                           ],
                         ),
                       ),
                     ],*/
                     //if(widget.cartId != -1)...[
                       Consumer<CartViewProvider>(
                         builder: (context, data, child) {
                           if(data.cartData != null) {
                             return _cartDataView(data);
                           }
                           if(data.cartData == null) {
                             return Expanded(
                               child: Column(
                                 mainAxisSize: MainAxisSize.min,
                                 mainAxisAlignment: MainAxisAlignment.center,
                                 crossAxisAlignment: CrossAxisAlignment.stretch,
                                 children: [
                                   Text(CartViewString.noActiveCartYet,
                                     textAlign: TextAlign.center,
                                     style: AppTextStyle.smallTextStyle.copyWith(
                                       color: ColorConstant.colorBlueLight_50,
                                       fontSize: isBigScreenResolution(context) ? 16 : 14.0,
                                       letterSpacing: 0.3,
                                     ),
                                   ),
                                 ],
                               ),
                             );
                           }
                           return Container();
                         },
                       ),
                    // ],
                   ],
                 ),
               ),
               if(context.watch<BottomSheetProvider>().showDialogCart)...[
                 Align(
                   alignment: Alignment.bottomCenter,
                   child: _handleBottomSheetMenu(),
                 ),
               ],
             ],
           ),
           bottomNavigationBar: _handleNavigationBar(),
           // bottomNavigationBar: customBottomAppBar(
           //   context: context, rightLabelValue: context.watch<CartViewProvider>().getLabelForBottomMenuRightOption(),
           //   rightButtonPressed: (){
           //     if(context.read<CartViewProvider>().actionVoidItem){
           //       debugPrint('items to void ${context.read<CartViewProvider>().itemsAvailableForVoid}');
           //
           //        if(context.read<CartViewProvider>().allowToVoidItem()) {
           //          /// show reason dialog
           //          _voidItemConfirmationDialog();
           //        }else {
           //          showSnackBar(CartViewString.errorPleaseSelectItemToVoid);
           //        }
           //       return;
           //     }
           //     CartData?  cartData = context.read<CartViewProvider>().cartData;
           //     if(cartData != null) {
           //       final cartStatus = cartData.status!.toLowerCase();
           //       // if (cartStatus == OrderStatus.placed ||
           //       //     cartStatus == OrderStatus.ready ||
           //       //     cartStatus == OrderStatus.outForDelivery) {
           //       //   return;
           //       // }
           //       if(cartData.platform!.toLowerCase() == ConstantString.platformWeb){
           //         if(cartStatus == OrderStatus.placed ||
           //             cartStatus == OrderStatus.ready ||
           //             cartStatus == OrderStatus.outForDelivery) {
           //           return ;
           //         }
           //         /*if(cartStatus == OrderStatus.pending) {
           //           return ;
           //         }*/
           //       }
           //     }
           //     _cartUserSelectionDialog();
           //
           //   }, leftLabelValue: context.watch<CartViewProvider>().getLabelForBottomMenuLeftOption(),
           //   leftButtonPressed: (){
           //     if(context.read<CartViewProvider>().getLabelForBottomMenuLeftOption() == BottomMenuString.send_to_kitchen){
           //       _itemSendToKitchen();
           //     }
           //   },
           //   actionButtonPressed: widget.cartId == -1?null:(){
           //     if(context.read<BottomSheetProvider>().showDialogCart){
           //       context.read<BottomSheetProvider>().setDialogForCartPage(false);
           //     }else{
           //       context.read<BottomSheetProvider>().setDialogForCartPage(true);
           //     }
           //
           //   },
           //   enableBottomSheet: widget.cartId == -1?false:context.watch<BottomSheetProvider>().showDialogCart
           // ),
         ),
     );
  }

  /// cart data view
   Widget _cartDataView(CartViewProvider data) {
     return Flexible(
       child: Column(
         mainAxisSize: MainAxisSize.min,
         mainAxisAlignment: MainAxisAlignment.start,
         children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                 mainAxisAlignment: MainAxisAlignment.start,
                children: [
                     _commonLabelView('#${data.cartData!.id!.toString()}', textAlign: TextAlign.start,),
                     if(data.cartData!.orderReceiveMethod!.toLowerCase() == OrderReceiveMethod.dineIn)...[
                       Expanded(child :_commonLabelView(data.cartData!.table!.label!, textAlign: TextAlign.center,),),
                     ],
                  Expanded(child :_commonLabelView(DateTimeUtils.utcTimeToConvertLocalTime(
                      data.cartData!.platform == ConstantString.platformWeb
                          ? data.cartData!.submittedAt?? 'n/a'
                          : data.cartData!.createdAt ?? 'n/a',
                      formattedString: DateTimeUtils
                          .dd_mm_yyyy_hh_mm_a), textAlign: TextAlign.center,),),
                  _commonLabelView(NumberFormat.simpleCurrency().format(data.cartData!.total!,),textAlign: TextAlign.end,),
                ],
              ),
              _verticalSpacing7,
              _divider,
             _verticalSpacing7,
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Flexible(child: _pendingOrderView(data),),
                      _verticalSpacing7,
                      Flexible(child: _submittedOrderView(data),),
                      Container(
                        margin: EdgeInsets.symmetric(vertical: _equalSizeMargin14),
                        child: Row(
                          children: [
                            // Void item button
                            if (context.read<CartViewProvider>().showVoidItemsButton())...[
                              Expanded(
                                child: InkWell(
                                  onTap: () {
                                    if (context.read<CartViewProvider>().itemsAvailableForVoid.isNotEmpty) {
                                      context.read<CartViewProvider>().startStopVoidItem();
                                    } else {
                                      showSnackBar(CartViewString.errorNoItemsToVoid);
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: isBigScreenResolution(context) ? 12 : 11,
                                    ),
                                    decoration: BoxDecoration(
                                      color: ColorConstant.colorBlueDark,
                                      borderRadius: BorderRadius.circular(isBigScreenResolution(context) ? 6 : 4),
                                    ),
                                    child: Text(
                                      context.watch<CartViewProvider>().actionVoidItem
                                                    ? BottomSheetString.clearAllItem
                                                    : BottomSheetString.voidItem,
                                      style: AppTextStyle.largeTextStyle.copyWith(
                                        fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                                        color: ColorConstant.colorThemeWhite,
                                        letterSpacing: 1.5,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ],

                            // Enter reason button
                            if (context.read<CartViewProvider>().showVoidItemsButton() && context.watch<CartViewProvider>().actionVoidItem)...[
                              SizedBox(width: 10),
                              Expanded(
                                child: InkWell(
                                  onTap: context.read<CartViewProvider>().allowToVoidItem()==false? null : () {
                                    if (context.read<CartViewProvider>().allowToVoidItem()) {
                                      // Show reason dialog
                                      _voidItemConfirmationDialog();
                                    } else {
                                      showSnackBar(CartViewString.errorPleaseSelectItemToVoid);
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical:isBigScreenResolution(context) ? 12 : 11,
                                    ),
                                    decoration: BoxDecoration(
                                      color: context.read<CartViewProvider>().allowToVoidItem()?
                                             ColorConstant.colorBlueDark: ColorConstant.colorBlueLight_16,
                                      borderRadius: BorderRadius.circular(isBigScreenResolution(context) ? 6 : 4),
                                    ),
                                    child: Text(
                                      BottomSheetString.enterReason,
                                      style: AppTextStyle.largeTextStyle.copyWith(
                                        fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                                        color: context.read<CartViewProvider>().allowToVoidItem()?
                                                ColorConstant.colorThemeWhite : ColorConstant.colorBlueDark,
                                        letterSpacing: 1.5,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

             /// Flexible(child: _cartItemsView(data.cartData!, ),),

         ],
       ),
     );
   }

   /// divider
   Widget get _divider =>const Divider(height: 1,color: ColorConstant.colorBlueLight_16,);

  /// divider
  Widget get _dividerItems =>const Divider(height: 1,color: ColorConstant.colorBlueDark,);


   /// pending order view
   Widget _pendingOrderView(CartViewProvider data){
     return Container(
       decoration: BoxDecoration(
         color: ColorConstant.colorBlueLight_8,
         borderRadius: BorderRadius.circular(6.0,),
       ),
       padding: EdgeInsets.all(_equalSizePadding8,),
       child: Column(
         mainAxisSize: MainAxisSize.min,
         mainAxisAlignment: MainAxisAlignment.start,
         children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const Icon(Icons.shopping_cart_outlined, size: 20,color: ColorConstant.colorBlueDark,),
                _horizontalSpacing7,
                Expanded(
                  child: Text(CartViewString.pendingOrder,
                    style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontWeight: FontWeight.w600,
                      fontSize: isBigScreenResolution(context) ? 18 : 16,
                  ), ),
                ),
              ],
            ),
           if(data.pendingCartItemMap.isEmpty)...[
             _verticalSpacing7,
             Row(
               mainAxisSize: MainAxisSize.min,
               mainAxisAlignment: MainAxisAlignment.start,
               children: [
                 Expanded(
                   child: Text(CartViewString.cartIsEmpty,
                     textAlign: TextAlign.center,
                     style: AppTextStyle.smallTextStyle.copyWith(
                       color: ColorConstant.colorBlueLight_50,
                       fontSize: isBigScreenResolution(context) ? 14 : 12,
                     ), ),
                 ),
               ],
             ),
             _verticalSpacing7,
           ],

           ...data.pendingCartItemMap.entries.map((e) => _userWiseCartView(e,),),
         ],
       ),
     );
   }

   /// user wise cart view
   Widget _userWiseCartView(MapEntry<CartUserData, List<CartItems>> e, {bool hideModifiers = false,}){
     return Column(
       mainAxisSize: MainAxisSize.min,
       mainAxisAlignment: MainAxisAlignment.start,
       children: [
         _verticalSpacing7,
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: e.key.userId!.isNotEmpty ? () {
              navigateToCustomerDetailsScreen(context,e.key.userId!,e.key.name!);
            } : null,
            child: Row(
               mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(e.key.name!,
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontWeight: FontWeight.w600,
                    fontSize: isBigScreenResolution(context) ? 18 : 16,
                  ), ),
                _horizontalSpacing7,
                const Icon(Icons.keyboard_arrow_right_sharp, size: 20,color: ColorConstant.colorBlueDark,),

              ],
            ),
          ),
         _verticalSpacing7,
         ...e.value.map((e) => _itemRowView(e, hideModifiers: hideModifiers),),
         _verticalSpace,
       ],
     );
   }

  /// summary view
  Widget _summaryView({String label='', String value='', VoidCallback? onIconPressed, bool showServiceTipsRemoveIcon = false}){
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10),
      margin: EdgeInsets.only(bottom: isBigScreenResolution(context)? 3 : 2,),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              label,
              textAlign: TextAlign.start,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                color: ColorConstant.colorBlueDark ,
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark ,
              letterSpacing: 1.00,
            ),
          ),
          if(showServiceTipsRemoveIcon)...[
            if(label.replaceAll(RegExp(r'\s*\(.*?\)'), '').trim() == CartViewString.tip ||
                label.replaceAll(RegExp(r'\s*\(.*?\)'), '').trim() == CartViewString.serviceCharge)...[
              GestureDetector(
                onTap: onIconPressed,
                child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                  color: ColorConstant.colorRedDark_5,),
              )
            ]else if(context.read<CartViewProvider>().cartData!.cartTip != null ||
                context.read<CartViewProvider>().cartData!.cartServiceCharge != null)...[
              GestureDetector(
                onTap: onIconPressed,
                child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                  color: Colors.transparent,),
              )
            ]
          ],
        ],
      ),
    );
  }

  /// promo code view
  Widget _promoCodeView({String label = '', String value = '', VoidCallback? onRemoveIconPressed}) {
    CartPromoCode? cartPromoCode = context.read<CartViewProvider>().cartData?.cartPromoCode;
    bool onCart = cartPromoCode?.applyOn!.toLowerCase() == PromotionApplyOn.cart.name;
    bool alterColor = true;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10),
      margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 3 : 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Row(
              children: [
                Text(
                  label,
                  textAlign: TextAlign.start,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                    color: ColorConstant.colorBlueDark,
                  ),
                ),
              ],
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
          if(context.watch<CartViewProvider>().cartData!.paymentReceived == false)...[
            GestureDetector(
              onTap: onRemoveIconPressed,
              child: Container(
                color: Colors.transparent, // Ensure the area is tappable
                child: Icon(
                  Icons.close,
                  size: isBigScreenResolution(context) ? 20.0 : 16.0,
                  color: ColorConstant.colorRedDark_5,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// promo code view
  Widget _presetCodeView({String label = '', String value = '', VoidCallback? onRemoveIconPressed}) {
    CartDiscount? cartDiscount = context.read<CartViewProvider>().cartData?.cartDiscount;
    bool onCart = cartDiscount?.applyOn!.toLowerCase() == PromotionApplyOn.cart.name;
    bool alterColor = true;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10),
      margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 3 : 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Row(
              children: [
                Text(
                  label,
                  textAlign: TextAlign.start,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                    color: ColorConstant.colorBlueDark,
                  ),
                ),
                if(context.watch<CartViewProvider>().cartData!.cartDiscount!.approved! == false)...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: ColorConstant.notifPendingBackground,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      "Pending",
                      style: AppTextStyle.smallTextStyle.copyWith(
                        color: ColorConstant.notifPendingTextColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
          if(context.watch<CartViewProvider>().cartData!.paymentReceived == false)...[
            GestureDetector(
              onTap: onRemoveIconPressed,
              child: Container(
                color: Colors.transparent, // Ensure the area is tappable
                child: Icon(
                  Icons.close,
                  size: isBigScreenResolution(context) ? 20.0 : 16.0,
                  color: ColorConstant.colorRedDark_5,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }


  // custom discount view
  Widget _customDiscountView({String label = '', String value = "", bool onCart = false, VoidCallback? onRemoveIconPressed}) {
    bool alterColor = true;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10),
      margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 3 : 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Row(
              children: [
                Text(
                  label,
                  textAlign: TextAlign.start,
                  style: AppTextStyle.smallTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                    color: ColorConstant.colorBlueDark,
                  ),
                ),
                if(context.watch<CartViewProvider>().cartData!.cartCustomDiscount!.first.approved! == false)...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: ColorConstant.notifPendingBackground,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      "Pending",
                      style: AppTextStyle.smallTextStyle.copyWith(
                        color: ColorConstant.notifPendingTextColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ]
              ],
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
          GestureDetector(
            onTap: onRemoveIconPressed,
            child: Container(
              color: Colors.transparent, // Ensure the area is tappable
              child: Icon(
                Icons.close,
                size: isBigScreenResolution(context) ? 20.0 : 16.0,
                color: ColorConstant.colorRedDark_5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // custom discount view
  Widget _redeemPointsView({String label = '', String value = "", VoidCallback? onRemoveIconPressed}) {
    bool alterColor = true;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10),
      margin: EdgeInsets.only(bottom: isBigScreenResolution(context) ? 3 : 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Image.asset(
            AssetImages.loyaltyImage,
            width: isBigScreenResolution(context) ? 16 : 12,
            height: isBigScreenResolution(context) ? 16 : 12,
            color: alterColor ? ColorConstant.colorBlueDark : ColorConstant.colorGrayLight,
          ),
          Expanded(
            child: Text(
              label,
              textAlign: TextAlign.start,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                color: ColorConstant.colorBlueDark,
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyle.largeTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
              color: ColorConstant.colorBlueDark,
              letterSpacing: 1.00,
            ),
          ),
          GestureDetector(
            onTap: onRemoveIconPressed,
            child: Container(
              color: Colors.transparent, // Ensure the area is tappable
              child: Icon(
                Icons.close,
                size: isBigScreenResolution(context) ? 20.0 : 16.0,
                color: ColorConstant.colorRedDark_5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// submitted order view
   Widget _submittedOrderView(CartViewProvider data){
     return Container(
       decoration:  const BoxDecoration(
         color: ColorConstant.colorThemeWhite,
         boxShadow: [
           BoxShadow(
             color: ColorConstant.colorBlueLight_16,
             blurRadius: 5.0,
             spreadRadius: 0.0,
             offset:  Offset(2.0, 0), // shadow direction: bottom right
           ),
           BoxShadow(
             color: ColorConstant.colorBlueLight_16,
             blurRadius: 5.0,
             spreadRadius: 0.0,
             offset:  Offset(-2.0, 0), // shadow direction: bottom right
           )
         ],
       ),
       //padding: EdgeInsets.all(_equalSizePadding8,),
       child: Column(
         mainAxisSize: MainAxisSize.min,
         mainAxisAlignment: MainAxisAlignment.start,
         children: [
            Container(
              padding: EdgeInsets.all(_equalSizePadding8,),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(Icons.receipt_long_sharp, size: 20,color: ColorConstant.colorBlueDark,),
                      _horizontalSpacing7,
                      Expanded(
                        child: Text(CartViewString.submittedOrder,
                          style: AppTextStyle.mediumTextStyle.copyWith(
                            color: ColorConstant.colorBlueDark,
                            fontWeight: FontWeight.w600,
                            fontSize: isBigScreenResolution(context) ? 18 : 16,
                          ), ),
                      ),
                    ],
                  ),
                  if(data.submittedCartItemMap.isEmpty)...[
                    _verticalSpacing7,
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(CartViewString.billIsEmpty,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.smallTextStyle.copyWith(
                              color: ColorConstant.colorBlueLight_50,
                              fontSize: isBigScreenResolution(context) ? 14 : 12,
                            ), ),
                        ),
                      ],
                    ),
                  ],
                  ...data.submittedCartItemMap.entries.map((e) => _userWiseCartView(e,),),
                ],
              ),
            ),
           _dividerItems,
           _verticalSpacing7,
           if(data.submittedCartItemMap.isNotEmpty)...[
             _subTotalView(),
           ],
           cutImage(7.0, MediaQuery.of(context).size.width,AssetIcons.bottomCut,),
         ],
       ),
     );
   }

   /// cart items view

   /// sub total ui view
   Widget _subTotalView(){
     return Container(
         decoration:  const BoxDecoration(
         color: ColorConstant.colorThemeWhite,
         ),
       child: Column(
         children: [
           _summaryView(label:CartViewString.subTotal,value: NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().subTotalCalculate(),)),
           if(context.read<CartViewProvider>().cartData!.cartPromoCode != null)...[
             _promoCodeView(label: _promoCodeText(),onRemoveIconPressed: () {
                  _removePromotions(forCustomDisc: false, forPromo: true, id:context.read<CartViewProvider>().cartData!.cartPromoCode!.id);
               },
             ),
           ],
           if(context.read<CartViewProvider>().cartData!.cartDiscount != null)...[
              _presetCodeView(label: _presetCodeText(),onRemoveIconPressed: () {
                _removeDiscount();
              },
             ),
           ],
           if(context.read<CartViewProvider>().cartData!.cartCustomDiscount!.isNotEmpty)...[
             ...context.read<CartViewProvider>().cartData!.cartCustomDiscount!.map((customDiscount) {
                return _customDiscountView(label: _customDiscountText(appliedValue: customDiscount.appliedValue!),
                        onCart: customDiscount.applyOn!.toLowerCase() ==PromotionApplyOn.cart.name,
                        onRemoveIconPressed: () {
                          _removeCustomDiscount(discountId:customDiscount.id!);
                        },
                );
             }),

           ],
           if(context.read<CartViewProvider>().cartData!.cartLoyalty != null && context.read<CartViewProvider>().cartData!.cartLoyalty!.pointsRedeemed! > 0 )...[

             _redeemPointsView(label: _redeemPointsText(cartLoyalty: context.read<CartViewProvider>().cartData!.cartLoyalty!),
                 onRemoveIconPressed: () {
                   _removeRedeemPoints(id:context.read<CartViewProvider>().cartData!.cartLoyalty!.id);
                 },
               )
           ],
           _summaryView(label: CartViewString.newSubTotal,value: NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().newSubTotalCalculation(),),),
           _summaryView(label:CartViewString.tax,value: NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().cartData!.tax),),
           if(context.read<CartViewProvider>().cartData?.cartServiceCharge != null)...[
             _summaryView(label: '${CartViewString.serviceCharge} '
                 '( ${context.read<CartViewProvider>().cartData?.cartServiceCharge?.value}${context.read<CartViewProvider>().cartData?.cartServiceCharge?.type!.toLowerCase() == ApiConstant.VALUE_TYPE_FLAT ? '\$' : '%'} )',
                 value: NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().cartData?.cartServiceCharge?.amount ??0,),onIconPressed: () {
                   _removeServiceCharge();
                 },
                 showServiceTipsRemoveIcon:  context.read<CartViewProvider>().showServiceChargesRemoveIcon()
             ),
           ],
           if(context.read<CartViewProvider>().cartData?.cartTip != null)...[
             _summaryView(label: '${CartViewString.tip} '
                 '( ${context.read<CartViewProvider>().cartData?.cartTip?.value}${context.read<CartViewProvider>().cartData?.cartTip?.type.toString().toLowerCase() == ApiConstant.VALUE_TYPE_FLAT ? '\$' : '%'} )',
               value: NumberFormat.simpleCurrency().format(context.read<CartViewProvider>().cartData?.cartTip?.amount ??0,),onIconPressed: () {
                 _removeTip();
               },
               showServiceTipsRemoveIcon: context.read<CartViewProvider>().showTipsRemoveIcon(),
             ),
           ],
         ],
       ),
     );
   }
   // Widget _cartItemsView(CartData data) {
   //   return Container(
   //     decoration: BoxDecoration(
   //       color: ColorConstant.colorBlueLight_8,
   //       borderRadius: BorderRadius.circular(6.0,),
   //     ),
   //     padding: EdgeInsets.all(_equalSizePadding8,),
   //     child: Column(
   //       mainAxisSize: MainAxisSize.min,
   //       mainAxisAlignment: MainAxisAlignment.start,
   //       children: [
   //         ...data.cartItems!.map((e) => _itemRowView(e),),
   //         _verticalSpacing7,
   //         _divider,
   //         _verticalSpacing7,
   //         Row(
   //           mainAxisSize: MainAxisSize.min,
   //           mainAxisAlignment: MainAxisAlignment.start,
   //           children: [
   //             Expanded(child: _commonLabelView(CartViewString.subTotal, textAlign: TextAlign.start, isBold: true,)),
   //             Expanded(child: _commonLabelView(NumberFormat.simpleCurrency().format(data.subtotal!,), textAlign: TextAlign.end, isBold: true,)),
   //           ],
   //         ),
   //         Row(
   //           mainAxisSize: MainAxisSize.min,
   //           mainAxisAlignment: MainAxisAlignment.start,
   //           children: [
   //             Expanded(child: _commonLabelView(CartViewString.tax, textAlign: TextAlign.start, isBold: false,)),
   //             Expanded(child: _commonLabelView(NumberFormat.simpleCurrency().format(data.tax!,), textAlign: TextAlign.end, isBold: false,)),
   //           ],
   //         ),
   //         Row(
   //           mainAxisSize: MainAxisSize.min,
   //           mainAxisAlignment: MainAxisAlignment.start,
   //           children: [
   //             Expanded(child: _commonLabelView(CartViewString.total, textAlign: TextAlign.start, isBold: true,)),
   //             Expanded(child: _commonLabelView(NumberFormat.simpleCurrency().format(data.total!,), textAlign: TextAlign.end, isBold: true,)),
   //           ],
   //         ),
   //       ],
   //     ),
   //   );
   // }

   /// item row view
   Widget _itemRowView(CartItems item, {bool hideModifiers = false}) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
           Row(
             mainAxisSize: MainAxisSize.min,
             mainAxisAlignment: MainAxisAlignment.start,
             crossAxisAlignment: CrossAxisAlignment.center,
             children: [
               Expanded(child: _commonLabelView('${item.itemQty!}x ${item.name!}', textAlign: TextAlign.start, isBold: false,)),
               Expanded(child: _commonLabelView(NumberFormat.simpleCurrency().format(item.lineTotal!,), textAlign: TextAlign.end, isBold: false,)),
               if(!item.sentToKitchen!)...[
                 PopupMenuButton(
                   offset: Offset(-5, 20),
                   child:Container(
                      padding: EdgeInsets.symmetric(vertical: 3, ),
                     child: Icon(Icons.more_vert_sharp, color: ColorConstant.colorBlueDark,
                     size: 18,),
                   ),
                   itemBuilder:(context) {
                     return [
                       PopupMenuItem(
                         onTap: (){
                           _deleteCartItem(item.id!);
                         },
                         padding: EdgeInsets.zero,
                         height: 0,
                         child: Container(
                           padding: EdgeInsets.all(3),
                           child: Row(
                             mainAxisSize: MainAxisSize.max,
                             mainAxisAlignment: MainAxisAlignment.center,
                             children: [
                               Text(CartViewString.deleteItem,
                                 style: AppTextStyle.mediumTextStyle.copyWith(
                                   fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                                   color: ColorConstant.colorBlueDark,
                                   fontWeight: FontWeight.w500,
                                 ),),
                             ],
                           ),
                         ),
                       ),
                     ];
                   },
                 ),
               ],
               if(item.sentToKitchen! == true &&
                   context.watch<CartViewProvider>().actionVoidItem)...[

                     Container(
                       padding: EdgeInsets.symmetric(vertical: 3, horizontal: 3),
                       child: Checkbox(
                         visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                         materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                         checkColor: Colors.white,
                         activeColor: ColorConstant.colorBlueDark,
                         value:  context.watch<CartViewProvider>().checkItemSelectedForVoid(item.id!),
                         onChanged: (bool? value) {
                           context.read<CartViewProvider>().selectDeselectItemForVoid(item.id!,value!);
                         },
                         side: BorderSide(color: ColorConstant.colorBlueDark,),
                       ),
                     ),
               ],
             ],
           ),
           if(!hideModifiers)...[
             if(item.cartItemModifiers!.isNotEmpty)...[
               SizedBox(height: isBigScreenResolution(context) ? 3 : 2,),
               ...item.cartItemModifiers!.map((e) =>_itemModifiersView(e,), ),
             ],
           ],
           if(item.note!.isNotEmpty)...[
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(child: _noteLabelView('${CartViewString.note} ${item.note!}', textAlign: TextAlign.start, isBold: false,)),
              ],
            ),
          ],
          // Item level discount
          // _showPromotions(item),
        ],
      );
    }

   /// item modifiers View
   Widget _itemModifiersView(CartItemModifiers itemModifiers) {
     return Column(
       mainAxisSize: MainAxisSize.min,
       mainAxisAlignment: MainAxisAlignment.start,
       children: [
         Row(
           mainAxisSize: MainAxisSize.min,
           mainAxisAlignment: MainAxisAlignment.start,
           children: [
             SizedBox(width: isBigScreenResolution(context) ? 6 :4,),
             Expanded(
               child: Text(itemModifiers.name!,
                 style: AppTextStyle.smallTextStyle.copyWith(
                   fontSize: isBigScreenResolution(context) ? 11.0 : 10.0,
                   color: ColorConstant.colorBlueDark,
                 ),),
             ),
           ],
         ),
         if(itemModifiers.cartItemModifierItems!.isNotEmpty)...[
           ...itemModifiers.cartItemModifierItems!.map((e) =>_modifierItemsView(e,), ),
         ],
         SizedBox(height: isBigScreenResolution(context) ? 3 :2,),
       ],
     );
   }

   /// modifier items view
   Widget _modifierItemsView(CartItemModifierItems modifierItems){
     return Row(
       mainAxisSize: MainAxisSize.min,
       mainAxisAlignment: MainAxisAlignment.start,
       children: [
         SizedBox(width: isBigScreenResolution(context) ? 4 :3,),
         Expanded(
           child: Text('  ${modifierItems.qty} x ${modifierItems.name!}',
             style: AppTextStyle.smallTextStyle.copyWith(
               fontSize: isBigScreenResolution(context) ? 10.0 : 9.0,
               color: ColorConstant.colorBlueDark,
             ),),
         ),
       ],
     );
   }

   // common label view
  Widget  _commonLabelView(String label, {TextAlign textAlign = TextAlign.center , bool isBold = true }) {
     return AutoSizeText(
       label,
       textAlign: textAlign,
       maxLines: 2,
       style: AppTextStyle.smallTextStyle.copyWith(
         color: ColorConstant.colorBlueDark,
         fontWeight: isBold ?FontWeight.bold : FontWeight.w400,
       ),
       minFontSize: isBigScreenResolution(context) ? 13 : 11,
       maxFontSize: isBigScreenResolution(context) ? 14 : 12,
     );
   }

   // note label view
   Widget  _noteLabelView(String label, {TextAlign textAlign = TextAlign.center , bool isBold = true }) {
     return AutoSizeText(
       label,
       textAlign: textAlign,
       maxLines: 2,
       style: AppTextStyle.smallTextStyle.copyWith(
         color: ColorConstant.colorBlueDark,
         fontWeight: isBold ?FontWeight.bold : FontWeight.w400,
       ),
       minFontSize: isBigScreenResolution(context) ? 11 : 10,
       maxFontSize: isBigScreenResolution(context) ? 12 : 11,
     );
   }

  /// vertical space = height 16:14
  Widget get _verticalSpace => SizedBox(
    height: isBigScreenResolution(context) ? 3.0 : 2.0,
  );

   double get _equalSizePadding8 => isBigScreenResolution(context) ? 8 : 6;
   double get _equalSizeMargin14 => isBigScreenResolution(context) ? 14 : 12;
   Widget get _verticalSpacing7 => SizedBox(height: isBigScreenResolution(context) ? 7 : 6,);
   Widget get _horizontalSpacing7 => SizedBox(width: isBigScreenResolution(context) ? 7 : 6,);

  /// get cart
  Future<void> _getCart(int cartId) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();


    if (hasConnected) {
       if(!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<CartViewProvider>()
            .getCart(id: cartId);

        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }


   /// show  cart user list while add item
   _cartUserSelectionDialog() {
     showDialog(
         context: context,
         barrierDismissible: false,
         builder: (BuildContext _) {
           return Dialog(
             insetPadding: const EdgeInsets.all(10,),
             backgroundColor: ColorConstant.colorThemeWhite,
             shape: RoundedRectangleBorder(
               borderRadius: BorderRadius.circular(
                 isBigScreenResolution(context) ? 12.0 : 11.0,),
             ),
             child: CartUserSelectionContent(
               onAddButtonPressed: (){
                 if(widget.cartId == -1) {
                   _createCart();
                 }else {
                   _addUserToCart();
                 }
               },
               onDoneButtonPressed: ()async {
                 if(context.read<CartViewProvider>().cartData != null) {
                   Navigator.pop(context);
                 ///  navigate to menu view
                  navigateToMenuScreen(context);/*context,
                 context.read<CartViewProvider>().cartData!.id!*/
                 }else {
                 /// create cart first then navigate to menu view
                   Navigator.pop(context);
                   await _createCart();
                   navigateToMenuScreen(context);
                 }
               },
             ),
           );
         });
   }

   /// add user to cart
   Future<void> _addUserToCart() async {
     var hasConnected =
     await CheckInternetConnection.newInstance().checkConnection();


     if (hasConnected) {
       if(!mounted) return;
       showLoaderDialog(context);
       try {

         await context.read<CartViewProvider>()
             .addUserToCart();
         if (!mounted) return;
         hideLoaderDialog(context);
       } on UnauthorisedException catch (error) {
         if (!mounted) return;
         hideLoaderDialog(context);
         errorUnauthorisedAlertDialog(
             navigatorKey.currentState!.overlay!.context, error);
         return;
       } on FetchDataException catch (error) {
         debugPrint('Fetch data error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         return;
       } on BadRequestException catch (error) {
         debugPrint('Bad request data error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         return;
       } catch (error) {
         debugPrint('Getting error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         errorAlertDialog(error);
         return;
       }
     } else {
       showInterNetConnectionDialog(context);
     }
   }

   /// create cart
   Future<void> _createCart() async {
     var hasConnected =
     await CheckInternetConnection.newInstance().checkConnection();

     if (hasConnected) {
       if(!mounted) return;
       showLoaderDialog(context);
       try {

         CreateCartResponse cartResponse = await context.read<CartViewProvider>()
             .createCart(orderReceiveMethod:  widget.tableId != -1 ?
             OrderReceiveMethod.dineIn : OrderReceiveMethod.pickup,
             tableId: widget.tableId);

         if(!mounted) return;
         hideLoaderDialog(context);

          if(cartResponse.payload != null) {
            await _getCart(cartResponse.payload!.id!);
            /// assign back cart id to widget cart id
            widget.cartId = cartResponse.payload!.id!;

            _subscribeToCartChannel();

            if(widget.tableId != -1){

               if(!mounted) return;
              await context.read<DineInTakeoutProvider>().getFloorWithTables();
            }
            if(context.read<CartViewProvider>().selectedCartUser == 0) {
              _addUserToCart();
            }
          }

       } on UnauthorisedException catch (error) {
         if (!mounted) return;
         hideLoaderDialog(context);
         errorUnauthorisedAlertDialog(
             navigatorKey.currentState!.overlay!.context, error);
         return;
       } on FetchDataException catch (error) {
         debugPrint('Fetch data error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         return;
       } on BadRequestException catch (error) {
         debugPrint('Bad request data error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         return;
       } catch (error) {
         debugPrint('Getting error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         errorAlertDialog(error);
         return;
       }
     } else {
       showInterNetConnectionDialog(context);
     }
   }

   /// handle bottom sheet menu
   _handleBottomSheetMenu() {

     debugPrint(':: menu items start::');
     Map<String, bool> menuItems ={};

     debugPrint(':: menu items $menuItems::');
     return Consumer<CartViewProvider>(
       builder: (context, data, child) {
         CartData? cartData =  data.cartData;
         if(cartData != null) {
           String cartStatus = cartData.status!.toLowerCase();
           if(cartData.platform!.toLowerCase() == ConstantString.platformWeb) {

             /// if its pending
             if(cartStatus == OrderStatus.pending) {

               menuItems.addAll({
                 ..._addItemsBottomMenuItemLabel(),
                 ..._sendToKitchenBottomMenuItemLabel(),
                 BottomSheetString.payUsingCash: true,
                 BottomSheetString.payUsingNonIntegrated: true,
                 ..._applyPromoCodeBottomMenuItemLabel(),
                 ..._applyCustomDiscountBottomMenuItemLabel(),
                 BottomSheetString.applyPresetDiscount: true,
                 if (cartData.paymentReceived == true) BottomSheetString.closeBill: true,
                 BottomSheetString.printReceipt: false,
                 BottomSheetString.printKot: false,
               });

             }
             /// if its placed/submitted
             else if(cartStatus == OrderStatus.placed){

               if( cartData.paymentReceived == false) {

                 menuItems.addAll({
                   BottomSheetString.payUsingCash: true,
                   BottomSheetString.payUsingNonIntegrated: true,
                   ..._applyPromoCodeBottomMenuItemLabel(),
                   ..._applyCustomDiscountBottomMenuItemLabel(),
                   BottomSheetString.applyPresetDiscount: true,
                   BottomSheetString.printReceipt: false,
                   BottomSheetString.printKot: false,
                 });

               }else if(cartData.paymentReceived == true)  {
                 menuItems.addAll({
                   BottomSheetString.closeBill: true,
                   BottomSheetString.printReceipt: false,
                   BottomSheetString.printKot: false,
                 });
               }

             }else if(cartStatus == OrderStatus.ready ||
                 cartStatus == OrderStatus.outForDelivery) {

               menuItems.addAll({
                 BottomSheetString.printReceipt: false,
                 BottomSheetString.printKot: false,
               });

             }

           }else  {

             if(cartStatus == OrderStatus.pending || cartStatus == OrderStatus.placed) {
               if(cartData.paymentReceived == false) {

                 menuItems.addAll({
                   ..._addItemsBottomMenuItemLabel(),
                   ..._sendToKitchenBottomMenuItemLabel(),
                   BottomSheetString.payUsingCash: true,
                   BottomSheetString.payUsingNonIntegrated: true,
                   ..._applyPromoCodeBottomMenuItemLabel(),
                   ..._applyCustomDiscountBottomMenuItemLabel(),
                   BottomSheetString.applyPresetDiscount: true,
                   ..._serviceChargeBottomMenuItemLabel(),
                   ..._tipsBottomMenuItemLabel(),
                   BottomSheetString.printReceipt: false,
                   BottomSheetString.printKot: false,
                 });

               }else if(cartData.paymentReceived == true)  {
                 menuItems.addAll({
                   ..._addItemsBottomMenuItemLabel(),
                   BottomSheetString.closeBill: true,
                   BottomSheetString.printReceipt: false,
                   BottomSheetString.printKot: false,
                 });
               }
             }

           }
         }else {
           menuItems ={BottomSheetString.addItems: true};
         }

         return BottomSheetContent(menuItems: menuItems, onMenuItemSelected: (selectedMenu) {
           context.read<BottomSheetProvider>().setDialogForCartPage(false);

            switch(selectedMenu) {
              case BottomSheetString.applyPromo:
                bool allPendingEmpty = context.read<CartViewProvider>().pendingCartItemMap.values.every((e) => e.isEmpty);
                bool allSubmittedEmpty = context.read<CartViewProvider>().submittedCartItemMap.values.every((e) => e.isEmpty);
                if(allPendingEmpty && allSubmittedEmpty == false) {
                  _applyPromoDialog();
                }else {
                  showSnackBar(CartViewString.errorPendingItem);
                }
                break;
              case BottomSheetString.applyPresetDiscount:
                   _applyPresetDiscountDialog();
                break;
              case BottomSheetString.applyCustomDiscount:
                  _applyCustomDiscountOptionDialog();
                break;
              case BottomSheetString.payUsingCash:
                   bool allPendingEmpty = context.read<CartViewProvider>().pendingCartItemMap.values.every((e) => e.isEmpty);
                   bool allSubmittedEmpty = context.read<CartViewProvider>().submittedCartItemMap.values.every((e) => e.isEmpty);
                   if(allPendingEmpty && allSubmittedEmpty == false) {
                     context.read<PaymentProvider>().updateCartData(context.read<CartViewProvider>().cartData);
                     navigateToCashPaymentScreen(context);
                   }else {
                     if(context.read<CartViewProvider>().pendingCartItemMap.isNotEmpty){
                       showSnackBar(CartViewString.errorPendingItem);
                     }
                   }

                break;
              case BottomSheetString.payUsingNonIntegrated:
                bool allPendingEmpty = context.read<CartViewProvider>().pendingCartItemMap.values.every((e) => e.isEmpty);
                bool allSubmittedEmpty = context.read<CartViewProvider>().submittedCartItemMap.values.every((e) => e.isEmpty);
                if(allPendingEmpty && allSubmittedEmpty == false) {
                    context.read<PaymentProvider>().updateCartData(context.read<CartViewProvider>().cartData);
                    _showOtherPaymentDialog();
                }else {
                  if(context.read<CartViewProvider>().pendingCartItemMap.isNotEmpty){
                    showSnackBar(CartViewString.errorPendingItem);
                  }
                }
                break;
              case BottomSheetString.closeBill:
                    _closeBill(context.read<CartViewProvider>().cartData!);
                break;
              case BottomSheetString.addServiceCharge:
                    _addServiceChargeDialog();
                break;
              case BottomSheetString.addTips:
                    _addTipDialog();
                break;
              case BottomSheetString.addItems:
                    _cartUserSelectionDialog();
                break;
              case BottomSheetString.sendToKitchen:
                _itemSendToKitchen();
                break;
            }

         },
         );
       },
     );

   }

   /// item send to kitchen
   Future<void> _itemSendToKitchen() async {
     var hasConnected =
     await CheckInternetConnection.newInstance().checkConnection();

     if (hasConnected) {
       showLoaderDialog(context);
       try {
         await context.read<CartViewProvider>().itemSendToKitchen();

         hideLoaderDialog(context);
       } on UnauthorisedException catch (error) {
         if (!mounted) return;
         hideLoaderDialog(context);
         errorUnauthorisedAlertDialog(
             navigatorKey.currentState!.overlay!.context, error);
         return;
       } on FetchDataException catch (error) {
         debugPrint('Fetch data error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         return;
       } on BadRequestException catch (error) {
         debugPrint('Bad request data error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         return;
       } catch (error) {
         debugPrint('Getting error : ' + error.toString());
         if (!mounted) return;
         hideLoaderDialog(context);
         errorAlertDialog(error);
         return;
       }
     } else {
       showInterNetConnectionDialog(context);
     }
   }

  /// pubnub emit listener
  _pubnubEmitListener(){
    if(_orderStreamSubscription == null){
      _orderStreamSubscription = context.read<PubNubProvider>().dataStream.listen((event) async {
        debugPrint(':: stream listener order $event ::');
        if (event.isNotEmpty) {
          if(event.containsKey(PubNubConstants.EVENT_TABLE_ACTIVATED)){
            PubNubOrderPayload? _payload;

            if(event.containsKey(PubNubConstants.EVENT_TABLE_ACTIVATED)) {
              _payload = event[PubNubConstants.EVENT_TABLE_ACTIVATED] as PubNubOrderPayload;
            }
            if(_payload?.cartId != 0 && _payload?.tableId == widget.tableId){
              widget.cartId = _payload!.cartId??0;
              if(widget.cartId != 0){
                await  _getCart(widget.cartId);
                _subscribeToCartChannel();
              }
            }
          }
          else if (event.containsKey(PubNubConstants.EVENT_ORDER_PROCESSED)) {
            debugPrint(':: order_proceed ():Cart View');
            PubNubOrderPayload? _payload;
            _payload = event[PubNubConstants.EVENT_ORDER_PROCESSED] as PubNubOrderPayload;
            if(_payload.cartId != 0 && _payload.cartId == widget.cartId) {
              await context.read<CartViewProvider>().setCartData(null);
              await context.read<CartViewProvider>().resetCartUserSelection();
              widget.cartId = -1;
              _unsubscribeFromCartChannel();
            }
          } else if (event.containsKey(PubNubConstants.EVENT_ORDER_CREATED)) {
            debugPrint(':: cart order_created ::');
            PubNubOrderPayload? _payload;
            _payload = event[PubNubConstants.EVENT_ORDER_CREATED] as PubNubOrderPayload;
            if(_payload.cartId != 0 && _payload.tableId == widget.tableId){
                widget.cartId = _payload.cartId??0;
                if(widget.cartId != 0){
                  await  _getCart(widget.cartId);
                 _subscribeToCartChannel();
                }
              }
          } else if (event.containsKey(PubNubConstants.EVENT_ORDER_DISCOUNT)) {
            debugPrint(':: cart order_discount ::');
            PubNubOrderPayload? _payload;
            _payload = event[PubNubConstants.EVENT_ORDER_DISCOUNT] as PubNubOrderPayload;
            if(_payload.cartId != 0){
              widget.cartId = _payload.cartId??0;
              if(widget.cartId != 0){
                await _getCart(widget.cartId);
              }
            }
          } else if (event.containsKey(PubNubConstants.EVENT_ORDER_PROMO_CODE)) {
            debugPrint(':: cart order_promocode ::');
            PubNubOrderPayload? _payload;
            _payload = event[PubNubConstants.EVENT_ORDER_PROMO_CODE] as PubNubOrderPayload;
            if(_payload.cartId != 0){
              widget.cartId = _payload.cartId??0;
              if(widget.cartId != 0){
                await _getCart(widget.cartId);
              }
            }
          }
        }
      });
    }

    if(_cartStreamSubscription == null) {
      _cartStreamSubscription = context.read<PubNubProvider>().cartStream.listen((event) async {
        debugPrint(':: stream listener cart $event ::');
        PubNubOrderPayload? _payload;
        if (event.isNotEmpty) {
          if (event.containsKey(PubNubConstants.EVENT_USER_CART)) {
            debugPrint('::  got user_cart emit serving ::');
            _payload = event[PubNubConstants.EVENT_USER_CART] as PubNubOrderPayload;
          }
          else if(event.containsKey(PubNubConstants.EVENT_CART_USER_UPDATED)) {
            _payload = event[PubNubConstants.EVENT_CART_USER_UPDATED] as PubNubOrderPayload;
          }
          else if(event.containsKey(PubNubConstants.EVENT_USER_JOINED_TABLE)) {
            _payload = event[PubNubConstants.EVENT_USER_JOINED_TABLE] as PubNubOrderPayload;
          }
          else if(event.containsKey(PubNubConstants.EVENT_PARTIAL_PAYMENT_RECEIVED)) {
           _payload = event[PubNubConstants.EVENT_PARTIAL_PAYMENT_RECEIVED] as PubNubOrderPayload;
          }
          else if(event.containsKey(PubNubConstants.EVENT_FULL_PAYMENT_RECEIVED)){
            _payload = event[PubNubConstants.EVENT_FULL_PAYMENT_RECEIVED] as PubNubOrderPayload;
          }
          else if(event.containsKey(PubNubConstants.EVENT_ORDER_LOYALTY)) {
            _payload = event[PubNubConstants.EVENT_ORDER_LOYALTY] as PubNubOrderPayload;
          }
          else if(event.containsKey(PubNubConstants.EVENT_ITEM_MOVE)) {
            _payload = event[PubNubConstants.EVENT_ITEM_MOVE] as PubNubOrderPayload;
          }

          if(_payload == null) return;
          if(context.read<CartViewProvider>().cartData !=null
              && context.read<CartViewProvider>().cartData!.id == _payload.cartId!) {
            try{
              if(_payload.cartStatus == OrderStatus.processed){
                await context.read<CartViewProvider>().setCartData(null);
                await context.read<CartViewProvider>().resetCartUserSelection();
                _unsubscribeFromCartChannel();
              }else{
                await context.read<CartViewProvider>().getCart(id: widget.cartId);
              }

            }catch(e){
              widget.cartId =-1;
              context.read<CartViewProvider>().setCartData(null);
              context.read<CartViewProvider>().resetCartUserSelection();
              _unsubscribeFromCartChannel();
            }
            await context.read<DineInTakeoutProvider>().getFloorWithTables();
          }
        }
      });
    }
  }

  /// Subscribe to the cart channel
  _subscribeToCartChannel() async{
    String locationId = LocationModel.fromJson(PrefsUtils.getObject(PrefKeys.location)).id!;

    if(widget.cartId != -1) {
      if(null == _cartSubscription) {
        if(mounted)
          _cartSubscription = await PubNubService().subScribeToOrderChannel(channelName:
        '${PubNubConstants.CHANNEL_NAME_PREFIX}${PubNubConstants.CHANNEL_NAME_PREFIX_LOCATION}$locationId${PubNubConstants.CHANNEL_NAME_POSTFIX_ORDER}${widget.cartId}', context: context);
      }
    }
    context.read<PubNubProvider>().reOpenStream();
  }

  /// Unsubscribe from the cart channel
  _unsubscribeFromCartChannel(){
    if(null !=_cartSubscription ){
      if(!_cartSubscription!.isCancelled)  {
        _cartSubscription?.cancel().then((value) {
          debugPrint(':: Subscription cancelled ::');
          _cartSubscription= null;
          debugPrint(' cart subscription list :: ${_cartSubscription?.channels}');
        });
      }

      return;
    }
  }

  /// Destroy pubnub listener
  _destroyEmitListener(){
    _orderStreamSubscription?.cancel();
    _orderStreamSubscription = null;
    _cartStreamSubscription?.cancel();
    _cartStreamSubscription = null;
  }

  /// Delete cart item
  Future<void> _deleteCartItem(int cartItemId) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context.read<CartViewProvider>().deleteCartItem(cartItemId: cartItemId);

        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// Void item confirmation dialog
  _voidItemConfirmationDialog() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            insetPadding: EdgeInsets.all(isBigScreenResolution(context)? 10 : 8,),
            backgroundColor: ColorConstant.colorThemeWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                isBigScreenResolution(context) ? 12.0 : 11.0,),
            ),
            child: Container(
              //width: MediaQuery.of(context).size.width / 1.3,
              child: VoidItemConfirmationContent(onButtonPressed: (value){
                Navigator.of(context).pop();
                debugPrint(':: reason added $value ::');
                _voidCartItem(reason: value);
              },),
            ),
          );
        });
  }

  /// void cart item
  void _voidCartItem(
      {required String reason}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        await context
            .read<CartViewProvider>()
            .voidCartItems(reason);

        hideLoaderDialog(context);
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// Show other payment option dialog
  _showOtherPaymentDialog() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext _) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: ColorConstant.colorThemeWhite,
                borderRadius: BorderRadius.circular(12.0),
              ),
              padding: EdgeInsets.symmetric(
                  horizontal: isBigScreenResolution(context) ? 20.0 : 18.0),
              child: SingleChildScrollView(
                controller: _otherPaymentAmountScrollController,
                child: Consumer<PaymentProvider>(builder: (_, data, child) {
                  return  Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: isBigScreenResolution(context) ? 24.0 : 22.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                CartViewString.otherPayment,
                                textAlign: TextAlign.center,
                                style: AppTextStyle.largeTextStyle.copyWith(
                                  fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                                  color: ColorConstant.colorBlueDark,
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                data.resetOtherPaymentSelection();
                                data.setReceivePayment(false);
                                data.resetCashCardPaymentFlag();
                                _otherPaymentAmountController.clear();
                                Navigator.of(context).pop();
                              },
                              child: closeIcon(25.0, color: ColorConstant.colorBlueDark,),
                            )
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                          top: isBigScreenResolution(context) ? 40.0 : 30.0,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 20, right: 20),
                              child: Container(
                                width: double.infinity,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        _otherPaymentTotalLabelTextView(CartViewString.total),
                                        _otherPaymentTotalLabelTextView(NumberFormat.simpleCurrency().format(data.cartData!.total),),
                                      ],
                                    ),
                                    if (data.cartData!.paidAmount! > 0) ...[
                                      Row(
                                        children: [
                                          // _otherPaymentTotalLabelTextView(CartViewString.PAID),
                                          // _otherPaymentTotalLabelTextView(NumberFormat.simpleCurrency().format(data.cartData!.paidAmount!),),
                                        ],
                                      ),
                                    ],
                                    Row(
                                      children: [
                                        _otherPaymentTotalLabelTextView(CartViewString.remaining),
                                        Expanded(
                                          child: Text(
                                            NumberFormat.simpleCurrency().format(data.cartData!.dueAmount/*.remainingAmount*/),
                                            style: AppTextStyle.largeTextStyle.copyWith(
                                              color: ColorConstant.rectangleStash,
                                              fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            StreamBuilder<bool>(
                              stream: _otherPaymentAmountFocusStream,
                              initialData: false,
                              builder: (context, snapshot) {
                                return Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isBigScreenResolution(context)? 8.0 : 6.0,
                                    vertical: isBigScreenResolution(context)? 10.0: 8.0,
                                  ),
                                  margin: EdgeInsets.only(top: 10.0,),
                                  decoration: BoxDecoration(
                                    color: ColorConstant.colorThemeWhite,
                                    borderRadius: BorderRadius.circular(isBigScreenResolution(context) ?6.0:4.0,),
                                    border: Border.all(
                                      color:
                                      snapshot.data == true
                                          ? data.isOverPayment
                                          ? ColorConstant.errorColor: ColorConstant.colorBlueLight_16
                                          : ColorConstant.colorBlueLight_16,
                                      width: 0.9,
                                    ),
                                  ),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        CartViewString.amount + ':',
                                        style: AppTextStyle.smallTextStyle
                                            .copyWith(
                                          letterSpacing: 0.3,
                                          fontSize:
                                          isBigScreenResolution(context)
                                              ? 14.0
                                              : 12.0,
                                          color: ColorConstant.colorBlueDark,
                                        ),
                                      ),
                                      Expanded(
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: isBigScreenResolution(context) ? 20.0 : 10.0),
                                          child: TextField(
                                            cursorColor: ColorConstant.colorBlueDark,
                                            focusNode: _otherPaymentAmountTextFieldFocus,
                                            textAlign: TextAlign.right,
                                            autofocus: false,
                                            showCursor: true,
                                            keyboardType: TextInputType.number,
                                            inputFormatters: <TextInputFormatter>[
                                              CurrencyTextInputFormatter(
                                                  locale: 'en_US',
                                                  decimalDigits: 2,
                                                  name: '\$',
                                                  symbol: '\$'),
                                            ],
                                            controller: _otherPaymentAmountController,
                                            onChanged: (value) {
                                              _debounce.call(() async {
                                                // _otherPaymentAmountStreamController
                                                //     .sink
                                                //     .add(value);
                                              });
                                            },
                                            style: AppTextStyle.largeTextStyle
                                                .copyWith(
                                              fontSize:
                                              isBigScreenResolution(context)
                                                  ? 16.0
                                                  : 14.0,
                                              letterSpacing: 0.3,
                                              color: ColorConstant.colorBlueDark,
                                            ),
                                            maxLines: 1,
                                            decoration: InputDecoration(
                                              filled: true,
                                              isDense: true,
                                              fillColor: Colors.transparent,
                                              contentPadding: EdgeInsets.zero,
                                              hintText: "\$0.00",
                                              hintStyle: AppTextStyle
                                                  .largeTextStyle
                                                  .copyWith(
                                                fontSize:
                                                isBigScreenResolution(context)
                                                    ? 16.0
                                                    : 14.0,
                                                letterSpacing: 0.3,
                                                color: ColorConstant.colorBlueDark,
                                              ),
                                              prefixIconConstraints:
                                              BoxConstraints(
                                                  minWidth: 0, minHeight: 0),
                                              border: OutlineInputBorder(
                                                borderSide: BorderSide.none,
                                                borderRadius: BorderRadius.zero,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),

                            if (data.isOverPayment) ...[
                              SizedBox(
                                height: 5.0,
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.error,
                                    color: ColorConstant.colorRedDark,
                                    size: 20.0,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(left: isBigScreenResolution(context)? 10.0 : 8.0),
                                    child: Text(
                                      PaymentString.errorOrderAmountGreaterError,
                                      style: AppTextStyle.smallTextStyle.copyWith(
                                        color: ColorConstant.colorRedDark,
                                        fontSize: isBigScreenResolution(context)
                                            ? 14.0
                                            : 12.0,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      SizedBox(height: isBigScreenResolution(context) ? 30 : 28,),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(
                                    OtherPaymentOptions.VISA.name,
                                    OtherPaymentOptions.VISA.index);
                              },
                              child: _otherPaymentOptionTextView(data, index: OtherPaymentOptions.VISA.index, optionName: OtherPaymentOptions.VISA.name),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(
                                    OtherPaymentOptions.MC.name,
                                    OtherPaymentOptions.MC.index);
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.MC.index, optionName: OtherPaymentOptions.MC.name),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(
                                    OtherPaymentOptions.AMEX.name,
                                    OtherPaymentOptions.AMEX.index);
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.AMEX.index, optionName: OtherPaymentOptions.AMEX.name),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(
                                    OtherPaymentOptions.DEBIT.name,
                                    OtherPaymentOptions
                                        .DEBIT.index);
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.DEBIT.index, optionName: OtherPaymentOptions.DEBIT.name),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(
                                    OtherPaymentOptions.UBER.name,
                                    OtherPaymentOptions.UBER.index);
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.UBER.index, optionName: OtherPaymentOptions.UBER.name),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(OtherPaymentOptions.GIFT_CARD.name,
                                    OtherPaymentOptions.GIFT_CARD.index);
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.GIFT_CARD.index, optionName: OtherPaymentOptions.GIFT_CARD.name.replaceAll('_', ' ')),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                data.setSelectedOtherPayment(OtherPaymentOptions.OTHER.name,
                                    OtherPaymentOptions.OTHER.index);
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.OTHER.index, optionName: OtherPaymentOptions.OTHER.name),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: data.isOverPayment
                                  ? null
                                  : () {
                                // String _terminalId = PaymentTerminal
                                //     .fromJson(PrefsUtils.getObject(PrefKeys.paymentTerminal)).terminalId ?? '';
                                // bool _isCardOptionEnable =
                                //     context
                                //         .read<SettingProvider>()
                                //         .isCardPaymentOption;
                                // if (_terminalId.isNotEmpty) {
                                //   if (!_isCardOptionEnable) {
                                //     data.setPaymentTerminalNotFoundErrorMessage(SettingsString.ERROR_ASSIGN_POS_STATION_CARD_PAYMENT_OPTION);
                                //     return;
                                //   }
                                //   data.setPaymentTerminalNotFoundErrorMessage('');
                                  data.setSelectedOtherPayment(
                                      OtherPaymentOptions
                                          .CLOVER.name,
                                      OtherPaymentOptions
                                          .CLOVER.index);
                                // } else {
                                //   data.setPaymentTerminalNotFoundErrorMessage(context.read<SettingProvider>().posStationList.isNotEmpty ? SettingsString
                                //       .ERROR_ASSIGN_POS_STATION_PAYMENT_TERMINAL : CartViewString
                                //       .ERROR_PAYMENT_TERMINAL);
                                // }
                              },
                              child: _otherPaymentOptionTextView(data,index: OtherPaymentOptions.CLOVER.index, optionName: OtherPaymentOptions.CLOVER.name),
                            ),
                          ),
                          // Extra button for design purposes
                          Expanded(
                            child: Container(),
                          ),
                        ],
                      ),
                      if (data.paymentTerminalNotFoundErrorMessage.isNotEmpty) ...[
                        SizedBox(height: 10,),
                        Row(
                          children: [
                            Icon(
                              Icons.error,
                              color: ColorConstant.colorRedDark,
                              size: 20.0,
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 10.0),
                              child: Text(
                                data.paymentTerminalNotFoundErrorMessage,
                                style: AppTextStyle.smallTextStyle.copyWith(
                                  color: ColorConstant.colorRedDark,
                                  fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                      InkWell(
                        onTap: data.isOverPayment
                            ? null
                            : () {
                          if (data.selectedOtherPaymentIndex == -1) {
                            showErrorDialog(
                                error:
                                PaymentString.errorOtherPayment);
                            return;
                          }
                          String _convertToAmount = _otherPaymentAmountController.text.toString().replaceAll('\$', '');

                          if (_convertToAmount.isEmpty) {
                            showErrorDialog(error: PaymentString.errorOrderAmount);
                            return;
                          }

                          num _cartAmount = double.parse(_convertToAmount);
                          debugPrint("Order Amount test $_cartAmount");

                          if (_cartAmount <= 0) {
                            showErrorDialog(error: PaymentString.errorOrderAmount);
                            return;
                          }

                          if (_cartAmount > data.cartData!.dueAmount!) {
                            showErrorDialog(
                                error: PaymentString.errorOrderAmountGreaterError);
                            return;
                          }

                          // if (Navigator.canPop(context)) {
                          //   context.read<CartViewProvider>().resetCashCardPaymentFlag();
                          //   Navigator.of(context).pop(true);
                          //   data.setPaymentTerminalNotFoundErrorMessage('');
                          // }


                          String _paymentType = data.getPaymentType();
                          String _cardType = data.cardType;

                          if (_paymentType == PaymentType.credit) {
                            data.setCardPaymentSelected(true);
                          }

                          Future.delayed(const Duration(milliseconds: 300), () async {
                            await _callCartPayment(
                              paymentType: _paymentType,
                              cartAmount: _cartAmount,
                              cardType: _cardType,
                            );

                            if (data.isPaymentSuccessFull){
                              _otherPaymentAmountController.clear();
                              if (data.cartData!.dueAmount! > 0) {
                                // data.setCashPaymentSelected(false);
                                // data.setCardPaymentSelected(false);
                                data.setOtherPaymentFail(true);
                                data.setPaymentSuccessFull(false);
                                showSnackBar(PaymentString.errorDueAmount);
                                // _showSnackBar(
                                //     message:
                                //     CartViewString.ERROR_DUE_AMOUNT,
                                //     duration: 2);

                                Future.delayed(const Duration(seconds: 1), () async {
                                  data.setReceivePayment(false);
                                });

                                return;
                              } else {
                                data.setWaitingForCardPayment(false, true);
                              }
                              if (_paymentType == PaymentType.credit) {
                                data.setWaitingForCardPayment(false, true);
                              }

                              if (Navigator.canPop(context)) {
                                data.resetCashCardPaymentFlag();
                                Navigator.of(context).pop(true);
                                data.setPaymentTerminalNotFoundErrorMessage('');
                                context.read<PaymentProvider>().resetOtherPaymentSelection();
                                context.read<PaymentProvider>().setReceivePayment(false);
                                context.read<PaymentProvider>().resetCashCardPaymentFlag();
                              }
                            } else{
                              if (_paymentType == PaymentType.credit) {
                                data.setWaitingForCardPayment(false, false);
                              } else {
                                showSnackBar(data.transaction!.status!);
                              }
                            }
                          });
                        },
                        child: Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(
                            top: 30.0,
                            bottom: 8.0,
                          ),
                          padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 12: 10),
                          child: Text(
                            PaymentString.receivePayment.toUpperCase(),
                            style: AppTextStyle.largeTextStyle.copyWith(
                              color: data.isOverPayment
                                  ? ColorConstant.colorBlueDark
                                  : Colors.white,
                              fontSize: isBigScreenResolution(context) ? 12.0 : 10.0,
                              letterSpacing: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          decoration: BoxDecoration(
                            color: data.isOverPayment
                                ? ColorConstant.colorBlueLight_16
                                : ColorConstant.colorBlueDark,
                            borderRadius: BorderRadius.circular(
                              4.0,
                            ),
                          ),
                        ),
                      ),
                      StreamBuilder<bool>(
                        stream: _otherPaymentAmountFocusStream,
                        initialData: false,
                        builder: (context, snapshot) {
                          if (snapshot.data == true) {
                            return SizedBox(
                              height: 100,
                            );
                          } else {
                            return Container();
                          }
                        },
                      ),
                    ],
                  );
                },),

              ),
            ),
          );
        });
  }
  /// Other payment dialog : total / paid / remaining text view
  Widget _otherPaymentTotalLabelTextView(String label){
    return Expanded(
      child: Text(
        label,
        style: AppTextStyle.smallTextStyle.copyWith(
          fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
          color: ColorConstant.colorBlueDark,
        ),
      ),
    );
  }


  /// Other payment dialog : other payment options text view.
  Widget _otherPaymentOptionTextView(PaymentProvider data, {int index = -1, String optionName = ''}){
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5.0,),
      padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 8 : 7), //11 : 9)
      decoration: BoxDecoration(
        color: data.isOverPayment
            ? ColorConstant.colorThemeWhite :data.selectedOtherPaymentIndex == index
            ? ColorConstant.colorBlueDark
            : ColorConstant.colorThemeWhite,
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(
          color: data.isOverPayment
              ? ColorConstant.colorBlueLight_16
              : ColorConstant.colorBlueDark,
        ),
      ),
      child: Text(
        optionName,
        style: AppTextStyle.smallTextStyle.copyWith(
          color: data.isOverPayment
              ? ColorConstant.colorBlueLight_16
              : data.selectedOtherPaymentIndex == index ? ColorConstant.colorThemeWhite :ColorConstant.colorBlueDark,
          fontSize:
          isBigScreenResolution(context)
              ? 10.0
              : 8.0,
          letterSpacing: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Pro code label
  _promoCodeText(){
    String label = CartViewString.code;
    // String? applyOn = context.read<CartViewProvider>().cartData!.cartPromoCode?.applyOn?.replaceAll("", "\u{200B}");
    String? code = context.read<CartViewProvider>().cartData!.cartPromoCode?.code;
    num? value = context.read<CartViewProvider>().cartData!.cartPromoCode?.appliedValue;

    return ("$label $code (-\$${roundDouble(value!)})");
  }

  /// Preset code label
  _presetCodeText(){
    String label = CartViewString.preset;
    String? code = context.read<CartViewProvider>().cartData!.cartDiscount?.title;
    num? value = context.read<CartViewProvider>().cartData!.cartDiscount?.appliedValue;

    return ("$label $code (-\$${roundDouble(value!)})");
  }

  /// Custom discount code label
  _customDiscountText({required num appliedValue}){
    String label = CartViewString.custom;
    String title = CartViewString.discount;


    return ("$label $title (-\$${roundDouble(appliedValue)})");
  }

  /// Redeem points label
  _redeemPointsText({required CartLoyalty? cartLoyalty}){
    String label = CartViewString.pointsRedeemed;
    return ("$label ${cartLoyalty!.pointsRedeemed!} (-\$${roundDouble(cartLoyalty.rewardAmount!)})");
  }

  void _onFocusChangeInOtherPaymentAmount() {
    debugPrint("Focus Other Payment amount: ${_otherPaymentAmountTextFieldFocus.hasFocus.toString()}");
    if (_otherPaymentAmountTextFieldFocus.hasFocus) {
      _otherPaymentAmountFocusStreamController.sink.add(true);
      _otherPaymentAmountController.selection = TextSelection.collapsed(
          offset: _otherPaymentAmountController.text.length);
    } else {
      _otherPaymentAmountFocusStreamController.sink.add(false);
      _otherPaymentAmountController.selection = TextSelection.collapsed(
          offset: _otherPaymentAmountController.text.length);
    }

    Future.delayed(Duration(milliseconds: 500), () {
      _otherPaymentAmountScrollController.jumpTo(
          _otherPaymentAmountTextFieldFocus.hasFocus
              ? _otherPaymentAmountScrollController.position.maxScrollExtent
              : _otherPaymentAmountScrollController.position.minScrollExtent);
    });
  }

  /// cart payment api
  Future<void> _callCartPayment(
      {required String paymentType,
        required num cartAmount,
        String cardType = 'other'}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context.read<PaymentProvider>().cartPayment(
            paymentType: paymentType,
            cartAmount: cartAmount,
            cardType: cardType);
        hideLoaderDialog(context);
        if(widget.cartId == -1)
          return;
        // Update cartData
        context.read<CartViewProvider>().getCart(id: widget.cartId);

      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      } catch (error) {
        debugPrint('Getting error test: ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(
            error: 'Something went wrong. Please check the payment device.');
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// Call update cart status api on close bill button
  _closeBill(CartData data) async{
    _updateCartStatus(data.id!, OrderStatus.processed, data.tableId!);
  }

  /// update cart item
  Future<void> _updateCartStatus(int cartId, String status, int tableId) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      showLoaderDialog(context);
      try {

        await context.read<CartViewProvider>().updateCartStatus(status: status);

        if(status == OrderStatus.processed){
          CartData? _cartData = context.read<CartViewProvider>().cartData;
          if(_cartData != null && _cartData.id !=0) {
            widget.cartId =-1;
            context.read<CartViewProvider>().setCartData(null);
            context.read<CartViewProvider>().resetCartUserSelection();
            _unsubscribeFromCartChannel();
          }
        }

        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// add service charge dialog
  _addServiceChargeDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 10,),
          backgroundColor: ColorConstant.colorThemeWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              isBigScreenResolution(context) ? 12.0 : 11.0,
            ),
          ),
          child: Container(
            child: AddServiceChargeContent(
              onButtonPressed: () {
                Navigator.of(context).pop();
                debugPrint(':: service charge added  ::');
                _addServiceCharge();
              },
            ),
          ),
        );
      },
    );
  }

  /// add tip dialog
  _addTipDialog() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 10,),
            backgroundColor: ColorConstant.colorThemeWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                isBigScreenResolution(context) ? 12.0 : 11.0,),
            ),
            child: Container(
              child: AddTipContent(onButtonPressed: (){
                Navigator.of(context).pop();
                debugPrint(':: tip added  ::');
                _addTip();
              },),
            ),
          );
        });
  }

  /// add service charge
  void _addServiceCharge() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context
            .read<CartViewProvider>()
            .addServiceCharge(cartId:widget.cartId);
        await context.read<CartViewProvider>().getCart(id: widget.cartId);
        context.read<CartViewProvider>().resetAddServiceChargeDialog();
        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// apply pro dialog
  _applyPromoDialog() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 10,),
            backgroundColor: ColorConstant.colorThemeWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                isBigScreenResolution(context) ? 12.0 : 11.0,),
            ),
            child: Container(
              child: ApplyPromoView(onButtonPressed: (promoCode) {
                CartData? cartData = context.read<CartViewProvider>().cartData;
                context.read<DiscountProvider>().setOrder(cartData);
                debugPrint(':: promo code apply  ::');
                _applyPromoCode(promoCode);
              },),
            ),
          );
        });
  }

  /// apply preset discount dialog
  _applyPresetDiscountDialog() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 10,),
            backgroundColor: ColorConstant.colorThemeWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                isBigScreenResolution(context) ? 12.0 : 11.0,),
            ),
            child: Container(
              child: ApplyPresetDiscountView(onButtonPressed: (promoCode) {
                debugPrint(':: preset code apply  ::');
              },),
            ),
          );
        });
  }

  /// apply preset discount dialog
  _applyCustomDiscountOptionDialog() {
    context.read<DiscountProvider>().resetDiscountOptions();
    context.read<DiscountProvider>().setCustomNextPressed(false);
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 10,),
            backgroundColor: ColorConstant.colorThemeWhite,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                isBigScreenResolution(context) ? 12.0 : 11.0,),
            ),
            child: DisocuntOptionView(),
          );
        });
  }

  /// remove service charge
  void _removeServiceCharge() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        CartData? cartData = context.read<CartViewProvider>().cartData;
        if(cartData !=null) {
          if(cartData.cartServiceCharge != null ){
            await context
                .read<CartViewProvider>()
                .removeServiceCharge(serviceChargeId: cartData!.cartServiceCharge!.id!);
          }
        }
        await context.read<CartViewProvider>().getCart(id: widget.cartId);
        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// add tip
  void _addTip() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        await context
            .read<CartViewProvider>()
            .addTip(cartId: widget.cartId);
        await context.read<CartViewProvider>().getCart(id: widget.cartId);
        context.read<CartViewProvider>().resetAddTipDialog();
        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove tip
  void _removeTip() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        CartData? cartData = context.read<CartViewProvider>().cartData;
        if(cartData !=null) {
          if(cartData.cartTip != null ){
            await context
                .read<CartViewProvider>()
                .removeTip(tipId: cartData!.cartTip!.id!);
          }
        }
        await context.read<CartViewProvider>().getCart(id: widget.cartId!);
        if (!mounted) return;
        hideLoaderDialog(context);
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// Show promo code button at bottom menu bar
  Map<String, bool> _applyPromoCodeBottomMenuItemLabel() {
    if(context.read<CartViewProvider>().cartData!.cartPromoCode == null){
      return {BottomSheetString.applyPromo: true};
    }
    return {};
  }

  /// Show custom discount button at bottom menu bar
  Map<String, bool> _applyCustomDiscountBottomMenuItemLabel() {
    if(context.read<CartViewProvider>().cartData!.cartCustomDiscount == null){
      return {BottomSheetString.applyCustomDiscount: true};
    }
    return {BottomSheetString.applyCustomDiscount: true};
  }

  /// Show add item button at bottom menu bar
  Map<String, bool> _addItemsBottomMenuItemLabel() {
    if(context.read<CartViewProvider>().isAddItemsMenuButtonEnable()){
      return {BottomSheetString.addItems: true};
    }
    return {};
  }

  /// Send to kitchen show/hide label in bottom menu logic
  Map<String, bool> _sendToKitchenBottomMenuItemLabel() {
    if(context.read<CartViewProvider>().isSendToKitchenMenuButtonEnable()){
      return {BottomSheetString.sendToKitchen: true};
    }
    return {};
  }

  /// Service charges show/hide label in bottom menu logic
  Map<String, bool> _serviceChargeBottomMenuItemLabel() {
    final cartViewProvider = context.read<CartViewProvider>();

    if (cartViewProvider.cartData?.cartServiceCharge != null) return {};

    bool allPendingEmpty = context.read<CartViewProvider>().pendingCartItemMap.values.every((e) => e.isEmpty);
    bool allSubmittedEmpty = context.read<CartViewProvider>().submittedCartItemMap.values.every((e) => e.isEmpty);
    if(allPendingEmpty && allSubmittedEmpty == false) {
      return  {BottomSheetString.addServiceCharge: true};
    }
    return {};
  }


  /// Tips show/hide label in bottom menu logic
  Map<String, bool> _tipsBottomMenuItemLabel() {
    final cartViewProvider = context.read<CartViewProvider>();

    if (cartViewProvider.cartData?.cartTip != null) return {};

    bool allPendingEmpty = context.read<CartViewProvider>().pendingCartItemMap.values.every((e) => e.isEmpty);
    bool allSubmittedEmpty = context.read<CartViewProvider>().submittedCartItemMap.values.every((e) => e.isEmpty);
    if(allPendingEmpty && allSubmittedEmpty == false) {
      return  {BottomSheetString.addTips: true};
    }
    return {};
  }

  // _handleNavigationBar(){
  //   return NavigationMenu(onMenuSelected: (selectedMenu) {
  //     switch (selectedMenu){
  //       case BottomBarNavigationString.more:
  //         debugPrint('Cart in page $selectedMenu');
  //         context.read<BottomSheetProvider>().setDialogForCartPage(!context.read<BottomSheetProvider>().showDialogCart) ;
  //         break;
  //      }
  //     },
  //   );
  // }

  /// Navigation item selection
  _handleNavigationBar(){
    return NavigationMenu(onMenuSelected: (selectedMenu) {
      switch (selectedMenu) {
        case BottomBarNavigationString.order:
          context.read<BottomSheetProvider>().setDialogForHomePage(false);
          context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.order);
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
          break;
        case BottomBarNavigationString.more:
          debugPrint('Cart in page $selectedMenu');
          context.read<BottomSheetProvider>().setDialogForCartPage(!context.read<BottomSheetProvider>().showDialogCart);
          break;
        case BottomBarNavigationString.history:
          context.read<BottomSheetProvider>().setDialogForHomePage(false);
          context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.history);
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
          break;
        case BottomBarNavigationString.alerts:
          context.read<BottomSheetProvider>().setDialogForHomePage(false);
          context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.alerts);
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
          break;
        default:
          context.read<BottomSheetProvider>().setDialogForHomePage(false);
          context.read<HomeScreenProvider>().updateSelectedPage(BottomBarNavigationString.order);
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
      }
    },
    );
  }

  /// show promotions for items
  _showPromotions(CartItems itemData) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [

        /// item promotions  - preset regular discount
        if (null != context.read<CartViewProvider>().cartDiscount) ...[
          if ((context.read<CartViewProvider>().cartDiscount!.applyOn! == PromotionApplyOn.product.name) ||
              (context.read<CartViewProvider>().cartDiscount!.applyOn! ==
                  PromotionApplyOn.multiple_product_qty.name)) ...[
            if (null != itemData.cartDiscountItem &&
                0 != itemData.cartDiscountItem?.discount!) ...[
              SizedBox(
                height: isBigScreenResolution(context) ? 10 : 6,
              ),
              _itemPromotionView(
                type: PromotionsString.preset,
                label: context.read<CartViewProvider>().cartDiscount!.title,
                onCart: false,
                color: ColorConstant.colorBlueDark,
                value: itemData.cartDiscountItem!.discount,
              ),
            ],
          ],
        ],

        /// item promotions - custom discount
        if (itemData.cartCustomDiscountItem!.isNotEmpty) ...[
          ...itemData.cartCustomDiscountItem!.map((discountItem) {
            if ((discountItem.applyOn == PromotionApplyOn.product.name) ||
                (discountItem.applyOn ==
                    PromotionApplyOn.multiple_product_qty.name)) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: isBigScreenResolution(context) ? 10 : 8,
                  ),
                  _itemPromotionView(
                    type: PromotionsString.custom,
                    label: PromotionsString.custom_discount,
                    onCart: discountItem.applyOn!.toLowerCase() ==
                        PromotionApplyOn.cart.name,
                    color: ColorConstant.colorBlueDark,
                    value: discountItem.appliedValue,
                  ),
                ],
              );
            } else {
              return Container();
            }
          }),
        ],

        /// item rewards
        // if(null != itemData.cartRewardItem)...[
        //   if(null !=  context.read<CartViewProvider>().cartReward)...[
        //     if( context.read<CartViewProvider>().cartReward!.rewardType!.toLowerCase() == RewardType.item.name)...[
        //       if(0!= itemData.cartRewardItem!.discount!)...[
        //         SizedBox(
        //           height: isBigScreenResolution(context) ? 10 : 6,
        //         ),
        //         _itemPromotionView(
        //             type: PromotionsString.reward,
        //             label: context.read<CartViewProvider>().cartReward!.programTierName!,
        //             onCart: false,
        //             color: ColorConstant.colorBlueDark,
        //             value: itemData.cartRewardItem!.discount,
        //             points: '${context.read<MembershipPointsProvider>().formatAmount(context.read<CartDataViewProvider>().cartReward!.points!)}'
        //         ),
        //       ],
        //     ],
        //   ],
        // ],
      ],
    );
  }

  /// item level promotion view
  _itemPromotionView({
    String type = '',
    bool onCart = false,
    String? label = PromotionsString.custom,
    Color color =ColorConstant.colorGrayDark,
    num? value = 0,
    String points=''
  }) {
    return Container(
      margin: EdgeInsets.only(
        left: isBigScreenResolution(context) ? 10 : 7,
        right: isBigScreenResolution(context) ? 10 : 7,
      ),
      padding: EdgeInsets.all(
        isBigScreenResolution(context) ? 10.0 : 7,
      ),
      decoration: DottedDecoration(
        shape: Shape.box,
        strokeWidth: 0.50,
        color: ColorConstant.colorBlueDark,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            onCart ? Icons.shopping_cart_sharp : Icons.discount_sharp,
            size: isBigScreenResolution(context) ? 16 : 12,
            color: color,
          ),
          const SizedBox(
            width: 5,
          ),
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                  ),
                ),
                Flexible(
                  child: Text(
                    ' $label '.replaceAll("", "\u{200B}"),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.smallTextStyle.copyWith(
                      color: color,
                      fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                    ),
                  ),
                ),
                Text(
                  points.isEmpty ? '(-\$${roundDouble(value!)})' : '(-$points pts)',
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                  ),
                ),
                const SizedBox(
                  width: 5,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// apply promo code
  _applyPromoCode(String promoCode) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        CartPromoCodeResponse _cartPromoCodeResponse = await context
            .read<DiscountProvider>()
            .applyPromoCode(promoCode: promoCode, );
        hideLoaderDialog(context);
        context.read<DiscountProvider>().setSuccessMsg(_cartPromoCodeResponse.message!);
        await context.read<CartViewProvider>().getCart(id: widget.cartId!);
        if(Navigator.canPop(context)){
          Navigator.of(context).pop(true);
        }
      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        context.read<DiscountProvider>().setPromoCodeInputError(true, error.toString());
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        context.read<DiscountProvider>().setPromoCodeInputError(true, error.toString());
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        context.read<DiscountProvider>().setPromoCodeInputError(true, error.toString());
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove promotions
  void _removePromotions(
      {required bool forCustomDisc,
        required bool forPromo,
        required num? id}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {
        if (forCustomDisc) {
          await context
              .read<DiscountProvider>()
              .removeCustomDiscount(customDiscountId: id!);
        }else if (forPromo) {
          await context.read<DiscountProvider>().removePromoCode(id: id!);
        } else {
         // await context.read<DiscountProvider>().removeDiscount(id: id!);
        }

        /// recall cart and save data to local for cart
        CartData? cartData= context.read<CartViewProvider>().cartData;
        if(cartData!=null && cartData.id !=0) {
          await context.read<CartViewProvider>().getCart(
              id: cartData.id!);
          //await context.read<TakeOutDeliveryViewProvider>().resetCurrentPage();
          //await context.read<TakeOutDeliveryViewProvider>().getCartList();
        }

        hideLoaderDialog(context);
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove discount
  void _removeDiscount() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        if(null != context.read<CartViewProvider>().cartData) {
          await context.read<CartViewProvider>().removeDiscount(id: context.read<CartViewProvider>().cartData!.cartDiscount!.id!);
        }


        hideLoaderDialog(context);

        if(null != context.read<CartViewProvider>().cartData) {
          /// recall order and save data to local for order
          await context.read<CartViewProvider>().getCart(id:context.read<CartViewProvider>().cartData!.id!);
        }

      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove custom discount
  void _removeCustomDiscount({required num discountId}) async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        if(null != context.read<CartViewProvider>().cartData) {
          await context.read<DiscountProvider>().removeCustomDiscount(customDiscountId: discountId);
        }

        hideLoaderDialog(context);

        if(null != context.read<CartViewProvider>().cartData) {
          /// recall order and save data to local for order
          await context.read<CartViewProvider>().getCart(id:context.read<CartViewProvider>().cartData!.id!);
        }

      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  /// remove membership points
  void _removeRedeemPoints({required num? id}) async {

    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();
    if (hasConnected) {
      showLoaderDialog(context);
      try {

        await context
            .read<CartViewProvider>()
            .removeLoyaltyFromOrder(loyaltyId: id!);

        /// recall order and save data to local for order
        if(null != context.read<CartViewProvider>().cartData) {
          /// recall order and save data to local for order
          await context.read<CartViewProvider>().getCart(id:context.read<CartViewProvider>().cartData!.id!);
        }

        hideLoaderDialog(context);
      }  on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Badrequest data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }

  @override
  void dispose() {
     _unsubscribeFromCartChannel();
     _destroyEmitListener();
    if(navigatorKey.currentContext != null) {
      navigatorKey.currentContext?.read<CartViewProvider>().resetCartData();
    }
     _otherPaymentAmountTextFieldFocus.dispose();
     _debounce.dispose();
     _otherPaymentAmountFocusStreamController.close();
    super.dispose();
  }
}
