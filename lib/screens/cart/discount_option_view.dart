import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';
import 'package:gr8tables_server_manager/screens/cart/cart_custom_discountview.dart';
import 'package:provider/provider.dart';
import '../../constants/app_color.dart';
import '../../constants/app_dimens.dart';
import '../../constants/app_string.dart';
import '../../constants/permission_strings.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../helpers/permission_checker.dart';
import '../../models/cart/cart_data.dart';
import '../../models/discount/cart_discount_response.dart';
import '../../models/discount/discount_response.dart';
import '../../my_app.dart';
import '../../providers/discount/discount_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import 'item_custom_discountview.dart';

class DisocuntOptionView extends StatefulWidget {


  DisocuntOptionView({super.key});

  @override
  State<DisocuntOptionView> createState() =>
      _DisocuntOptionViewState();
}

class _DisocuntOptionViewState extends State<DisocuntOptionView> {
  @override
  void initState() {
    debugPrint("CustomDiscountView initState()");
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context
          .read<DiscountProvider>()
          .setOrder(context.read<CartViewProvider>().cartData);
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    if(context.watch<DiscountProvider>().hasCustomNextPressed) {
      if(context.watch<DiscountProvider>().customDiscountOnCartSelected) {
        return  CartCustomDiscountView();
      }else if(context.watch<DiscountProvider>().customDiscountOnItemSelected) {
        return  ItemCustomDiscountView();
      }else{
        return _optionalSelectionView();
      }
    }

    return _optionalSelectionView();
  }

  /// Cart and item selection view
  _optionalSelectionView(){
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Align(
          alignment: Alignment.bottomRight,
          child: Padding(
            padding: const EdgeInsets.only(right: 15,top: 15, bottom: 10),
            child: GestureDetector(
              onTap: (){
                if(Navigator.canPop(context)){
                  Navigator.pop(context);
                }
              },
              child: Icon(Icons.close,size: isBigScreenResolution(context) ? 20.0 : 16.0,
                color: ColorConstant.colorBlueDark,),
            ),
          ),
        ),
        Container(
          margin:  EdgeInsets.only(bottom: isBigScreenResolution(context) ?20 : 10,),
          child: Text(
            DiscountOptionViewString.selectOption,
            maxLines: 1,
            style: AppTextStyle.smallTextStyle.copyWith(
                fontSize: isBigScreenResolution(context)
                    ? 16.0
                    : 14.0,
                color: ColorConstant.colorBlueDark
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment. start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cart Option
              _discountSelectionOption(
                label: DiscountOptionString.optionCart,
                icon: Icons.shopping_cart_outlined,
                isSelected: context.watch<DiscountProvider>().customDiscountOnCartSelected,
                onTap: () {
                  if (!context.read<DiscountProvider>().customDiscountOnCartSelected) {
                    context.read<DiscountProvider>().setCustomDiscountIOnCartSelected();
                  }
                },
              ),
              SizedBox(height: isBigScreenResolution(context) ? 40 : 30),
              // Item Option
              _discountSelectionOption(
                label: DiscountOptionString.optionItem,
                icon: Icons.list_outlined,
                isSelected: context.watch<DiscountProvider>().customDiscountOnItemSelected,
                onTap: () {
                  if (!context.read<DiscountProvider>().customDiscountOnItemSelected) {
                    context.read<DiscountProvider>().setCustomDiscountIOnItemSelected();
                  }
                },
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,  // Center the button
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.read<DiscountProvider>().resetCustomDiscount();
                    context.read<DiscountProvider>().setCustomNextPressed(true);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 18.0),  // Add padding around the button text
                    decoration: BoxDecoration(
                      color: ColorConstant.colorBlueDark,
                      borderRadius: BorderRadius.circular(5.0),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 5,
                          offset: Offset(0, 2),  // Slight elevation effect
                        ),
                      ],
                    ),
                    child: Text(
                      DiscountOptionString.next.toUpperCase(),
                      textAlign: TextAlign.center,
                      style: AppTextStyle.mediumTextStyle.copyWith(
                        fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                        letterSpacing: 1.2,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _discountSelectionOption({
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_8,
          borderRadius: BorderRadius.circular(5.0),
          border: Border.all(
            color: isSelected ? ColorConstant.colorBlueLight_8 : ColorConstant.colorThemeGray,
            width: 1,
          ),
          boxShadow: isSelected
              ? []
              : [
            BoxShadow(
              color: ColorConstant.dropShadowColor.withOpacity(0.1),
              offset: const Offset(0.0, 0.0),
              blurRadius: 5.0,
              spreadRadius: 0.0,
            ),
          ],
        ),
        child: _cartItemProtoType(label, icon, isSelected ? ColorConstant.colorThemeWhite : ColorConstant.colorBlueDark),
      ),
    );
  }


  /// cart item prototype view
  Widget _cartItemProtoType(String name, IconData icon, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Icon(icon , size: isBigScreenResolution(context) ? 40 : 30, color:color,)),
        const SizedBox(height: 15,),
        Expanded(
          child: Align(
            alignment: Alignment.topLeft,
            child: Text(
              name,
              maxLines: 1,
              style: AppTextStyle.mediumTextStyle.copyWith(
                fontSize: isBigScreenResolution(context)
                    ? 20.0
                    : 18.0,
                color: color,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

