import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../models/cart/cart_data.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';
import '../../utils/datetime_utils.dart';

class TakeoutView extends StatefulWidget {

  DineInTakeoutProvider? providerData;
  TakeoutView({super.key, this.providerData});

  @override
  State<TakeoutView> createState() => _TakeoutViewState();
}

class _TakeoutViewState extends State<TakeoutView> {

   late BuildContext _context;
   late ScrollController _scrollController ;



  @override
  void initState() {
    _scrollController  = ScrollController();
    _scrollController.addListener(_loadMore);
    super.initState();
  }

  /// load more data
  void _loadMore() async{
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent &&
        !widget.providerData!.isLoading) {
      await widget.providerData!.setCurrentPage('');

    }
  }

  @override
  Widget build(BuildContext context) {
    _context = context;
    if(widget.providerData!.cartList.isEmpty) {
      return Center(
        child: Text(DineInTakeoutString.noOrdersYet,
          style: AppTextStyle.smallTextStyle.copyWith(
            color: ColorConstant.colorBlueLight_50,
            fontSize: isBigScreenResolution(context) ? 16 : 14.0,
            letterSpacing: 0.3,
          ),
        ),
      );
    }
    return Padding(
      padding: EdgeInsets.only(top: _equalSizePadding8 ,
          left: _equalSizePadding8, right: _equalSizePadding8,),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            Flexible(
              child: ListView.builder(
                controller: _scrollController,
                 shrinkWrap: true,
                  itemCount: widget.providerData!.cartList.length + (widget.providerData!.isLoading ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == widget.providerData!.cartList.length) {
                      debugPrint(
                          ': index == list length $index ${widget.providerData!.cartList.length}:');
                      return  Container(
                        padding:  const EdgeInsets.symmetric(vertical: 10),
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: ColorConstant.colorBlueDark,
                          ),
                        ),
                      );
                    }
                     return _cartListItem(widget.providerData!.cartList[index]);
                  },
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// cart list item
  Widget _cartListItem(CartData cartData) {
    return GestureDetector(
       onTap: (){
         navigateToCartScreen(_context, cartData.id!, -1);
       },
      child: Container(
        decoration: BoxDecoration(
          color: ColorConstant.colorThemeWhite,
          border: Border.all(
            color: ColorConstant.colorBlueLight_8,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(isBigScreenResolution(_context) ? 8 : 6,),
        ),
        margin: EdgeInsets.only(bottom: isBigScreenResolution(_context) ? 8 : 6,),
        child: IntrinsicHeight(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(isBigScreenResolution(_context)? 10: 8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            DineInTakeoutString.orderId.replaceAll("#", cartData.id.toString()),
                            style: AppTextStyle.largeTextStyle.copyWith(
                              fontSize: isBigScreenResolution(_context) ? 16 : 14,
                              color: ColorConstant.colorBlueDark,
                              overflow: TextOverflow.ellipsis,
                              letterSpacing: 0.15,
                            ),
                            maxLines: 2,
                          ),
                          Expanded(
                            child: Text(
                              NumberFormat.simpleCurrency().format(cartData.total),
                              style: AppTextStyle.largeTextStyle.copyWith(
                                fontSize: isBigScreenResolution(_context) ? 16 : 14,
                                color: ColorConstant.colorBlueDark,
                                overflow: TextOverflow.ellipsis,
                                letterSpacing: 0.15,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                      _verticalSpacing8,
                     _orderTableTextView(
                        title:
                        DineInTakeoutString.placedDate.replaceAll("%s",
                          DateTimeUtils.utcTimeToConvertLocalTime(
                              cartData.platform == ConstantString.platformWeb
                                  ? cartData.submittedAt?? 'n/a'
                                  : cartData.createdAt ?? 'n/a',
                              formattedString: DateTimeUtils
                                  .at_yyyy_MM_dd_hh_mm_a),
                        ),
                        fontSize: isBigScreenResolution(_context) ? 12 : 10,
                      ),
                      _verticalSpacing8,

                      Text(
                        widget.providerData!.getCustomerName(cartData.user),
                        style: AppTextStyle.largeTextStyle.copyWith(
                          fontSize: isBigScreenResolution(_context) ? 16 : 14,
                          color: ColorConstant.colorBlueDark,
                          overflow: TextOverflow.ellipsis,
                          letterSpacing: 0.15,
                        ),
                      ),
                      _verticalSpacing8,
                      _orderTableTextView(
                        title: cartData.user != null ? cartData.user!.email : '',
                        fontSize: isBigScreenResolution(_context) ? 14 : 12,
                      ),
                      _verticalSpacing8,
                      _orderTableTextView(
                        title: cartData.user != null ? cartData.user!.phone : '',
                        fontSize: isBigScreenResolution(_context) ? 14 : 12,
                      ),
                    ],
                  ),
                ),
              ),
              _verticalDivider(),
              Container(
                padding: EdgeInsets.all(isBigScreenResolution(context) ? 10 : 8),
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal:isBigScreenResolution(_context)?10:8,
                          vertical:isBigScreenResolution(_context)?8:6),
                      decoration: BoxDecoration(
                        color: cartData.paymentReceived! ? ColorConstant.colorGreenLight_3 :
                        ColorConstant.colorRedDark_5,
                        borderRadius: BorderRadius.circular(isBigScreenResolution(_context)? 20 : 18),
                      ),
                      child: Text(
                        cartData.paymentReceived! ? DineInTakeoutString.paid: DineInTakeoutString.unpaid,
                        style: AppTextStyle.smallTextStyle.copyWith(
                          color: ColorConstant.colorThemeWhite,
                          fontSize: isBigScreenResolution(_context) ? 14:12,
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.symmetric(vertical: isBigScreenResolution(_context)? 14.0:12.0),
                      child: Column(
                        children: [
                          Image.asset(
                            cartData.orderReceiveMethod == OrderReceiveMethod.delivery
                                ?  AssetIcons.delivery : AssetIcons.pickup,
                            color: cartData.receiveLater! ? ColorConstant.colorBlueDark : ColorConstant.colorRedDark_5,
                            width: 20.0,
                            height: 20.0,
                          ),
                          SizedBox(height: isBigScreenResolution(_context)? 6.0:4.0,),
                          Text(
                            cartData.receiveLater!
                                ? DateTimeUtils.utcTimeToConvertLocalTime(
                                cartData.receiveTime ?? DateTime.now().toUtc().toString(),
                                formattedString: DateTimeUtils.hh_mm_a)
                                : DineInTakeoutString.asap,
                            style: AppTextStyle.smallTextStyle.copyWith(
                                color: cartData.receiveLater! ? ColorConstant.colorBlueDark : ColorConstant.colorRedDark_5,
                                fontSize: isBigScreenResolution(_context) ? 14:12
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(DineInTakeoutString.totalItems.replaceAll("%d", cartData.itemCount.toString()),
                      style: AppTextStyle.smallTextStyle.copyWith(
                          color: ColorConstant.colorBlueDark,
                          fontSize: isBigScreenResolution(_context) ? 14:12
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// order item text view
  Widget _orderTableTextView({String? title, double? fontSize, double? letterSpacing}) {
    return Text(
      title!,
      style: AppTextStyle.smallTextStyle.copyWith(
        fontSize: fontSize,
        color: ColorConstant.colorBlueDark,
        letterSpacing: letterSpacing,
      ),
    );
  }

   /// vertical divider
   Widget _verticalDivider() {
     return  Container(
       height: double.infinity,
       width: 1,
       child: const VerticalDivider(thickness: 1,color: ColorConstant.colorBlueLight_8,),);
   }

  double get _equalSizePadding8 => isBigScreenResolution(_context) ? 8 : 6;

  Widget get _verticalSpacing8 => SizedBox(height: isBigScreenResolution(_context) ? 7 : 6,);
}
