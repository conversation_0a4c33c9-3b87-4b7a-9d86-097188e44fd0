import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/constants/app_color.dart';
import 'package:gr8tables_server_manager/providers/cartview/cart_view_provider.dart';
import 'package:gr8tables_server_manager/providers/common/shareable_provider.dart';
import 'package:gr8tables_server_manager/providers/homescreen/bottomview_provider.dart';
import 'package:gr8tables_server_manager/providers/menuview/menu_view_provider.dart';
import 'package:gr8tables_server_manager/providers/package_info/package_info_provider.dart';
import 'package:gr8tables_server_manager/providers/payment/payment_provider.dart';
import 'package:gr8tables_server_manager/utils/app_routes.dart';
import 'package:provider/provider.dart';

import '../../constants/app_string.dart';
import '../../custom_widgets/bottom_sheet_widget.dart';
import '../../custom_widgets/logout/logout_dialog_overlay.dart';
import '../../custom_widgets/nav_menu.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../my_app.dart';
import '../../providers/auth/auth_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/homescreen/bottom_sheet_provider.dart';
import '../../providers/pubnub/pubnub_provider.dart';
import '../../services/graph_ql/graph_ql_service.dart';
import '../../utils/app_utils.dart';
import '../../utils/pref_utils.dart';
import 'dine_in_view.dart';
import 'takeout_view.dart';

class DineInTakeoutView extends StatefulWidget {
  const DineInTakeoutView({super.key});

  @override
  State<DineInTakeoutView> createState() => _DineInTakeoutViewState();
}

class _DineInTakeoutViewState extends State<DineInTakeoutView>
    with SingleTickerProviderStateMixin {

  late TabController _tabController;

  @override
  void initState() {
    debugPrint("DineInTakeoutView initState()");
    _tabController = TabController(length: 2, vsync: this);
   // _getFloors();

    // TODO: remove below code when home screen content is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var businessLocationData = context.read<AuthProvider>();
      if(businessLocationData.isBusinessSelected() && businessLocationData.isLocationSelected()) {
        _getFloors();
      }
    });

    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: isBigScreenResolution(context) ? 8 : 6,
                right: isBigScreenResolution(context) ? 8 : 6,
                top: isBigScreenResolution(context) ? 8 : 6,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _tabBar(),
                  Expanded(child: _tabContainer()),
                ],
              ),
            ),
            if(context.watch<BottomSheetProvider>().showDialogDine)...[
              Align(
                alignment: Alignment.bottomCenter,
                child: _handleBottomSheetMenu(),
              ),
            ],
          ],
        ),
        // bottomNavigationBar: _handleNavigationBar(),
        // bottomNavigationBar: customBottomAppBar(
        //   context: context, rightLabelValue: 'options',
        //   rightButtonPressed: (){}, leftLabelValue: 'options',
        //   leftButtonPressed: (){},
        //   actionButtonPressed: (){
        //       if(context.read<BottomSheetProvider>().showDialogDine){
        //         context.read<BottomSheetProvider>().setDialogForDinePage(false);
        //       }else{
        //         context.read<BottomSheetProvider>().setDialogForDinePage(true);
        //       }
        //   },
        //   enableBottomSheet: context.watch<BottomSheetProvider>().showDialogDine
        // ),
      ),
    );
  }

   /// tab bar
   Widget _tabBar(){
       return TabBar(
         controller: _tabController,
         isScrollable: true,
         tabAlignment: TabAlignment.center,
         physics: const NeverScrollableScrollPhysics(),
         dividerColor: Colors.transparent,
         indicatorColor: ColorConstant.colorBlueDark,
         indicatorSize: TabBarIndicatorSize.label,
         tabs: const [
            Tab(text: DineInTakeoutString.tabDineIn,),
            Tab(text: DineInTakeoutString.tabTakeout,),
         ],
       );
   }

  /// tab bar container
  Widget _tabContainer(){
    return Consumer<DineInTakeoutProvider>(
      builder: (context, data, child) {
        return TabBarView(
          controller: _tabController,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            DineInView(providerData: data,),
            TakeoutView(providerData: data,),
          ],
        );
      },
    );

  }

  /// get floors
  Future<void> _getFloors() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if (!mounted) return;
       showLoaderDialog(context);
      try {

        debugPrint(':: get floors::');
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getFloorWithTables();
        if(!mounted) return;
        await context.read<DineInTakeoutProvider>().getCartList();

        if(!mounted) return;
        hideLoaderDialog(context);


      } on UnauthorisedException catch (error) {
        if (!mounted) return;
         hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
         hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
         hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// handle bottom sheet menu
  _handleBottomSheetMenu(){
    final Map<String, bool> menuItems = {
      BottomSheetString.logout: true,
    };

    return BottomSheetContent(menuItems: menuItems, onMenuItemSelected: (selectedMenu) {
      debugPrint("Selected Menu: $selectedMenu");
          context.read<BottomSheetProvider>().setDialogForDinePage(false);
          Future.delayed(const Duration(milliseconds: 100), () {
                showGeneralDialog(
                    context: context,
                    barrierDismissible: false,
                    barrierColor: Colors.black.withOpacity(0.5),
                    transitionDuration: const Duration(milliseconds: 200),
                    pageBuilder: (BuildContext buildContext,
                        Animation animation, Animation secondaryAnimation) {
                      return LogOutDialogOverlay(
                        onPositiveButtonClick: () async {
                          // Navigator.of(context).pop();
                          // /// in case of user log out from app
                          _clearAll();
                          navigateToLogin(context);
                        },
                        onNegativeButtonClick: () {
                          Navigator.of(context).pop();
                        },
                      );
                  });
      });
       },
    );

  }

  _clearAll(){
    PrefsUtils.removeKey(PrefKeys.authToken);
    PrefsUtils.clear();
    context.read<AuthProvider>().reset();
    context.read<CartViewProvider>().reset();
    context.read<ShareableProvider>().reset();
    context.read<DineInTakeoutProvider>().reset();
    context.read<BottomSheetProvider>().reset();
    context.read<BottomViewProvider>().reset();
    context.read<MenuViewProvider>().reset();
    context.read<PackageInfoProvider>().reset();
    context.read<PubNubProvider>().reset();
    context.read<PaymentProvider>().reset();
    GraphQLService().reset();
  }

  @override
  void dispose() {
    debugPrint("DineInTakeoutView dispose()");
    _tabController.dispose();
    super.dispose();
  }
}
