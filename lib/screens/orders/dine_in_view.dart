import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/utils/app_routes.dart';
import 'package:provider/provider.dart';
import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../models/dinein_takeout/floors_response.dart';
import '../../models/dinein_takeout/table_data.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/homescreen/bottom_sheet_provider.dart';
import '../../utils/app_utils.dart';
import '../../utils/datetime_utils.dart';

class DineInView extends StatelessWidget {

  DineInTakeoutProvider? providerData;
  DineInView({super.key, this.providerData});

  late BuildContext _context;

  @override
  Widget build(BuildContext context) {
    _context = context;

    if(providerData!.floorList.isEmpty) {
      return Center(
        child: Text(DineInTakeoutString.noTablesYet,
          style: AppTextStyle.smallTextStyle.copyWith(
            color: ColorConstant.colorBlueLight_50,
            fontSize: isBigScreenResolution(context) ? 16 : 14.0,
            letterSpacing: 0.3,
          ),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
          _equalSizeSpacing15,
          _floorsView(),
          _equalSizeSpacing15,
          Flexible(child: _tablesView(),),
      ],
    );
  }
  /// floors view
  Widget _floorsView() {
    return Container(
      alignment: Alignment.topLeft,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          alignment: WrapAlignment.center,
          spacing: 8,
          direction: Axis.horizontal,
          children: [
            ...providerData!.floorList.map(
                  (e) => _floorItemView(e),),
          ],
        ),
      ),
    );
  }

  /// floor item view
  Widget _floorItemView(FloorData floor,) {
    return GestureDetector(
      onTap: () {
        providerData?.markFloorSelected(floor);
      },
      child: Container(
        constraints: BoxConstraints(
          minWidth: isBigScreenResolution(_context) ? 90 : 80,
          maxWidth: isBigScreenResolution(_context) ? 90 : 80,
        ),
        padding: EdgeInsets.symmetric(
            vertical: isBigScreenResolution(_context) ? 7 : 5,
            horizontal: isBigScreenResolution(_context) ? 6 : 4),
        decoration: BoxDecoration(
          color: floor.isSelected!
              ? ColorConstant.colorBlueDark
              : ColorConstant.colorThemeWhite,
          borderRadius: BorderRadius.circular(isBigScreenResolution(_context)? 6: 4),
          border: Border.all(
            color: floor.isSelected!
                ? ColorConstant.colorBlueDark
                : ColorConstant.colorBlueDark,
          ),),
        alignment: Alignment.center,
        child: AutoSizeText(
          floor.name!,
          maxLines: 1,
          maxFontSize : isBigScreenResolution(_context) ? 14.0 : 12.0,
          minFontSize: isBigScreenResolution(_context) ? 12.0 : 10.0,
          style: TextStyle(
            color: floor.isSelected!
                ? ColorConstant.colorThemeWhite
                : ColorConstant.colorBlueDark,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// tables view
  Widget _tablesView() {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Wrap(
            alignment: WrapAlignment.start,
            crossAxisAlignment: WrapCrossAlignment.start,
            spacing: 10,
            runSpacing: 10,
            children: [
              if(providerData!.getFloorTables().isNotEmpty)...[
                ...providerData!.getFloorTables().map((e) => _tableRowView(
                  e,
                ),),
              ],

            ],
          ),
        ],
      ),
    );
  }

  /// table row view
  Widget _tableRowView(TableData tableData) {
    return GestureDetector(
      onTap:  () async{
        _context.read<BottomSheetProvider>().setDialogForCartPage(false);
        navigateToCartScreen(_context, tableData.cartData == null ? -1 :
        tableData.cartData!.id!, tableData.id!);
      },
      child: Container(
        constraints: BoxConstraints(
          minWidth: isBigScreenResolution(_context) ? 110 : 120,
          maxWidth: isBigScreenResolution(_context) ? 110 : 120,
          minHeight: isBigScreenResolution(_context) ? 110 : 120,
          maxHeight: isBigScreenResolution(_context) ? 110 : 120,
        ),
        padding: EdgeInsets.all(isBigScreenResolution(_context) ? 8 : 6,),
        decoration:BoxDecoration(
          color: tableData.cartData == null ? ColorConstant.colorGreenLight : ColorConstant.colorRedLight,
          border:  (!tableData.isSelected!) ? null : Border.all(
            color: ColorConstant.colorBlueLight_50,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(
            isBigScreenResolution(_context) ? 6 : 4,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  AssetIcons.iconDishFill,
                  color: tableData.cartData == null ? ColorConstant.colorDarkGreen_600 : ColorConstant.colorRedDark,
                  width: isBigScreenResolution(_context) ? 16 : 14,
                  height: isBigScreenResolution(_context) ? 16 : 14,
                ),
                SizedBox(width: isBigScreenResolution(_context) ? 4 : 3,),
                Text(
                  tableData.cartData == null? '0' : tableData.cartData!.itemCount.toString(),
                  style: AppTextStyle.largeTextStyle.copyWith(
                    fontSize: isBigScreenResolution(_context) ? 14 : 12,
                    letterSpacing: 1.15,
                    color: tableData.cartData == null ? ColorConstant.colorDarkGreen_600 : ColorConstant.colorRedDark,
                  ),
                ),
                Expanded(child: Container(),),
                Image.asset(
                  AssetIcons.iconPeople,
                  color: tableData.cartData == null ? ColorConstant.colorDarkGreen_600 : ColorConstant.colorRedDark,
                  width: isBigScreenResolution(_context) ? 16 : 14,
                  height: isBigScreenResolution(_context) ? 16 : 14,
                ),
                SizedBox(width: isBigScreenResolution(_context) ? 4 : 3,),
                Text(
                  '${tableData.cartData?.usersCount ?? 0}',
                  style: AppTextStyle.largeTextStyle.copyWith(
                    fontSize: isBigScreenResolution(_context) ? 14 : 12,
                    letterSpacing: 1.15,
                    color: tableData.cartData == null ? ColorConstant.colorDarkGreen_600 : ColorConstant.colorRedDark,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      tableData.label!,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: AppTextStyle.largeTextStyle.copyWith(
                        fontSize: isBigScreenResolution(_context) ? 14 : 13,
                        color:tableData.cartData == null ? ColorConstant.colorDarkGreen_600 : ColorConstant.colorRedDark,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            Container(
              margin: EdgeInsets.only(bottom: isBigScreenResolution(_context) ? 8 : 6,),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Opacity(
                      opacity: tableData.cartData == null ? 0.24 : 1,
                      child: Text(
                        tableData.cartData == null ?'00:00' : DateTimeUtils.getTimeDifference(tableData.cartData!.createdAt),
                        textAlign: TextAlign.center,
                        style: AppTextStyle.largeTextStyle.copyWith(
                          fontSize: isBigScreenResolution(_context) ? 18 : 16,
                          letterSpacing: 0.25,
                          color: tableData.cartData == null ? ColorConstant.colorDarkGreen_600 : ColorConstant.colorRedDark,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          ],
        ),
      ),
    );
  }

  Widget get _equalSizeSpacing15 => SizedBox( height: isBigScreenResolution(_context) ? 15 : 14);


}
