import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../custom_widgets/common_widget.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/cart/cart_data.dart';
import '../../models/menu/menu_data.dart';
import '../../my_app.dart';
import '../../providers/cartview/cart_view_provider.dart';
import '../../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../../providers/menuview/item_modifier_view_provider.dart';
import '../../utils/app_utils.dart';

class ItemModifierView extends StatefulWidget {
  Items itemData;
  String categoryName;
  ItemModifierView({super.key, required this.itemData,
    required this.categoryName});

  @override
  State<ItemModifierView> createState() => _ItemModifierViewState();
}

class _ItemModifierViewState extends State<ItemModifierView> {

  late TextEditingController _notesController;

  @override
  void initState() {
    _notesController = TextEditingController()..text ='';
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<ItemModifierViewProvider>().setItemData(widget.itemData,
          widget.categoryName);
      context.read<ItemModifierViewProvider>().setItemCourseDropDownData();
      context.read<ItemModifierViewProvider>().setItemCourse(widget.itemData.course!);
      context.read<ItemModifierViewProvider>().setCartUserData(
          context.read<CartViewProvider>().getSelectedCartUser());
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: CustomAppBar(
          context: context,
          appBarTitleText:
              context.watch<ItemModifierViewProvider>().itemData ?.name ??'',
        ),
        body: Padding(
          padding: EdgeInsets.all(isBigScreenResolution(context) ? 10 : 8,),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if(context.watch<ItemModifierViewProvider>().itemData != null)
                    ...[
                      _tagsView(),
                      _descriptionView(),
                      _modifiersView(),
                    ],
            ],
          ),
        ),
        bottomNavigationBar: customBottomAppBar(
          context: context, rightLabelValue: ItemModifierViewString.addToOrder,
          rightButtonPressed: (){
            _createCartItem();
          }, leftLabelValue: '',
          enableLeftButton: false,
          enableRightButton: context.watch<ItemModifierViewProvider>().checkItemValidation(),
          leftButtonPressed: (){},actionButtonPressed: (){},
        ),
      ),
    );
  }

   @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  /// modifiers view
  Widget _modifiersView(){
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Consumer<ItemModifierViewProvider>(
              builder: (context, data, child) {
                if (data.itemModifiers.isNotEmpty){
                   return ListView.builder(
                     itemBuilder: (context, index) {
                       return data.itemModifiers[index].status!
                           ? Theme(
                             data: Theme.of(context)
                                 .copyWith(
                                 dividerColor:
                                 Colors.transparent),
                             child: _expandableTileWidget(
                                 index,
                                 data.itemModifiers[index],
                                 data),
                           )
                           : Container();
                     },
                     itemCount: data.itemModifiers.length,
                     shrinkWrap: true,
                     physics: const NeverScrollableScrollPhysics(),
                   );

                }
                return Container();
              },
            ),
             _courseDropDown(),
             _notesView(),
            _plusMinusQtyView(),
          ],
        ),
      ),
    );
  }

 /// expansion tile widget
  Widget _expandableTileWidget(int parentIndex, ItemModifiers itemModifier,
      ItemModifierViewProvider data) {
    debugPrint(
        '::: modifier item length :: ${itemModifier.modifierItems!.length}');
    return ExpansionTile(
      expandedAlignment: Alignment.centerLeft,
      tilePadding: EdgeInsets.zero,
      key: Key(parentIndex.toString()),
      initiallyExpanded: true,
      childrenPadding: EdgeInsets.zero,
      title: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  itemModifier.name ?? '',
                  style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontWeight: FontWeight.w600,
                    fontSize: isBigScreenResolution(context) ? 12 : 11,
                  ),
                ),
              ),
            ],
          ),
          _expandableItemNote(parentIndex, itemModifier, data),
        ],
      ),

      ///modifier items view
      children: [
        ///expandable item note in visibility widget

        Container(
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: itemModifier.modifierItems!.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                mainAxisExtent: 120,
                crossAxisCount: 3,
                mainAxisSpacing: isBigScreenResolution(context) ? 6.0 : 4.0,
                crossAxisSpacing: isBigScreenResolution(context) ? 6.0 : 4.0),
            itemBuilder: (context, childIndex) {
              debugPrint(':: child Index ::  $childIndex::');
               ModifierItems modifierItem =
              itemModifier.modifierItems![childIndex];
              return GestureDetector(
                ///to enable disable modifier item click
                onTap: modifierItem.status!
                    ? () {
                  debugPrint(':: child Index  inside ::  $childIndex::');
                  if (modifierItem.min == 0 &&
                      data.getAllModifierItemQuantity(
                          itemModifier.modifierItems!) <
                          itemModifier.max!) {
                    data.updateSelection(itemModifier,
                        itemModifier.modifierItems![childIndex]);
                  }
                }
                    : null,
                child: Container(
                  decoration: BoxDecoration(
                    ///to enable disable modifier item bg
                      color: modifierItem.status!
                          ? data.isSelected(
                          parentIndex,
                          childIndex,
                          itemModifier.modifierId!,
                          modifierItem.modifierItemId!)
                          ? ColorConstant.colorOrangeLight
                          : ColorConstant.colorLightGray_5x
                          : ColorConstant.blackColor.withOpacity(0.2),
                      borderRadius: const BorderRadius.all(Radius.circular(10.0))),

                  ///child view with single and multi selection [plus(+), minus(-) count view]
                  child: _multiSelectionItem(parentIndex, childIndex,
                      itemModifier, modifierItem, data),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _expandableItemNote(int parentIndex,
      ItemModifiers modifierCategory, ItemModifierViewProvider data) {
    return Visibility(
      child: Container(
        margin: EdgeInsets.only(
          top: isBigScreenResolution(context) ? 2.0 : 1.0,
        ),
        child: modifierCategory.status!
            ? Text(
          data.makeItemLimitNote(modifierCategory),
          style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 11.0 : 10.0,
            color: data.getColorAfterValidation(modifierCategory),
          ),
        )
            : Container(child: const Text('All modifier status is false')),
      ),
    );
  }

  ///this widget contain multi selection with plus, minus count view
  Widget _multiSelectionItem(
      int parentIndex,
      int childIndex,
      ItemModifiers modifierCategory,
      ModifierItems modifierItem,
      ItemModifierViewProvider data) {
    int nonIncludedCount =
    data.getNonIncludedCount(modifierCategory, modifierItem);
    return Container(
      padding: const EdgeInsets.only(bottom: 5.0),
      child: Column(
          mainAxisAlignment: data.isSelected(parentIndex, childIndex,
              modifierCategory.modifierId!, modifierItem.modifierItemId!)
              ? MainAxisAlignment.spaceAround
              : MainAxisAlignment.center,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 5.0, bottom: 0.0),
                child: Align(
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AutoSizeText(
                        ///modifier item name here
                        modifierItem.name!.trim().replaceAll('', '\u{200B}'),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize:
                            isBigScreenResolution(context) ? 14.0 : 12.0,
                            color: modifierItem.status!
                                ? ColorConstant.colorGrayDark
                                : ColorConstant.colorGrayLight),
                      ),
                      Visibility(
                        visible: (modifierCategory.included == 0 ||
                            nonIncludedCount > 0) &&
                            modifierItem.price! > 0,
                        child: AutoSizeText(
                          ///modifier item price here
                          nonIncludedCount == 0
                              ? NumberFormat.simpleCurrency().format(modifierItem.price)
                              : NumberFormat.simpleCurrency().format(modifierItem.price! * nonIncludedCount),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          style: AppTextStyle.smallTextStyle.copyWith(
                              fontSize:
                              isBigScreenResolution(context) ? 14.0 : 12.0,

                              ///to enable disable modifier item text color
                              color: modifierItem.status!
                                  ? ColorConstant.colorGrayDark
                                  : ColorConstant.colorGrayLight),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            ///below widgets are for (minus) itemCount (plus) view
            Visibility(
                visible: modifierItem.status! &&
                    data.isSelected(
                        parentIndex,
                        childIndex,
                        modifierCategory.modifierId!,
                        modifierItem.modifierItemId!),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                                child: _childMinusIconWidget(parentIndex,
                                    childIndex, modifierCategory, modifierItem),
                                flex: 1),
                            Expanded(
                              flex: 1,
                              child: Text(
                                data.getModifierItemQuantity(
                                    modifierCategory, modifierItem),
                                style: AppTextStyle.mediumTextStyle.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: ColorConstant.colorGrayDark),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Expanded(
                              child: _childPlusIconWidget(parentIndex,
                                  childIndex, modifierCategory, modifierItem),
                              flex: 1,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ))
          ]),
    );
  }

  _childMinusIconWidget(int parentIndex, int childIndex,
      ItemModifiers modifierCategory, ModifierItems modifierItem) {
    return GestureDetector(
        onTap: () {

          if (context.read<ItemModifierViewProvider>().checkForMinimum(
              modifierCategory.modifierId, modifierItem.modifierItemId)) {
            context.read<ItemModifierViewProvider>().updatePlusMinusSelection(
                parentIndex, childIndex, -1, modifierCategory, modifierItem);
          }
        },
        child: _minusIconWidget(modifierCategory, modifierItem));
  }

  _childPlusIconWidget(int parentIndex, int childIndex,
      ItemModifiers modifierCategory,ModifierItems modifierItem) {
    return GestureDetector(
        onTap: () {

          context.read<ItemModifierViewProvider>().updatePlusMinusSelection(
              parentIndex, childIndex, 1, modifierCategory, modifierItem);
        },
        child: _plusIconWidget(
            parentIndex, childIndex, modifierCategory, modifierItem));
  }

  _minusIconWidget(
      ItemModifiers itemModifier, ModifierItems modifierItem) {
    return Icon(Icons.remove_circle,
        size: isBigScreenResolution(context)? 28.0 : 26.0,
        color: context.read<ItemModifierViewProvider>().checkForMinimum(
            itemModifier.modifierId, modifierItem.modifierItemId)
            ? ColorConstant.colorBlueDark
            : ColorConstant.colorRedDark);
  }

  ///child item plus widget
  _plusIconWidget(int parentIndex, int childIndex,
      ItemModifiers modifierCategory, ModifierItems modifierItem) {
    return Icon(Icons.add_circle,
        size:isBigScreenResolution(context)? 28.0:26.0,
        color: context.read<ItemModifierViewProvider>().isQuantityLimitExceeded(
            modifierCategory.modifierId!, modifierItem.modifierItemId!)
            ? ColorConstant.colorBlueDark
            : ColorConstant.colorRedDark);
  }

  /// tags view
   Widget _tagsView() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: _equalSizeSpacing12,),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
           children: [
              ...context.watch<ItemModifierViewProvider>().itemTags.map((e) =>
                Align(
                  widthFactor: 0.7,
                  child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: null,
                        color: Colors.white,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(30.0),
                          child: Image.network(
                            e.image ??'',
                            fit: BoxFit.cover,
                            width: 27.2,
                            height: 27.2,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                width: 1.6,
                                color: ColorConstant.colorRedDark,
                                style: BorderStyle.solid,
                              ),
                            ),
                            height: 27.2,
                            width: isBigScreenResolution(context) ? 30.0 : 28.0,
                          );
                        },
                      )),
                ),
              ),
              ),
           ],
        ),
      ),
    );
   }
  /// item description view
   Widget _descriptionView() {
     return Text(
       context.read<ItemModifierViewProvider>().itemData!.shortDescription ?? '',
       style: AppTextStyle.smallTextStyle.copyWith(
         color: ColorConstant.colorBlueDark,
         fontSize: isBigScreenResolution(context) ? 16 : 14,
       ),
     );
   }

   /// plus minus qty view
  Widget _plusMinusQtyView() {
    return Container(
      margin: EdgeInsets.symmetric(vertical:isBigScreenResolution(context) ?12 : 10,),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _addRemoveItemCountView(
            onPress: (){
              context.read<ItemModifierViewProvider>().decreaseQty();
            },
            widget: _minusIconItemWidget(),
          ),
          Text(
            '${context.watch<ItemModifierViewProvider>().itemCount}',
            style: AppTextStyle.mediumTextStyle.copyWith(
                fontSize: isBigScreenResolution(context) ? 18: 16,
                fontWeight: FontWeight.w500, color: ColorConstant.colorBlueDark),
          ),
          _addRemoveItemCountView(
            onPress: (){
              context.read<ItemModifierViewProvider>().increaseQty();
            },
            widget: _bottomPlusIconWidget(),
          ),
        ],
      ),
    );
  }

  Widget _addRemoveItemCountView(
      {required void Function() onPress, required Widget widget}) {
    return GestureDetector(
      onTap: onPress,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isBigScreenResolution(context) ? 16.0 : 14.0,
          vertical: isBigScreenResolution(context) ? 10.0 : 8.0,
        ),
        child: widget,
      ),
    );
  }

  _minusIconItemWidget() {
    return Icon(Icons.remove_circle,
        size:isBigScreenResolution(context)? 24.0:22.0, color: ColorConstant.colorBlueDark);
  }

  ///this is for screen bottom main item count
  _bottomPlusIconWidget() {
    return Icon(Icons.add_circle,
        size: isBigScreenResolution(context)?24.0:22.0, color: ColorConstant.colorBlueDark);
  }

  /// course dropdown
  Widget _courseDropDown(){
    return Container(
      margin: EdgeInsets.only(top: _equalSizeSpacing12,),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  ItemModifierViewString.selectACourse,
                  style: AppTextStyle.mediumTextStyle.copyWith(
                    color: ColorConstant.colorBlueDark,
                    fontWeight: FontWeight.w500,
                    fontSize: isBigScreenResolution(context) ? 16 : 14,
                  ),
                ),
              ),
            ],
          ),
          DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              focusColor: Colors.white,
              value: context.watch<ItemModifierViewProvider>().selectedCourseId,
              isDense: true,
              isExpanded: true,
              style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context) ? 14: 12, color: ColorConstant.colorBlueDark),
              iconEnabledColor: ColorConstant.colorBlueDark,
              items: context
                  .read<ItemModifierViewProvider>()
                  .itemCourseList.map((e) {
                return DropdownMenuItem(
                  value: e.id,
                  child: Text(
                    e.name!,
                    style: AppTextStyle.smallTextStyle.copyWith(
                        fontSize: isBigScreenResolution(context) ? 14: 12,
                        color: ColorConstant.colorBlueDark),
                  ),
                );
              }).toList(),
              onChanged: (int? value) {
                context.read<ItemModifierViewProvider>().setCourseId(value!);
              },
              autofocus: false,
            ),
          ),
        ],
      ),
    );
  }

  /// notes view
  Widget _notesView() {
    return Container(
      margin: EdgeInsets.only(top: _equalSizeSpacing12,),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: isBigScreenResolution(context) ? 16.0 : 14.0,),
            child: Text(
              ItemModifierViewString.notes,
              style: AppTextStyle.mediumTextStyle.copyWith(
                  fontWeight: FontWeight.w500,
                  color: ColorConstant.colorBlueDark,
                fontSize: isBigScreenResolution(context) ? 16 : 14,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom:  isBigScreenResolution(context) ? 15.0: 14),
            child: TextField(
              maxLines: null,
              autofocus: false,
              maxLength: 200,
              controller: _notesController,
              textInputAction: TextInputAction.done,
              cursorColor: ColorConstant.colorBlueDark,
              style: AppTextStyle.smallTextStyle.copyWith(
                fontWeight: FontWeight.w500,
                color: ColorConstant.colorBlueDark,
                fontSize: isBigScreenResolution(context) ? 14 : 12,
              ),
              decoration: const InputDecoration(
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: ColorConstant.colorBlueLight_50),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: ColorConstant.colorBlueLight_50),
                ),
                border: UnderlineInputBorder(
                  borderSide: BorderSide(color: ColorConstant.colorBlueLight_50),
                ),
              ),
              onChanged: (value){
                context.read<ItemModifierViewProvider>().setNotes(value);
              },
            ),
          ),
        ],
      ),
    );
  }

   double get _equalSizeSpacing12 => isBigScreenResolution(context) ?12 : 10;

  /// create cart - item
  Future<void> _createCartItem() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();


    if (hasConnected) {
      if(!mounted) return;
      showLoaderDialog(context);
      try {

         var cartProvider = context.read<CartViewProvider>();
         CartData? cartData = cartProvider.cartData;
        if(cartData != null) {
           if(cartData.id != 0){

             await context.read<ItemModifierViewProvider>().createCartItem(cartData.id!);


               cartProvider.selectCartUser(0);
               cartProvider.getCart(id: cartData.id!);

             if(!mounted) return;
             var data = context.read<DineInTakeoutProvider>();

              if(cartData.orderReceiveMethod!.toLowerCase() == OrderReceiveMethod.pickup ||
                  cartData.orderReceiveMethod!.toLowerCase() == OrderReceiveMethod.delivery
              ) {

                await data.resetCurrentPage();
                await data.getCartList();
              }

             if(cartData.orderReceiveMethod!.toLowerCase() == OrderReceiveMethod.dineIn){
               await data.getFloorWithTables();
             }

           }
        }

         if (!mounted) return;
         hideLoaderDialog(context);

         if(Navigator.canPop(context)) {
           Navigator.of(context).pop(true);
         }

      } on UnauthorisedException catch (error) {
        if (!mounted) return;
        hideLoaderDialog(context);
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        showErrorDialog(error: error.toString());
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      showInterNetConnectionDialog(context);
    }
  }
}
