import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/app_text_style.dart';
import '../../custom_widgets/appbar_widget.dart';
import '../../helpers/check_internet_connection.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/menu/menu_data.dart';
import '../../models/menu/tag_response.dart';
import '../../my_app.dart';
import '../../providers/menuview/menu_view_provider.dart';
import '../../utils/app_routes.dart';
import '../../utils/app_utils.dart';

class MenuView extends StatefulWidget {

   MenuView({super.key,});

  @override
  State<MenuView> createState() => _MenuViewState();
}

class _MenuViewState extends State<MenuView> with
    TickerProviderStateMixin {

  List<Tab> _tabs = [];
  TabController? _tabController;

  @override
  void initState() {
     WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        context.read<MenuViewProvider>().fetchTags();
        _fetchMenus();
     });

    _listenStreamForMenus();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
          appBar: CustomAppBar(
            context: context,
            appBarTitleText: NavigationString.menuView,
          ),
          body: Padding(
            padding: EdgeInsets.only(
              left: isBigScreenResolution(context) ? 8 : 6,
              right: isBigScreenResolution(context) ? 8 : 6,
              top: isBigScreenResolution(context) ? 8 : 6,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// tags view
                Consumer<MenuViewProvider>(
                  builder: (context, data, child) {
                    if (data.tags.isNotEmpty) {
                      return _tagListView(data.tags);
                    } else {
                      return Container();
                    }
                  },
                 ),

                  if(context.watch<MenuViewProvider>().categories.isNotEmpty)...[
                    Flexible(child: _categoriesView()),
                  ],
                  if(context.watch<MenuViewProvider>().categories.isEmpty)...[
                    _noDataFoundView(MenuViewString.noMenusYet,),
                  ],
              ],
            ),
          ),
        ),
    );
  }

  /// tag list view
  Widget _tagListView(List<TagData> tags) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8,),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ...tags.map((e) => _tagListItemView(e),),
          ],
        ),
      ),
    );
  }

  /// tag list item view
  Widget _tagListItemView(TagData tagData) {
    return GestureDetector(
      onTap: (){
        context.read<MenuViewProvider>().markTagSelected(tagData);
        if( context.read<MenuViewProvider>().selectedTagIds.isEmpty) {
          _fetchMenus();
        } else {
          _searchItems();
        }
      },
      child: Container(
        constraints: BoxConstraints(
          minWidth: isBigScreenResolution(context) ? 75 : 65,
          maxWidth: isBigScreenResolution(context) ? 75 : 65,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: ColorConstant.colorBlueDark,),
          borderRadius: BorderRadius.circular(isBigScreenResolution(context) ? 6 : 4,),
          color : tagData.selected! ? ColorConstant.colorBlueDark : ColorConstant.colorThemeWhite,
        ),
        padding: EdgeInsets.all(isBigScreenResolution(context) ? 6 : 5,),
        margin: EdgeInsets.only(
          right: isBigScreenResolution(context) ? 11 : 10,),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ClipOval(
              child: SizedBox.fromSize(
                size: Size.fromRadius(isBigScreenResolution(context) ? 10 : 9),
                child: tagData.image!.isNotEmpty ?
                Image.network(
                  tagData.image!,
                  fit: BoxFit.cover,
                  color: Colors.transparent,
                  colorBlendMode: BlendMode.srcOver,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: isBigScreenResolution(context) ? 18.0: 16.0,
                      width: isBigScreenResolution(context) ? 18.0: 16.0,
                      color: ColorConstant.colorBlueLight_8,
                    );
                  },
                )
                    :Container(
                  height: isBigScreenResolution(context) ? 18.0: 16.0,
                  width: isBigScreenResolution(context) ? 18.0: 16.0,
                  color: ColorConstant.colorBlueLight_8,
                ) ,
              ),
            ),
            SizedBox(width: isBigScreenResolution(context) ? 6: 4,),
            Expanded(
              child: AutoSizeText(
                tagData.name!,
                textAlign: TextAlign.start,
                minFontSize: isBigScreenResolution(context) ? 9 : 8,
                maxFontSize: isBigScreenResolution(context) ? 10 : 9,
                maxLines: 1,
                style: TextStyle(
                  fontFamily: AppFonts.roboto,
                  color: tagData.selected! ? ColorConstant.colorThemeWhite :ColorConstant.colorBlueDark,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// categories view
  Widget _categoriesView() {
     if(_tabController != null) {
       return Column(
         mainAxisSize: MainAxisSize.min,
         mainAxisAlignment: MainAxisAlignment.start,
         crossAxisAlignment: CrossAxisAlignment.start,
         children: [
           TabBar(
               controller: _tabController,
               isScrollable: true,
               tabAlignment: TabAlignment.center,
               // physics: const NeverScrollableScrollPhysics(),
               dividerColor: Colors.transparent,
               indicatorColor: ColorConstant.colorBlueDark,
               indicatorSize: TabBarIndicatorSize.label,
               tabs: _tabs,
               onTap: (int index) async {
               debugPrint('category tab tapped :::: $index');
               var data = context.read<MenuViewProvider>();
               data.setItems(data.categories[index].items!);
             },
           ),
           _equalSizeSpacing15,
           Expanded(
             child: TabBarView(
               controller: _tabController,
               physics: const NeverScrollableScrollPhysics(),
               children: _tabBarView(),
             ),
           ),
         ],
       );
     }
    return Container();
  }

  /// listen stream for menus
  void _listenStreamForMenus(){
    context.read<MenuViewProvider>().menuStream.listen((event) async{
      debugPrint("Stream menuStream Listen");
      if (event.isNotEmpty) {
        if (mounted) {
          debugPrint("Menu stream listener menu ");
          if (event.isNotEmpty) {
            _tabs = await getTabs(event.length, event);
            debugPrint(' Tabs count ${_tabs.length}');
            _tabController = getTabController();
          }
        }
      } else {
        debugPrint('Categories is empty');
      }
    });
  }

  TabController getTabController() {
    return TabController(length: _tabs.length, vsync: this);
  }

  // get Tabs
  Future<List<Tab>> getTabs(int tabLength,
      List<MenuCategories> category) async {
    _tabs.clear();
    category.forEach((cat) {
      _tabs.add(Tab(text: cat.categoryName,),);
    });
    return _tabs;
  }

  /// tab bar widgets
  List<Widget> _tabBarView() {
    List<Widget> tabBars =[];
    _tabs.forEach((tab) {
      tabBars.add(_menuItemView(),);
    });
    return tabBars;
  }

  /// menu item view
  Widget _menuItemView(){
     return Consumer<MenuViewProvider>(
         builder: (_, data, child) {

           if(data.items.isNotEmpty){
             return  RefreshIndicator(
               onRefresh: () async{
                 _fetchMenus();
               },
               child: GridView.builder(
                   physics: const AlwaysScrollableScrollPhysics(),
                   shrinkWrap: false,
                   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                     crossAxisCount: 3,
                     childAspectRatio: 0.85,
                     crossAxisSpacing: isBigScreenResolution(context) ? 9.0 : 7.0,
                     mainAxisSpacing: isBigScreenResolution(context) ? 9.0 : 7.0,
                   ),/*SliverGridDelegateWithMaxCrossAxisExtent(
                     maxCrossAxisExtent: 160.0,
                     crossAxisSpacing: isBigScreenResolution(context) ? 7.0 : 6.0,
                     mainAxisSpacing: isBigScreenResolution(context) ? 9.0 : 7.0,
                     mainAxisExtent: 120.0,
                   ),*/
                   itemCount: data.items.length,
                   itemBuilder: (_, index) {
                     return GestureDetector(
                       behavior: HitTestBehavior.opaque,
                       onTap: data.checkItemInStock(data.items[index]) ? () {
                           navigateToItemModifierScreen(context,Items.deepCopy(data.items[index]),
                             context.read<MenuViewProvider>().getCategoryName(data.items[index].categoryId!));
                       } : null,
                       child: Container(
                         decoration: BoxDecoration(
                           color: ColorConstant.colorThemeGray,
                           borderRadius: BorderRadius.circular(4.0),
                           border: const Border(
                             top: BorderSide(color: ColorConstant.colorThemeGray,
                               width: 0.5,),
                             left: BorderSide(color: ColorConstant.colorThemeGray,
                               width: 0.5,),
                             right: BorderSide(color: ColorConstant.colorThemeGray,
                               width: 0.5,),
                           ),
                         ),
                         child: ClipRRect(
                           borderRadius: BorderRadius.circular(4.0),
                           child: Column(
                             mainAxisAlignment: MainAxisAlignment.start,
                             crossAxisAlignment: CrossAxisAlignment.start,
                             mainAxisSize: MainAxisSize.min,
                             children: [
                               Expanded(child: _itemImageView(data.items[index])),
                               _addToStashButtonView(data.items[index].itemPriceInventory?.price ?? 0,
                                 inStock: data.checkItemInStock(data.items[index]),),
                             ],
                           ),
                         ),
                       ),
                     );
                   }),
             );
           }

           if(data.items.isEmpty) {
             return _noDataFoundView(MenuViewString.noItemsYet,);
           }

           return Container();
         },);
  }

  /// item image view
  Widget _itemImageView(Items item) {
    return Stack(
      fit: StackFit.expand,
      children: [
        if (item.imageUrl!.isNotEmpty) ...[
          Image.network(
            item.imageUrl!,
            fit: BoxFit.cover,
            color: Colors.transparent,
            colorBlendMode: BlendMode.srcOver,
            loadingBuilder: (BuildContext context, Widget child,
                ImageChunkEvent? loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: ColorConstant.colorBlueLight_8,
              );
            },
          ),
        ],
        if(item.imageUrl!.isEmpty)...[
          Image.asset(AssetImages.noImage,)
        ],
        /// when item is out of stock
        if(!context.read<MenuViewProvider>().checkItemInStock(item))...[
          Container(
            color: ColorConstant.colorThemeGray.withOpacity(0.5,),
          ),
          Align(
            alignment: Alignment.center,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5.0,vertical: 7.0,),
              decoration: BoxDecoration(
                color: ColorConstant.colorThemeGray.withOpacity(0.8,),
                borderRadius:const BorderRadius.all(Radius.circular(2.0)),
              ),
              child: Text(MenuViewString.outOfStock.toUpperCase(),
                style: AppTextStyle.smallTextStyle.copyWith(
                  fontSize: isBigScreenResolution(context)? 12: 10,
                  color: ColorConstant.colorGrayDark,),
              ),
            ),
          ),
        ],
        Positioned.fill(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              color: ColorConstant.colorThemeWhite.withOpacity(0.8),
              padding: const EdgeInsets.all(5.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _itemNameView(item),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// item name view
  Widget _itemNameView(Items item) {
    return Expanded(
      child: Text(
        item.name??'',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: AppTextStyle.smallTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 10: 8,
            color: ColorConstant.colorBlueDark),
      ),
    );
  }

  /// add to stash button view
  Widget _addToStashButtonView(num price, {bool inStock = true}) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: inStock ? ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_16,
        backgroundBlendMode: BlendMode.srcOver,
        border: Border.all(
          color: Colors.transparent,
          width: 0.0,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.zero,
          bottomRight: Radius.circular(4.0),
          topRight: Radius.zero,
          bottomLeft: Radius.circular(4.0),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: Text(
          NumberFormat.simpleCurrency().format(price),
          textAlign: TextAlign.center,
          style: AppTextStyle.largeTextStyle.copyWith(
            fontSize: isBigScreenResolution(context) ? 11.0 : 10.0,
            letterSpacing: 0.1,
            color: !inStock ? ColorConstant.colorBlueDark :
            ColorConstant.colorThemeWhite,
          ),
        ),
      ),
    );
  }

  /// fetch menus
  Future<void> _fetchMenus() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
       if(!mounted) return;
      showLoaderDialog(context);
      try {
        if(!mounted) return;
        await context.read<MenuViewProvider>().fetchMenus();

        if (!mounted) return;
        hideLoaderDialog(context);

      } on UnauthorisedException catch (error) {
        hideLoaderDialog(context);
        if (!mounted) return;
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      if (!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  /// search items
  Future<void> _searchItems() async {
    var hasConnected =
    await CheckInternetConnection.newInstance().checkConnection();

    if (hasConnected) {
      if(!mounted) return;
      showLoaderDialog(context);
      try {

        await context.read<MenuViewProvider>().searchItems();
        if(!mounted) return;
        hideLoaderDialog(context);

      } on UnauthorisedException catch (error) {
        hideLoaderDialog(context);
        if (!mounted) return;
        errorUnauthorisedAlertDialog(
            navigatorKey.currentState!.overlay!.context, error);
        return;
      } on FetchDataException catch (error) {
        debugPrint('Fetch data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } on BadRequestException catch (error) {
        debugPrint('Bad request data error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        return;
      } catch (error) {
        debugPrint('Getting error : ' + error.toString());
        if (!mounted) return;
        hideLoaderDialog(context);
        errorAlertDialog(error);
        return;
      }
    } else {
      if(!mounted) return;
      showInterNetConnectionDialog(context);
    }
  }

  ///  no data found viw
  Widget _noDataFoundView(String message){
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(message,
            textAlign: TextAlign.center,
            style: AppTextStyle.smallTextStyle.copyWith(
              color: ColorConstant.colorBlueLight_50,
              fontSize: isBigScreenResolution(context) ? 16 : 14.0,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  /// equal size spacing 15
  Widget get _equalSizeSpacing15 => SizedBox( height: isBigScreenResolution(context) ? 15 : 14);
}
