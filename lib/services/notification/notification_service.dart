import 'dart:convert';
import 'dart:ffi';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:gr8tables_server_manager/models/notification/all_notification_response.dart';
import 'package:gr8tables_server_manager/models/notification/device_register.dart';
import '../../constants/api_constant.dart';
import '../../constants/api_headers.dart';
import '../../helpers/flavor_config.dart';
import '../../helpers/http_response_helper.dart';
import 'package:http/http.dart';
import '../../utils/pref_utils.dart';

class NotificationService extends HttpResponseHelper {
  static final String _baseUrl = ApiConfig.authSERVERURL;
  final String _apiToken = ApiConfig.authSERVERTOKEN;
  final Client _http = Client();

  static const String _middleRoute = "/api/users/";
  static const String _registerDevice = "${_middleRoute}devices";
  static const String _unReadCount = "${_middleRoute}notifications/unread-count";
  static const String _removeDevice = "${_middleRoute}devices/token/";
  static const String _allNotification = "${_middleRoute}notifications/";
  static const String _readNotification = "${_middleRoute}notifications/";
  static const String _markAllRead= "${_middleRoute}notifications/mark-all-read/";

  Map<String, String> get _apiHeaders {
    return {
      ApiHeadersConstant.API_TOKEN: _apiToken,
      ApiHeadersConstant.CONTENT_TYPE: ApiHeadersConstant.CONTENT_TYPE_VALUE,
    };
  }

  Map<String, String> get _authApiHeaders {
    return {
      ..._apiHeaders,
      ApiHeadersConstant.AUTHORIZATION:
          ApiConstant.BEARER + PrefsUtils.getString(PrefKeys.authToken)!,
    };
  }

  /// register device with notification service
  Future<NotificationData> registerDevice({required String fcmToken}) async {
    try {
      var url = Uri.https(_baseUrl, _registerDevice);

      final response = await _http.post(
        url,
        headers: _authApiHeaders,
        body: jsonEncode(
          {
            ApiConstant.DEVICE_TYPE: Platform.isAndroid ? 'android' : Platform.isIOS ?'ios' :'n/a',
            ApiConstant.API_TOKEN: _apiToken,
            ApiConstant.DEVICE_TOKEN:fcmToken
          },
        ),
      );

      final result = parseHttpResponse(response);
      final deviceRegister = NotificationData.fromJson(result['payload']);
      return deviceRegister;
    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

  /// remove device token with notification service
  Future<void> removeDevice({required String fcmToken}) async {
    try {
      var url = Uri.https(_baseUrl, _removeDevice+fcmToken);

      final response = await _http.delete(
        url,
        headers: _authApiHeaders,
      );

      final result = parseHttpResponse(response);

    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

  /// Get unread count notifications
  Future<NotificationData> unReadCount() async {
    try {
      var url = Uri.https(_baseUrl, _unReadCount);

      final response = await _http.get(
        url,
        headers: _authApiHeaders,
      );

      final result = parseHttpResponse(response);
      final deviceRegister = NotificationData.fromJson(result['payload']);
      return deviceRegister;
    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

  /// List user's all notifications
  Future<UserNotificationResponse> listOfAllNotification({String sortByCreatedAt = "",required String page,
                            required String isRead}) async {
    try {

      Map<String, dynamic> _orderQueryParam = {
        if(sortByCreatedAt.isNotEmpty) ApiConstant.SORT_BY_CREATED_AT : sortByCreatedAt,
        ApiConstant.PAGE: page,
        ApiConstant.PER_PAGE: ApiConstant.PER_PAGE_VALUE,
        ApiConstant.IS_READ: isRead,
      };
      var url = Uri.https(_baseUrl, _allNotification,_orderQueryParam);

      final response = await _http.get(
        url,
        headers: _authApiHeaders,
      );

      final result = parseHttpResponse(response);

       return UserNotificationResponse.fromJson(result['payload']);

    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

  /// Mark read  user's notifications
  Future<UserNotificationResponse> markAsReadUnreadNotification({required String notificationId, required bool isRead}) async {
    try {

      var url = Uri.https(_baseUrl, _readNotification+notificationId);

      final response = await _http.patch(
        url,
        headers: _authApiHeaders,
        body: jsonEncode(
          {
            ApiConstant.IS_READ: isRead,
          },
        ),
      );

      final result = parseHttpResponse(response);

      return UserNotificationResponse.fromJson(result['payload']);

    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

  /// Mark read  user's notifications
  Future<UserNotificationResponse> markAllReadNotification() async {
    try {

      var url = Uri.https(_baseUrl, _markAllRead);

      final response = await _http.patch(
        url,
        headers: _authApiHeaders,
      );

      final result = parseHttpResponse(response);

      return UserNotificationResponse.fromJson(result['payload']);

    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

}
