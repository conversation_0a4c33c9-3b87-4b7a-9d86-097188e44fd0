import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart';

import '../../constants/api_constant.dart';
import '../../constants/api_headers.dart';
import '../../helpers/flavor_config.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/auth/user_model.dart';
import '../../models/auth/user_register_model.dart';
import '../../models/business/user_business_model.dart';
import '../../models/location/location_model.dart';
import '../../utils/pref_utils.dart';

class AuthService extends HttpResponseHelper {
  static final String _baseUrl = ApiConfig.authSERVERURL;
  final String _apiToken = ApiConfig.authSERVERTOKEN;
  final Client _http = Client();

  static const String _middleRoute = "/api/";
  static const String _loginWithEmail = "${_middleRoute}auth/login";
  static const String _forgotPassword = "${_middleRoute}auth/forget-password";
  static const String _getProfile = "${_middleRoute}users/me";
  static const String _refreshToken = "${_middleRoute}auth/refresh-token";
  static const String _getBusinesses = "${_middleRoute}businesses/all";
  static const String _getBusinessesLocations = "${_middleRoute}locations/all";

  Map<String, String> get _apiHeaders {
    return {
      ApiHeadersConstant.API_TOKEN: _apiToken,
      ApiHeadersConstant.CONTENT_TYPE: ApiHeadersConstant.CONTENT_TYPE_VALUE,
    };
  }

  Map<String, String> get _authApiHeaders {
    return {
      ..._apiHeaders,
      ApiHeadersConstant.AUTHORIZATION: ApiConstant.BEARER + PrefsUtils.getString(PrefKeys.authToken)!,
    };
  }

  Map<String, String> get _authContentTypeApiHeaders {
    return {
      ApiHeadersConstant.CONTENT_TYPE: ApiHeadersConstant.CONTENT_TYPE_VALUE,
      ApiHeadersConstant.AUTHORIZATION: ApiConstant.BEARER + PrefsUtils.getString(PrefKeys.authToken)!,
    };
  }

  /// login with email
  Future<UserModel> loginWithEmail(String email, String password) async {
    try {

      var url = Uri.https(_baseUrl, _loginWithEmail);

      final response = await _http.post(url,
          headers: _apiHeaders, body: jsonEncode({
            ApiConstant.USERNAME: email,
            ApiConstant.PASSWORD: password,
          }),);

      final result = parseHttpResponse(response);

      final user = UserModel.fromJson(result['payload']);

      return user;
    } on SocketException catch (error) {
      debugPrint(error.toString());
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw error;
      }
    }
  }

  /// get profile api
  Future<UserData> getProfileResponse() async {
    try {
      Map<String, String> profileMeQueryParam  = {
        ApiHeadersConstant.INCLUDE_PERMISSIONS: ApiConstant.INCLUDE_VALUE,
      };

      var url = Uri.https(_baseUrl, _getProfile,profileMeQueryParam);
      final response = await _http.get(url, headers: _authApiHeaders);

      final result = parseHttpResponse(response);
      final userData = UserData.fromJson(result['payload']);
      return userData;
    } on SocketException catch (err) {
      debugPrint(err.toString());
      throw InternetException();
    } catch (err) {
      if (err.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw err;
      }
    }
  }


  /// forgot password api
  Future<void> forgotPassword(String email) async {
    try {
      var url = Uri.https(_baseUrl, _forgotPassword);

      final response = await _http.post(
          url,
          headers: _apiHeaders,
          body: jsonEncode({
            ApiConstant.EMAIL: email,
          }),);

      parseHttpResponse(response);

      debugPrint('forgot_password:: ${response.body}');
    } on SocketException catch (err) {
      debugPrint(err.toString());
      throw InternetException();
    } catch (err) {
      if (err.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw err;
      }
    }
  }

  /// refresh Token api
  Future<RefreshTokenModel> refreshToken(
      String businessId, String locationId) async {
    var headers = _authApiHeaders;
    if (businessId != '') {
      headers[ApiConstant.BUSINESS_ID] = businessId;
    }

    if (locationId != '') {
      headers[ApiConstant.LOCATION_ID] = locationId;
    }

    try {
      var url = Uri.https(_baseUrl, _refreshToken);
      final response = await _http.get(
          url,
          headers: headers
      );

      final result = parseHttpResponse(response);

      final refreshTokenModel = RefreshTokenModel.fromJson(result['payload']);

      return refreshTokenModel;
    } on SocketException catch (err) {
      debugPrint(err.toString());
      throw InternetException();
    } catch (err) {
      if (err.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw err;
      }
    }
  }

  /// user business list api
  Future<List<UserBusinessModel>> getUserBusiness() async{
    try {
      Map<String, dynamic>  userBusinessQueryParam = {
          ApiHeadersConstant.INCLUDE_BRANDING: ApiConstant.INCLUDE_VALUE,
      };

      var url =Uri.https(_baseUrl, _getBusinesses, userBusinessQueryParam);
      final response = await _http.get(
        url,
        headers: _authContentTypeApiHeaders,
      );

      final result = parseHttpResponse(response);

      if (result['payload'] == null) {
        return [];
      }

      final List<UserBusinessModel> userBusinesses = [];

      result['payload'].forEach((business) {
        userBusinesses.add(UserBusinessModel.fromGetMyBusinesses(business));
      });

      return userBusinesses;
    } on SocketException catch (err) {
      debugPrint(err.toString());
      throw InternetException();
    } catch (err) {
      if (err.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw err;
      }
    }
  }

  ///Businesses Locations list api
  Future<List<LocationModel>> getBusinessLocations() async {
    try {
      var url = Uri.https(_baseUrl, _getBusinessesLocations);
      final response = await _http.get(
        url,
        headers: _authApiHeaders,
      );

      final result = parseHttpResponse(response);

      if (result['payload'] == null) {
        return [];
      }

      final List<LocationModel> businessLocations = [];

      result['payload'].forEach((business) {
        businessLocations.add(LocationModel.fromBusinessLocation(business));
      });

      return businessLocations;
    } on SocketException catch (err) {
      debugPrint(err.toString());
      throw InternetException();
    } catch (err) {
      if (err.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      } else {
        throw err;
      }
    }
  }

}