import 'dart:io';

import 'package:http/http.dart';

import '../../constants/api_constant.dart';
import '../../helpers/flavor_config.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/business/user_business_model.dart';
import '../../models/location/location_model.dart';
import '../../models/location/location_user_model.dart';
import '../../utils/pref_utils.dart';

class LocationService extends HttpResponseHelper {

  final String _baseUrl = ApiConfig.authSERVERURL;
  final String _apiToken = ApiConfig.authSERVERTOKEN;
  final Client _http = Client();

  static final String _middleRoute = "/api/";
  static final String _getBusinessesLocations = _middleRoute + "locations/all";
  static final String _getUsersBasedOnLocation = _middleRoute + "web/users";
  static final String _getEmployees = _middleRoute + "locations/users/all";
  static final String _getLocationDetail = _middleRoute + "web/locations/:locationId/show";


  Map<String, dynamic> get _locationUsersQueryParam {
    return {
      'businessId': UserBusinessModel.fromJson(PrefsUtils.getObject(PrefKeys.business)).id ?? '',
      'locationId': LocationModel.fromJson(PrefsUtils.getObject(PrefKeys.location)).id ?? '',
    };
  }

  //Businesses Locations
  Future<List<LocationModel>> getBusinessLocations() async {
    try {
      Map<String, dynamic>  _locationUsersOptionalQueryParam =  {
          'includeCannabisSettings': '1',
      };

      var url = Uri.https(_baseUrl, _getBusinessesLocations,_locationUsersOptionalQueryParam);
      final response = await _http.get(
        url,
      );

      final result = parseHttpResponse(response);

      if (result['payload'] == null) {
        return [];
      }

      final List<LocationModel> businessLocations = [];

      result['payload'].forEach((business) {
        businessLocations.add(LocationModel.fromBusinessLocation(business));
      });


      return businessLocations;
    } on SocketException catch (err) {
      print(err);
      throw InternetException();
    } catch (err) {
      if (err.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw err;
    }
  }

  // Location Base Users
  Future<List<LocationUserModel>> getLocationBaseUsers() async {
    try {
      var url = Uri.https(_baseUrl, _getUsersBasedOnLocation,_locationUsersQueryParam);
      final response = await _http.get(
        url,
      );

      final result = parseHttpResponse(response);

      if (result['payload'] == null) {
        return [];
      }

      final List<LocationUserModel> userLocations = [];

      result['payload'].forEach((location) {
        userLocations.add(LocationUserModel.fromLocationUser(location));
      });

      return userLocations;
    } on SocketException catch (err) {
      print(err);
      throw InternetException();
    }  catch (err) {
      if (err.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw err;
    }
  }

  // employee list
  Future<List<LocationUserModel>> getEmployees() async {
    try {
      var url = Uri.https(_baseUrl, _getEmployees, {
         ApiConstant.PERMISSIONS : ApiConstant.PERMISSIONS_VALUE.join(",")
      });
      final response = await _http.get(
        url,
      );

      final result = parseHttpResponse(response);

      if (result['payload'] == null) {
        return [];
      }

      final List<LocationUserModel> userLocations = [];

      result['payload'].forEach((location) {
        userLocations.add(LocationUserModel.fromLocationUser(location));
      });

      return userLocations;
    } on SocketException catch (err) {
      print(err);
      throw InternetException();
    }  catch (err) {
      if (err.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw err;
    }
  }

}
