import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';

import '../../constants/api_constant.dart';
import '../../constants/app_string.dart';
import '../../helpers/flavor_config.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/auth/user_model.dart';
import '../../models/business/user_business_model.dart';
import '../../models/cart/cart_custom_discount_response.dart';
import '../../models/cart/cart_items/delete_cart_item_response.dart';
import '../../models/cart/cart_promotions/remove_promotions.dart';
import '../../models/cart/cart_response.dart';
import '../../models/cart/cart_user.dart';
import '../../models/cart/cart_users/add_cart_user_response.dart';
import '../../models/cart/create_cart_item_response.dart';
import '../../models/cart/create_cart_response.dart';
import '../../models/cart/get_cart_data_response.dart';
import '../../models/dinein_takeout/floors_response.dart';
import '../../models/discount/cart_discount_response.dart';
import '../../models/location/location_model.dart';
import '../../models/loyalty/remove_loyalty_response.dart';
import '../../models/orders/cart_promotions/cart_promocode_response.dart';
import '../../models/orders/order_receipt/order_receipt_response.dart';
import '../../models/payment/cart_transaction_response.dart';
import '../../models/paymentterminal/payment_terminals.dart';
import '../../utils/pref_utils.dart';
import '../interceptor/auth_interceptor.dart';

class CartService extends HttpResponseHelper {

  final AuthInterceptorClient _http = AuthInterceptorClient();

  static final String _baseUrl = ApiConfig.cartSERVERURL;

  static const String _middleRoute = "/api/";

  static const String _floors = "${_middleRoute}floors";
  static const String _getCartsByFilter = "${_middleRoute}carts";
  static const String _getCart = "${_middleRoute}carts/getCart";
  static const String _cartUser = "${_middleRoute}cart-users";
  static const String _createCartItem = "${_middleRoute}cart-items";
  static const String _createCart = "${_middleRoute}carts";
  static const String _itemsSendToKitchen = _middleRoute +"carts/:cartId/sendToKitchen";
  static const String _deleteCartItem = _middleRoute +"cart-items/:cartItemId";
  static const String _voidCartItem = _middleRoute +"cart-items/void";
  static const String _emailReceipt = _middleRoute +"carts/:cartId/sendmail";
  static const String _cartPayment = _middleRoute + "cart-payment";
  static const String _updateCartStatus = _middleRoute + "carts/:cartId/updateStatus";
  static const String _cartServiceCharge = _middleRoute +"cartServiceCharge";
  static const String _cartTip = _middleRoute +"cartTip";
  static const String _applyCartPromoCode = _middleRoute + "cart-promocode";
  static const String _removeCustomDiscount = _middleRoute +"cart-custom-discount";
  static final String _removeLoyalty = _middleRoute + "cart-loyalty/";
  static const String _applyCartDiscount = _middleRoute + "cart-discount";
  static final String _applyCustomDiscount = _middleRoute +"cart-custom-discount-item/multiple";
  static final String _approveCustomDiscount = _middleRoute +"carts/:cartId/approve";


  ///Get floor with tables
  Future<List<FloorData>> getFloors() async {
    try {
      var url = Uri.https(_baseUrl, _floors , {
        ApiConstant.INCLUDE_TABLES : ApiConstant.STATUS_TRUE,
        ApiConstant.INCLUDE_CART : ApiConstant.STATUS_TRUE
      });
      final response = await _http.get(url,);
      final result = parseHttpResponse(response);

      FloorsResponse floorsResponse = FloorsResponse.fromJson(result);

      return floorsResponse.payload ?? [];
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error,stacktrace) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Get  order list
  Future<CartPayloadData?> getOrders(
      {String search = '',
        String page = '1'}) async {
    try {
      Map<String, dynamic> queryParams = {
        if (search.isNotEmpty) ApiConstant.SEARCH: search,
        ApiConstant.ORDER_RECEIVE_METHOD : [OrderReceiveMethod.pickup,OrderReceiveMethod.delivery].join(','),
        ApiConstant.STATUSES : [OrderStatus.pending,OrderStatus.placed, OrderStatus.ready,
          OrderStatus.outForDelivery].join(','),
        ApiConstant.PAGE: page,
        ApiConstant.PER_PAGE: ApiConstant.PER_PAGE_VALUE,
      };


      var url = Uri.https(_baseUrl, _getCartsByFilter, queryParams);

      final response = await _http.get(url);

      final result = parseHttpResponse(response);

      CartResponse cartResponse = CartResponse.fromJson(result);

      if (cartResponse.payload == null) {
        return null;
      }

      return cartResponse.payload;
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// get cart
  Future<GetCartDataResponse> getCart({required String id}) async {
    try {

      Map<String, dynamic> queryParams = {
        ApiConstant.ID: id,
      };

      var url = Uri.https(
          _baseUrl, _getCart, queryParams);

      final response = await _http.get(url,);
      final result = parseHttpResponse(response);

      GetCartDataResponse getCartDataResponse =
      GetCartDataResponse.fromJson(result);

      return getCartDataResponse;
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

 /// add user to cart
  Future<AddCartUserResponse> addUserToCart(int cartId,String guestName) async{
    try {

      var body = jsonEncode({
        ApiConstant.CART_ID :  cartId,
        ApiConstant.NAME : guestName.trim(),
      });
      debugPrint(':: add user to cart :: $body');

      var url = Uri.https(_baseUrl, _cartUser);

      final response = await _http.post(
        url, body: body,);

      final result = parseHttpResponse(response, body);

      AddCartUserResponse addCartUserResponse =
      AddCartUserResponse.fromJson(result);

      return addCartUserResponse;

    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Create cart item
  Future<CreateCartItemResponse> createCartItem({required String body}) async {
    try {

      var url = Uri.https(_baseUrl, _createCartItem);

      final response = await _http.post(
        url,
        body: body,
      );

      final result = parseHttpResponse(response, body);

      CreateCartItemResponse createCartItemResponse =
      CreateCartItemResponse.fromJson(result);

      return createCartItemResponse;
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Create order for a cart
  Future<CreateCartResponse> createCart(
      {String userId = '', /*Customer? user,*/ String orderReceiveMethod = OrderReceiveMethod.pickup, int tableId = -1,
        /*UserAddress? userAddress*/}) async {
    try {
      debugPrint("Create Order and user id is $userId");
     /* var _userObject = {
        if (userId.isNotEmpty) ApiConstant.ID: userId,
        if (user != null && user.firstName!.isNotEmpty)
          ApiConstant.FIRST_NAME: user.firstName,
        if (user != null && user.lastName!.isNotEmpty)
          ApiConstant.LAST_NAME: user.lastName,
        if (user != null && user.email!.isNotEmpty)
          ApiConstant.EMAIL: user.email,
        if (user != null && user.phone!.isNotEmpty)
          ApiConstant.PHONE: user.phone,
      };*/


      var params = {
        ApiConstant.LOCATION_ID: LocationModel.fromJson(PrefsUtils.getObject(PrefKeys.location)).id,
        ApiConstant.BUSINESS_ID : UserBusinessModel.fromJson(PrefsUtils.getObject(PrefKeys.business)).id!,
        //if(userId.isNotEmpty)ApiConstant.USER_ID: userId,
        //if(userId.isNotEmpty)ApiConstant.USER: _userObject,
        ApiConstant.ADDED_BY: ApiConstant.ADDED_BY_VALUE,
        ApiConstant.ADDED_BY_ID: UserModel.fromJson(PrefsUtils.getObject(PrefKeys.user)).id,
        ApiConstant.ORDER_RECEIVE_METHOD: orderReceiveMethod,
        ApiConstant.RECEIVE_LATER: false,
        ApiConstant.PAY_LATER: false,
        ApiConstant.PAYMENT_RECEIVED: false,
        ApiConstant.PLATFORM: ApiConstant.PLATFORM_VALUE_POS,
        ApiConstant.STATUS: ApiConstant.STATUS_PENDING,
        //if (_posStationId != 0) ApiConstant.POS_STATION_ID: _posStationId,
        //if (_shiftId != 0)ApiConstant.SHIFT_ID: _shiftId,
        if(tableId != -1) ApiConstant.TABLE_ID : tableId,
        //if(userAddress != null) ApiConstant.DELIVERY : userAddress,
      };

      var body = jsonEncode(params);

      debugPrint('CART BODY:: $body');

      var url = Uri.https(_baseUrl, _createCart);

      final response = await _http.post(url, body: body,);

      final result = parseHttpResponse(response, body);

      CreateCartResponse createCartResponse =
      CreateCartResponse.fromJson(result);

      return createCartResponse;
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// item send to kitchen
  Future<CreateCartResponse> itemSendToKitchen(
      {required List<int> cartItemIds, required int cartId,}) async {
    try {

      var params= {
        ApiConstant.CART_ITEM_IDS :cartItemIds,
      };

      String sendToKitchen = _itemsSendToKitchen.replaceAll(':cartId', cartId.toString(),);

      var url = Uri.https(_baseUrl, sendToKitchen);

      final response =
      await _http.patch(url, body: jsonEncode(params));
      final result = parseHttpResponse(response);

      CreateCartResponse _createCartResponse = CreateCartResponse.fromJson(result);

      return _createCartResponse;

    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Delete item from cart
  Future<DeleteCartItemResponse> deleteCartItem({required int cartItemId}) async {
    try {

      String deleteCartItem = _deleteCartItem.replaceAll(':cartItemId', cartItemId.toString(),);
      var url = Uri.https(_baseUrl, deleteCartItem,);

      final response = await _http.delete(url,);

      final result = parseHttpResponse(response);

      DeleteCartItemResponse deleteCartItemResponse =
      DeleteCartItemResponse.fromJson(result);

      return deleteCartItemResponse;
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// void cart item
  Future<void> voidCartItem(
      {required List<int> cartItemIds, String? reason}) async {
    try {

      var body= {
        ApiConstant.CART_ITEM_IDS :cartItemIds,
        ApiConstant.REASON :reason,
      };


      var url = Uri.https(_baseUrl, _voidCartItem);

      final response =
      await _http.post(url,  body: jsonEncode(body));
      final result = parseHttpResponse(response);

      /// no need to parse in model as only success is return

    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Order payment
  Future<CartTransactionResponse> cartPayment(
      {required int cartId, required String paymentType, required num cartAmount, String cardType = 'other'}) async {
    try {
      String deviceId = '';
      if (paymentType == PaymentType.credit)
        deviceId = PaymentTerminal.fromJson(PrefsUtils.getObject(PrefKeys.paymentTerminal)).terminalId!;

      debugPrint("Payment Type $paymentType and terminal id is $deviceId");

      var params = {
        ApiConstant.CART_ID: cartId,
        ApiConstant.TOTAL: cartAmount,
        ApiConstant.PAYMENT_TYPE: paymentType == PaymentType.cash
            ? PaymentType.cash
            : paymentType == PaymentType.credit
            ? PaymentType.credit
            : PaymentType.nonIntegrated,
        if(paymentType == PaymentType.nonIntegrated) ApiConstant.CARD_TYPE: cardType,
        if (paymentType == PaymentType.credit)
          ApiConstant.DEVICE_ID: deviceId,
      };

      var body = jsonEncode(params);
      debugPrint(':: CartPayment :: ${body}');

      var url = Uri.https(_baseUrl, _cartPayment);

      final response = await _http.post(
        url,
        body: body,
      );

      final result = parseHttpResponse(response, body);

      CartTransactionResponse _cartTransactionResponse = CartTransactionResponse.fromJson(result);

      return _cartTransactionResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// Update cart status
  Future<CreateCartResponse> updateCartStatus({required String cartId, required String status}) async{
    try{

      String updateCartStatus = _updateCartStatus.replaceAll(':cartId', cartId);

      num _posStationId= 0;int _shiftId =0;
      String _posStationName='';
      // if(PosStation.fromJson(PrefsUtils.getObject(PrefKeys.posStation)).id != null) {
      //   _posStationId = PosStation.fromJson(PrefsUtils.getObject(PrefKeys.posStation)).id!;
      //   _posStationName = PosStation.fromJson(PrefsUtils.getObject(PrefKeys.posStation)).name ??'';
      // }
      // if(ShiftData.fromJson(PrefsUtils.getObject(PrefKeys.activeShift)).id != null) {
      //   _shiftId = ShiftData.fromJson(PrefsUtils.getObject(PrefKeys.activeShift)).id!;
      // }

      var params = {
        ApiConstant.STATUS : status,
        // if (_posStationId != 0) ApiConstant.POS_STATION_ID: _posStationId,
        // if (_posStationName.isNotEmpty)ApiConstant.POS_STATION_NAME : _posStationName,
        // if (_shiftId != 0)ApiConstant.SHIFT_ID: _shiftId,
      };

      var body = jsonEncode(params);
      debugPrint(':: update cart status :: ${body} :: cartId: $cartId');
      var url = Uri.https(_baseUrl, updateCartStatus);

      final response = await _http.patch(url, body: body);

      final result = parseHttpResponse(response);

      CreateCartResponse _createCartResponse = CreateCartResponse.fromJson(result);
      return _createCartResponse;

    }on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// add service charge
  Future<void> addServiceCharge(
      {required int cartId, required String type, required num value, }) async {
    try {

      debugPrint("Type $type and value  is $value");

      var params = {
        ApiConstant.CART_ID: cartId,
        ApiConstant.TYPE: type,
        ApiConstant.VALUE: value,
      };

      var body = jsonEncode(params);
      debugPrint(':: apply service charge :: ${body}');

      var url = Uri.https(_baseUrl, _cartServiceCharge);

      final response = await _http.post(
        url,
        body: body,
      );

      final result = parseHttpResponse(response, body);

      /// no need to return as on 200/201 will refresh page

    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// add tip for cart
  Future<void> addTip(
      {required int cartId, required String type, required num value, }) async {
    try {


      debugPrint("Type $type and value  is $value");

      var params = {
        ApiConstant.CART_ID: cartId,
        ApiConstant.TYPE: type,
        ApiConstant.VALUE: value,
      };

      var body = jsonEncode(params);
      debugPrint(':: apply tip  :: ${body}');

      var url = Uri.https(_baseUrl, _cartTip);

      final response = await _http.post(
        url,
        body: body,
      );

      final result = parseHttpResponse(response, body);

      /// no need to return as on 200/201 will refresh page

    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// remove service charge
  Future<void> removeServiceCharge(
      {required int serviceChargeId, }) async {
    try {

      debugPrint("Service charge id : $serviceChargeId");

      String removeServiceCharge ='${_cartServiceCharge}/$serviceChargeId';

      var url = Uri.https(_baseUrl, removeServiceCharge);

      final response = await _http.delete(
        url,
      );

      final result = parseHttpResponse(response,);
      /// no need to return as on 200/201 will refresh page

    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// remove tip
  Future<void> removeTip(
      {required int tipId, }) async {
    try {

      debugPrint("Tip id : $tipId");

      String removeServiceCharge ='$_cartTip/$tipId';

      var url = Uri.https(_baseUrl, removeServiceCharge);

      final response = await _http.delete(
        url,
      );

      final result = parseHttpResponse(response,);
      /// no need to return as on 200/201 will refresh page

    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///send email receipt
  Future<OrderReceiptResponse> sendEmailReceipt({ required String email,
    required String cartId }) async {

    print(email);
    try {

      String emailReceipt  = _emailReceipt.replaceAll(':cartId', cartId);
      var url = Uri.https(_baseUrl, emailReceipt);

      var body = jsonEncode({
        ApiConstant.EMAIL : email,
      });
      final response = await _http.post(
          url,
          body: body
      );

      final result = parseHttpResponse(response,body);

      OrderReceiptResponse _orderReceiptResponse = OrderReceiptResponse.fromJson(result);
      return _orderReceiptResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///apply promo code
  Future<CartPromoCodeResponse> applyPromoCode({required String promoCode,
    required int cartId, CartUser? user}) async {

    try {
      var url = Uri.https(_baseUrl, _applyCartPromoCode);


      var body= jsonEncode({
        ApiConstant.CART_ID: cartId,
        ApiConstant.USERTYPE : user == null ? ApiConstant.USERTYPE_GUEST : ApiConstant.USERTYPE_REGISTERED,
        ApiConstant.PROMO_CODE :promoCode.trim(),
        ApiConstant.PLATFORM: ApiConstant.PLATFORM_VALUE,

      });

      final response = await _http.post(
        url,
        body: body,
      );

      final result = parseHttpResponse(response);

      CartPromoCodeResponse _cartPromoCodeResponse = CartPromoCodeResponse.fromJson(result);

      return _cartPromoCodeResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///remove custom discount
  Future<RemovePromotionsResponse> removeCustomDiscount({required num customDiscountId }) async {

    try {

      var url = Uri.https(_baseUrl, '$_removeCustomDiscount/$customDiscountId',);

      final response = await _http.delete(
        url,
      );

      final result = parseHttpResponse(response);

      RemovePromotionsResponse _removePromotionsResponse = RemovePromotionsResponse.fromJson(result);
      return _removePromotionsResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Remove loyalty from order
  Future<RemoveLoyaltyResponse> removeLoyaltyFromOrder({required num loyaltyId}) async {
    try {

      Map<String, String> _removeLoyaltyParam  = {
        ApiConstant.PLATFORM: ApiConstant.PLATFORM_VALUE
      };

      var url = Uri.https(_baseUrl, _removeLoyalty + loyaltyId.toString(), _removeLoyaltyParam);

      final response = await _http.delete(
        url,
      );

      final result = parseHttpResponse(response);

      RemoveLoyaltyResponse _removeLoyaltyResponse = RemoveLoyaltyResponse.fromJson(result);

      return _removeLoyaltyResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }


  ///remove promo code
  Future<RemovePromotionsResponse> removePromoCode({required num id }) async {

    try {
      Map<String, String> _removePromoCodeParam  = {
        ApiConstant.PLATFORM: ApiConstant.PLATFORM_VALUE
      };

      var url = Uri.https(_baseUrl, '$_applyCartPromoCode/$id', _removePromoCodeParam);

      final response = await _http.delete(
        url,
      );

      final result = parseHttpResponse(response);

      RemovePromotionsResponse _removePromotionsResponse = RemovePromotionsResponse.fromJson(result);
      return _removePromotionsResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///apply discount
  Future<CartDiscountResponse> applyDiscount({required int cartId,
    required num discountId,CartUser? user}) async {
    try {
      var url = Uri.https(_baseUrl, _applyCartDiscount);

      var body= jsonEncode({
        ApiConstant.CART_ID: cartId,
        ApiConstant.DISCOUNT_ID :discountId,
        ApiConstant.BUSINESS_ID :LocationModel.fromJson(PrefsUtils.getObject(PrefKeys.business)).id,
        ApiConstant.USERTYPE : user == null ? ApiConstant.USERTYPE_GUEST : ApiConstant.USERTYPE_REGISTERED,
        ApiConstant.PLATFORM : ApiConstant.PLATFORM_VALUE,
      });

      final response = await _http.post(
        url,
        body: body,
      );

      final result = parseHttpResponse(response);

      CartDiscountResponse _cartDiscountResponse = CartDiscountResponse.fromJson(result);

      return _cartDiscountResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///remove discount
  Future<RemovePromotionsResponse> removeDiscount({required num id }) async {

    try {
      /*Map<String, String> _removeDiscountQueryParam  = {
        ApiConstant.PLATFORM: ApiConstant.PLATFORM_VALUE
      };
     */
      var url = Uri.https(_baseUrl, '$_applyCartDiscount/$id',);

      final response = await _http.delete(
        url,
      );

      final result = parseHttpResponse(response);

      RemovePromotionsResponse _removePromotionsResponse = RemovePromotionsResponse.fromJson(result);
      return _removePromotionsResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///apply custom discount
  Future<CartCustomDiscountResponse> applyCustomDiscount({required data}) async {

    try {

      var url = Uri.https(_baseUrl, _applyCustomDiscount,);

      final response = await _http.post(
        url,
        body: jsonEncode(data),
      );

      final result = parseHttpResponse(response);

      CartCustomDiscountResponse _cartCustomDiscountResponse = CartCustomDiscountResponse.fromJson(result);

      return _cartCustomDiscountResponse;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  ///Approve custom discount
  Future<CartCustomDiscountResponse> approveCustomDiscount({
    required int cartId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final url = Uri.https(
        _baseUrl,
        _approveCustomDiscount.replaceAll(':cartId', cartId.toString()),
      );

      final response = await _http.patch(
        url,
        body: jsonEncode(data),
      );

      final result = parseHttpResponse(response);
      return CartCustomDiscountResponse.fromJson(result);
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest")) {
        throw InternetException();
      }
      rethrow;
    }
  }


}