import 'dart:io';

import '../../constants/api_constant.dart';
import '../../constants/app_string.dart';
import '../../helpers/flavor_config.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/cart/cart_response.dart';
import '../interceptor/auth_interceptor.dart';

class HistoryService extends HttpResponseHelper {
  final AuthInterceptorClient _http = AuthInterceptorClient();

  static final String _baseUrl = ApiConfig.cartSERVERURL;


  static final String _middleRoute = "/api/";
  static String _getCartsOrderHistory = _middleRoute +"carts";


  ///Get cart order list for History page
  Future<CartPayloadData?> getHistoryCartOrder(
      {String search = '', String sort = '', String sortBy = '', String page = '1'}) async {
    try {
      Map<String, dynamic> _orderQueryParam = {
        if (search.isNotEmpty) ApiConstant.SEARCH: search,
        if(sort.isNotEmpty) ApiConstant.SORT : sort,
        if(sortBy.isNotEmpty) ApiConstant.SORT_BY : sortBy,
        ApiConstant.STATUSES : [OrderStatus.processed,OrderStatus.cancelled,].join(','),
        ApiConstant.PAGE: page,
        ApiConstant.PER_PAGE: ApiConstant.PER_PAGE_VALUE,
      };

      var url = Uri.https(_baseUrl, _getCartsOrderHistory, _orderQueryParam);

      final response = await _http.get(url);

      final result = parseHttpResponse(response);

      CartResponse cartResponse = CartResponse.fromJson(result);

      if (cartResponse.payload == null) {
        return null;
      }

      return cartResponse.payload;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }
}
