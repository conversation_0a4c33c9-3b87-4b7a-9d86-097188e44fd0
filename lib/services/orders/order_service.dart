import 'dart:io';
import 'package:flutter/widgets.dart';
import '../../constants/api_constant.dart';
import '../../helpers/flavor_config.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/customer/customer_order/customer_frequent_orders.dart';
import '../../models/customer/customer_order/customer_past_order.dart';
import '../../models/menu/menu_data.dart';
import '../graph_ql/graph_ql_service.dart';
import '../interceptor/auth_interceptor.dart';

class OrderService extends HttpResponseHelper {
  final AuthInterceptorClient _http = AuthInterceptorClient();

  static final String _baseUrl = ApiConfig.cartSERVERURL;

  static final String _middleRoute = "/api/";

  static final String _customerFrequentOrders =_middleRoute +"carts/user/:userId/frequent";
  static final String _customerPastOrders =_middleRoute +"carts/user/:userId";



  /// get frequent orders
  Future<CustomerFrequentOrders> getCustomerFrequentOrders({required String userId,}) async {
    try {

      String _frequentOrders = _customerFrequentOrders.replaceAll(':userId',userId );
      var url = Uri.https(_baseUrl, _frequentOrders, {
        ApiConstant.LIMIT : ApiConstant.LIMIT_VALUE,
      });
      final response = await _http.get(url,);

      final result = parseHttpResponse(response);
      debugPrint('customerFrequentOrders:::=> ${result}');

      CustomerFrequentOrders _frequentOrdersData = CustomerFrequentOrders.fromJson(result);
      return _frequentOrdersData;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// fetch item details
  Future<List<Items>> fetchItemDetails({List<int>? itemId}) async {
    try {
      return await GraphQLService().fetchSearchItem(itemId: itemId);
    } catch (error) {
      throw error;
    }
  }

  /// get past orders
  Future<CustomerPastOrder> getCustomerPastOrders({required String userId,
    bool ascOrder=false , bool sortOnDate =true, bool sortOnLocation=false,
    bool sortOnItems = false, bool sortOnAmount =false, bool sortOnId =false}) async {
    try {

      String _pastOrders = _customerPastOrders.replaceAll(':userId',userId );
      var url = Uri.https(_baseUrl, _pastOrders, {
        ApiConstant.STATUSES : ApiConstant.STATUSES_VALUES ,
        ApiConstant.SORT :  ascOrder ? ApiConstant.SORT_ASC : ApiConstant.SORT_DESC,
        if(sortOnDate)ApiConstant.SORT_BY : ApiConstant.SORT_CREATED_AT,
        if(sortOnLocation)ApiConstant.SORT_BY : ApiConstant.SORT_LOCATION_NAME,
        if(sortOnItems)ApiConstant.SORT_BY : ApiConstant.SORT_ITEM_COUNT,
        if(sortOnAmount)ApiConstant.SORT_BY : ApiConstant.SORT_TOTAL,
        if(sortOnId)ApiConstant.SORT_BY : ApiConstant.SORT_ID,
      });
      final response = await _http.get(url,);

      final result = parseHttpResponse(response);

      CustomerPastOrder _customerOrder = CustomerPastOrder.fromJson(result);
      return _customerOrder;
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }


}
