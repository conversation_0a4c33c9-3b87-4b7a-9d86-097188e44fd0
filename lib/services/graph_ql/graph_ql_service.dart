import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

import '../../constants/api_constant.dart';
import '../../constants/app_string.dart';
import '../../graph_ql/graph_ql_config.dart';
import '../../graph_ql/graph_ql_query.dart';
import '../../helpers/http_response_helper.dart';
import '../../models/business/user_business_model.dart';
import '../../models/discount/discount_response.dart';
import '../../models/location/location_model.dart';
import '../../models/menu/menu_data.dart';
import '../../models/menu/menu_response.dart';
import '../../models/menu/search_item_response.dart';
import '../../models/menu/tag_response.dart';
import '../../utils/datetime_utils.dart';
import '../../utils/pref_utils.dart';


class GraphQLService {
  GraphQLClient? _client;

  static GraphQLService? _instance;

  GraphQLService._(){
    /// initialization
    _client = GraphQLConfig.initializeClient().value;
  }

  factory GraphQLService() {
    _instance ??= GraphQLService._();
    // since you are sure you will return non-null value, add '!' operator
    return _instance!;
  }

  /// fetch menus
  Future<List<MenuData>> fetchMenus() async {
    List<MenuData> menuList;
    try {
      String businessId= UserBusinessModel
          .fromJson(PrefsUtils.getObject(PrefKeys.business))
          .id ?? '';

      String locationId= LocationModel
          .fromJson(PrefsUtils.getObject(PrefKeys.location))
          .id ?? '';
      QueryResult? result =
      await _client?.query(QueryOptions(document: gql(GraphQLQuery.getMenus),
        variables: {ApiConstant.BUSINESS_ID : businessId ,
          ApiConstant.LOCATION_ID : locationId,
          ApiConstant.DATE : DateTimeUtils.currentDateInUTC()}, fetchPolicy: FetchPolicy.noCache,)
      );
      var data = result?.data;
      if (data != null) {
        final menus = (result?.data?['menusData']);

        MenuResponse menusResponse = MenuResponse.fromJson(jsonDecode(jsonEncode(menus)));

        if (menusResponse.menusData == null) {
          return [];
        }
        menuList = menusResponse.menusData ?? [];

        return menuList;
      } else {
        if(result!.exception!.graphqlErrors.first.message.toLowerCase()
            == ConstantString.unauthorized){
          throw UnauthorisedException();
        } else {
          throw Error();
        }
      }
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// fetch tags
  Future<List<TagData>> fetchTags() async {
    List<TagData> tags = [];
    try{
      QueryResult? result =
      await _client?.query(QueryOptions(document: gql(GraphQLQuery.getTags, ),
        fetchPolicy: FetchPolicy.noCache,
      ));
      var data = result?.data;
      if (data != null) {
        final tagsData = (result?.data?['tags']);

        TagResponse tagResponse = TagResponse.fromJson(jsonDecode(jsonEncode(tagsData)));

        if (tagResponse.tags == null) {
          return [];
        }
        tags = tagResponse.tags ?? [];

        return tags;
      }
      return tags;
    }on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }

  /// search items
  Future<List<Items>> searchItems({String searchKeyword = '',List<int>? tagIds, List<int>? itemIds,}) async {
    List<Items> items = [];
    try{
      String businessId= UserBusinessModel
          .fromJson(PrefsUtils.getObject(PrefKeys.business))
          .id ?? '';

      String locationId= LocationModel
          .fromJson(PrefsUtils.getObject(PrefKeys.location))
          .id ?? '';
      QueryResult? result =
      await _client?.query(QueryOptions(document: gql(GraphQLQuery.searchItem,),
        variables: {ApiConstant.BUSINESS_ID : businessId ,
          ApiConstant.LOCATION_ID : locationId,
          if(searchKeyword.isNotEmpty) ApiConstant.SEARCH_KEYWORD : searchKeyword,
          if(tagIds !=null) ApiConstant.TAGS : tagIds,
          if(itemIds != null) ApiConstant.ITEM_IDS : itemIds,
        }, fetchPolicy: FetchPolicy.noCache,)
      );

      var data = result?.data;

      if (data != null) {
        final searchItemData = (result?.data?['itemSearch']);

        SearchItemResponse itemSearchResponse = SearchItemResponse.fromJson(jsonDecode(jsonEncode(searchItemData)));

        if (itemSearchResponse.payload == null) {
          return [];
        }
        items = itemSearchResponse.payload ?? [];

        return items;
      } else {
        if(result!.exception!.graphqlErrors.first.message.toLowerCase() == ConstantString.unauthorized){
          throw UnauthorisedException();
        } else {
          throw Error();
        }
      }
    } on SocketException catch (error) {
      debugPrint('$error');
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }

  }


  /// fetch search item
  Future<List<Items>> fetchSearchItem({String search = '',List<int>? tagIdList, List<int>? itemId,}) async {
    List<Items> _itemList = [];
    try{
      String businessId= UserBusinessModel
          .fromJson(PrefsUtils.getObject(PrefKeys.business))
          .id ?? '';

      String locationId= LocationModel
          .fromJson(PrefsUtils.getObject(PrefKeys.location))
          .id ?? '';

      String timezone = LocationModel
          .fromJson(PrefsUtils.getObject(PrefKeys.location))
          .timezone??'America/New_York';

      QueryResult? result =
      await _client?.query(QueryOptions(document: gql(GraphQLQuery.searchItem),
        variables: {ApiConstant.BUSINESS_ID : businessId ,
          ApiConstant.LOCATION_ID : locationId,
          if(search.isNotEmpty) ApiConstant.SEARCH_KEYWORD : search,
          if(tagIdList !=null) ApiConstant.TAGS : tagIdList,
          if(itemId != null) ApiConstant.ITEM_IDs : itemId,
          ApiConstant.TIMEZONE : timezone,
        }, fetchPolicy: FetchPolicy.noCache,)
      );

      var data = result?.data;

      if (data != null) {
        final itemSearch = (result?.data?['itemSearch']);

        SearchItemResponse itemSearchResponse = SearchItemResponse.fromJson(jsonDecode(jsonEncode(itemSearch)));

        if (itemSearchResponse.payload == null) {
          return [];
        }
        _itemList = itemSearchResponse.payload ?? [];

        return _itemList;
      } else {
        if(result!.exception!.graphqlErrors.first.message.toLowerCase() == ConstantString.unauthorized){
          throw UnauthorisedException();
        } else {
          throw Error();
        }
      }
    } on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }
  }


  /// retrieve discounts
  Future<List<DiscountsData>> retrieveDiscounts({required List<int> itemIds}) async{
    List<DiscountsData> _discounts = [];
    try {
      String locationId = LocationModel
          .fromJson(PrefsUtils.getObject(PrefKeys.location))
          .id ?? '' ?? '';

      QueryResult? result =
      await _client?.query(
          QueryOptions(document: gql(GraphQLQuery.queryPresetDiscounts),
            variables: {
              ApiConstant.LOCATION_ID: locationId,
              ApiConstant.ITEM_IDs: itemIds,
            }, fetchPolicy: FetchPolicy.noCache,)
      );

      var data = result?.data;
      if(data != null) {
        final presetDiscounts = (result?.data?['presetDiscounts']);
        DiscountResponse discountResponse  = DiscountResponse.fromJson(jsonDecode(jsonEncode(presetDiscounts)));
        if (discountResponse.payload == null) {
          return [];
        }

        _discounts = discountResponse.payload?.discounts ?? [];

        return _discounts;
      }
      return  _discounts;

    }on SocketException catch (error) {
      print(error);
      throw InternetException();
    } catch (error) {
      if (error.toString().contains("XMLHttpRequest"))
        throw InternetException();
      else
        throw error;
    }

  }


  reset(){
    _instance = null;
    // _client = null;
  }

}