import 'package:http/http.dart' as http;

import '../../constants/api_constant.dart';
import '../../constants/api_headers.dart';
import '../../utils/pref_utils.dart';

class AuthInterceptorClient extends http.BaseClient{

  Map<String, String> get _authApiHeaders {
    return {
      ApiHeadersConstant.CONTENT_TYPE: ApiHeadersConstant.CONTENT_TYPE_VALUE,
      ApiHeadersConstant.AUTHORIZATION: ApiConstant.BEARER + PrefsUtils.getString(PrefKeys.authToken)!,
    };
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    request.headers.addAll(_authApiHeaders);

    return request.send();
  }

}