import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gr8tables_server_manager/helpers/notification_permission.dart';
import 'utils/pref_utils.dart';

import 'helpers/flavor_config.dart';
import 'my_app.dart';

void main() async{
  ApiConfig.setEnvironment(Environment.DEV);

  WidgetsFlutterBinding.ensureInitialized();
  // initialize shared preferences here to ensure that it is initialized and can then be accessed
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light.copyWith(
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.dark,
    statusBarColor: Colors.white,
    systemNavigationBarColor: Colors.white,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));
  // Configure background message handler
  await PrefsUtils.init();
  await Firebase.initializeApp();

  runApp(const MyApp());
}

