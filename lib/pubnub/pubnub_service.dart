import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pubnub/networking.dart';
import 'package:pubnub/pubnub.dart';

import '../constants/app_string.dart';
import '../helpers/flavor_config.dart';
import '../models/auth/user_model.dart';
import '../models/pubnub/pubnub_order_emit.dart';
import '../providers/pubnub/pubnub_provider.dart';
import '../utils/pref_utils.dart';
import 'pubnub_constants.dart';

class PubNubService {

  static PubNubService? _instance;

  PubNubService._();

  factory PubNubService() => _instance ??= PubNubService._();


  PubNub? _pubnub;
  Subscription? _subscription;


  /// initialise pubnub
  Future<void> initPubnub() async{
    String pubKey = ApiConfig.pubnubPublishKey;
    String subKey = ApiConfig.pubnubSubscribeKey;

    String userId = UserModel.fromJson(PrefsUtils.getObject(PrefKeys.user)).id ??'';
     if(userId.isNotEmpty) {
       var keySet = Keyset(subscribeKey:  subKey, publishKey: pubKey,userId  : UserId(userId));
       _pubnub ??= PubNub(networking: NetworkingModule(retryPolicy: RetryPolicy.exponential(maxRetries: 10)), defaultKeyset: keySet);
     }
  }


 /// subscribe to order channel
  dynamic  subScribeToOrderChannel({required String channelName, required BuildContext context}) async{
    debugPrint('subscribing to channel $channelName');
    Subscription? subscription;
    if( null != _pubnub) {
      subscription = _pubnub?.subscribe(channels: {channelName});
      debugPrint('subscription ::: $subscription');
      debugPrint('subscription ::: ${subscription?.channels}');

      subscription?.messages.listen((message) async{
        debugPrint('::: new message ::: ${message.messageType} :: ${jsonEncode(message.payload)}');
        PubNubOrderEmit pubnubOrderEmit = PubNubOrderEmit.fromJson(message.payload);

        if(null != pubnubOrderEmit.payload){
          switch(pubnubOrderEmit.declaration){
            case PubNubConstants.EVENT_ORDER_PLACED:
              debugPrint(':: order_placed served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 && pubnubOrderEmit.payload?.platform!.toLowerCase() == ConstantString.platformWeb) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_ORDER_PLACED: pubnubOrderEmit.payload!});

              }
              break;
            case PubNubConstants.EVENT_ORDER_CREATED:
              debugPrint(':: order_created  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_ORDER_CREATED: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_ORDER_PROCESSED:
              debugPrint(':: order_proceed  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_ORDER_PROCESSED: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_TABLE_ACTIVATED:
              debugPrint(':: table_activated  served ::');
              if(pubnubOrderEmit.payload?.tableId! !=0 ) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_TABLE_ACTIVATED: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_USER_JOINED_TABLE:
              debugPrint(':: user_joined_table  served ::');
              if(pubnubOrderEmit.payload?.tableId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_USER_JOINED_TABLE: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_CALL_SERVER:
              debugPrint(':: event_call_server  served ::');
              if(pubnubOrderEmit.payload?.tableId! !=0 ) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_CALL_SERVER: pubnubOrderEmit});
              }
              break;
            case PubNubConstants.EVENT_CART_USER_UPDATED:
              debugPrint(':: cart_user_updated  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_CART_USER_UPDATED: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_USER_CART:
              debugPrint(':: user_cart served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_USER_CART: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_PARTIAL_PAYMENT_RECEIVED:
              debugPrint(':: full_payment_received  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_PARTIAL_PAYMENT_RECEIVED: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_FULL_PAYMENT_RECEIVED:
              debugPrint(':: full_payment_received  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_FULL_PAYMENT_RECEIVED: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_ORDER_DISCOUNT:
              debugPrint(':: order_discount  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_ORDER_DISCOUNT: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_ORDER_PROMO_CODE:
              debugPrint(':: order_promocode  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().dataStreamController.sink.add({
                  PubNubConstants.EVENT_ORDER_PROMO_CODE: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_ORDER_POINTS_REDEEMED:
            case PubNubConstants.EVENT_ORDER_LOYALTY:
              debugPrint(':: order_points_redeemed order_loyalty served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_ORDER_LOYALTY: pubnubOrderEmit.payload!});
              }
              break;
            case PubNubConstants.EVENT_ITEM_MOVE:
              debugPrint(':: event_move_item  served ::');
              if(pubnubOrderEmit.payload?.cartId! !=0 ) {
                context.read<PubNubProvider>().cartStreamController.sink.add({
                  PubNubConstants.EVENT_ITEM_MOVE: pubnubOrderEmit.payload!});
              }
              break;
          }
        }
      });
      return subscription;
    }
  }

  /// destroy pubnub and unsubscribe all
   destroyPubNub() async{

    if(_pubnub != null) {
      _pubnub?.unsubscribeAll().then((value) {
        debugPrint(' unsubscribe all channel');
      });
      _pubnub = null;
    }
     if(null != _subscription) {
       if(! _subscription!.isCancelled) {
         await _subscription?.cancel();
       }
     }

   }


}
