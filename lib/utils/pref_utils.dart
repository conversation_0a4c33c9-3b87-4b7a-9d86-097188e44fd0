import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';


enum PrefKeys {
  authToken,
  user,
  business,
  location,
  paymentTerminal,
  cartData,
  fcmToken,
  fcmTableId,
  fcmCartId,
  fcmTopic<PERSON>ey,
  fcmReceived, // alert tab already active and reopen alert tab, after notification comes
}

class PrefsUtils {
  static SharedPreferences? _prefs;

  static Future<SharedPreferences> get _instance async =>
      _prefs ?? await SharedPreferences.getInstance();

  static Future<SharedPreferences?> init() async {
    _prefs = await _instance;
    return _prefs;
  }

  static String _getPrefKey(PrefKeys key) {
    switch (key) {
      case PrefKeys.authToken:
        return 'authToken';
      case PrefKeys.user:
        return 'user';
      case PrefKeys.business:
        return 'business';
      case PrefKeys.location:
        return 'location';
      case PrefKeys.cartData:
        return 'cartData';
      case PrefKeys.fcmToken:
        return 'fcmToken';
      case PrefKeys.fcmTableId:
        return 'fcmTableId';
      case PrefKeys.fcmCartId:
        return 'fcmCartId';
      case PrefKeys.fcmTopicKey:
        return 'fcmTopicKey';
      case PrefKeys.fcmReceived:
        return 'fcmMessageReceive';
      default:
        return 'unknown';
    }
  }

  static String? getString(PrefKeys prefKey) {
    String? key = _getPrefKey(prefKey);

    return _prefs!.getString(key);
  }

  static Future<bool> setString(PrefKeys prefKey, String authToken) async {
    String key = _getPrefKey(prefKey);

    return _prefs!.setString(key, authToken);
  }

  static Map<String, dynamic> getObject(PrefKeys prefKey) {
    String? key = _getPrefKey(prefKey);
    String? rawJson = _prefs!.getString(key);
    return rawJson == null ? {'': ''} : jsonDecode(rawJson);
  }

  static Future<bool> setJson(
      PrefKeys prefKey, Map<String, dynamic> jsonData) async {
    String key = _getPrefKey(prefKey);
    return _prefs!.setString(key, jsonEncode(jsonData));
  }

  static Future<bool> removeKey(PrefKeys prefKey) async {
    String key = _getPrefKey(prefKey);

    return await _prefs!.remove(key);
  }

  static Future<bool> clear() async {
    return await _prefs!.clear();
  }

  static Future<void> printAllSharedPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    Set<String> keys = prefs.getKeys();

    if (keys.isNotEmpty) {
      for (String key in keys) {
        var value = prefs.get(key); // Get value based on the key type (String, int, etc.)
        debugPrint('Key: $key, Value: $value');
      }
    } else {
      debugPrint('No SharedPreferences values found.');
    }
  }

}
