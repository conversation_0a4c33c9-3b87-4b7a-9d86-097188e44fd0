import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/providers/auth/auth_provider.dart';
import 'package:provider/provider.dart';

import '../constants/app_color.dart';
import '../constants/app_string.dart';
import '../custom_widgets/app_text_style.dart';
import '../my_app.dart';
import '../providers/alerts/alerts_provider.dart';
import '../providers/cartview/cart_view_provider.dart';
import '../providers/common/shareable_provider.dart';
import '../providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import '../providers/homescreen/bottom_sheet_provider.dart';
import '../providers/homescreen/bottomview_provider.dart';
import '../providers/menuview/menu_view_provider.dart';
import '../providers/notification/notification_provider.dart';
import '../providers/package_info/package_info_provider.dart';
import '../providers/pubnub/pubnub_provider.dart';
import 'app_routes.dart';

bool isBigScreenResolution(BuildContext context) {
  final mediaQuery = MediaQuery.maybeOf(context);
  if (mediaQuery == null) {
    return false; // Default to false if MediaQuery is not available
  }
  return mediaQuery.size.height >= 600.0;
}

void showLoaderDialog(BuildContext context, {String msg = "Loading..."}) {
  AlertDialog alert = AlertDialog(
    surfaceTintColor:ColorConstant.colorThemeWhite,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(0.0),
    ),
    content: Row(
      children: [
        const Padding(
          padding: EdgeInsets.only(right: 20.0),
          child: CircularProgressIndicator(
            color: ColorConstant.colorBlueDark,
          ),
        ),
        Flexible(
          child: Text(
            msg,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    ),
  );
  if (!(context.read<ShareableProvider>().isLoaderShowing)) {
    context.read<ShareableProvider>().setLoaderShowing(true);
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return PopScope(canPop: false, child: alert);
      },
    );
  }
}

void hideLoaderDialog(BuildContext context) {
  if (context.read<ShareableProvider>().isLoaderShowing) {
    context.read<ShareableProvider>().setLoaderShowing(false);
    Navigator.of(context, rootNavigator: true).pop();
  }
}

showInterNetConnectionDialog(BuildContext context) {
  Provider.of<AlertsProvider>(navigatorKey.currentState!.overlay!.context,
          listen: false)
      .setNewAlert(
    alertData: AlertModel(
        alertType: AlertType.alertDialog,
        title: ConstantString.error,
        message: ConstantString.internetError,
        alertOnPressed: () {
          navigatorKey.currentState!.overlay!.context
              .read<AlertsProvider>()
              .dismissDialog();
        }),
  );
}

/// common error alert dialog
errorAlertDialog(error) {
  navigatorKey.currentState!.overlay!.context
      .read<AlertsProvider>()
      .setNewAlert(
        alertData: AlertModel(
          alertType: AlertType.alertDialog,
          title: ConstantString.error,
          message: error.toString(),
        ),
      );
}

/// common unauthorized error alert dialog
errorUnauthorisedAlertDialog(BuildContext context, error) {
  navigatorKey.currentState!.overlay!.context
      .read<AlertsProvider>()
      .setNewAlert(
        alertData: AlertModel(
          alertType: AlertType.alertDialog,
          title: ConstantString.error,
          message: error.toString(),
          alertOnPressed: () {
            context.read<NotificationProvider>().removeDevice();
            context.read<AlertsProvider>().dismissDialog();
            context.read<CartViewProvider>().reset();
            context.read<ShareableProvider>().reset();
            context.read<DineInTakeoutProvider>().reset();
            context.read<BottomSheetProvider>().reset();
            context.read<BottomViewProvider>().reset();
            context.read<MenuViewProvider>().reset();
            context.read<PackageInfoProvider>().reset();
            context.read<PubNubProvider>().reset();
            context.read<AuthProvider>().logoutUser();
            navigateToLogin(context);
          },
        ),
      );
}

/// input decoration outline border
OutlineInputBorder outLineBorder(BuildContext context){
 return OutlineInputBorder(
    borderSide: const BorderSide(
      color: ColorConstant.colorBlueLight_8,
      width: 1,
    ),
    borderRadius: BorderRadius.circular(
      isBigScreenResolution(context) ? 6 : 4,
    ),
  );
}

/// show business/location list dialog
showContentDialog(BuildContext context, Widget dialogView) {
  showDialog(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext _) {
      return PopScope(
          canPop: false,
          child: Container(
            margin: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 20 : 18),
            child: Dialog(
                insetPadding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ?10 :8),
                child: dialogView),
          )
      );
    },
  );
}

/// rounded to Two digits precise
double roundDouble(num value){
  /// after decimal 2  digits
  num mod = pow(10.0 , 2);
  return ((value * mod).round().toDouble() / mod);
}

///return formatted phone number ************
String getFormattedPhoneNumber(String phoneNumber) {
  String formattedPhoneNumber = "";
  try {
    if (phoneNumber.length > 6) {
      formattedPhoneNumber = phoneNumber.replaceFirstMapped(
          new RegExp('(\\d{3})(\\d{3})(\\d+)'), (match) {
        return '${match.group(1)}-${match.group(2)}-${match.group(3)}';
      });
    } else {
      formattedPhoneNumber =
          phoneNumber.replaceFirstMapped( RegExp('(\\d{3})(\\d+)'), (match) {
            return '${match.group(1)}-${match.group(2)}';
          });
    }
    return formattedPhoneNumber;
  }catch(e){
    return phoneNumber;
  }
}

/// round shape
Widget shapeRound(childWidget, {double mHeight = 35.00, double mWidth = 35.00,
  Color backColor = ColorConstant.colorGreenLight_3}) {
  return ClipOval(
    child: Container(
      color: backColor,
      height: mHeight,
      width: mWidth,
      child: childWidget,
    ),
  );
}
/// get  name initials
String getInitials(String name) {
  name = (name ?? '').replaceAll('  ', ' ');
  String initials = "";

  if (name.isEmpty) return '';
  if (name.length == 1) return name;

  if (name.contains(' ')) {
    List<String> names = name.split(" ");
    int numWords = 2;

    if (numWords > names.length) {
      numWords = names.length;
    }
    for (var i = 0; i < numWords; i++) {
      initials += names[i][0];
    }
  } else {
    initials = name.substring(0, 2).toUpperCase();
  }
  return initials;
}

/// show zigzag image at bottom
Widget cutImage(double yAxisValue, double screenWidth, String imageName) {
  return Container(
    transform: Matrix4.translationValues(0.0, yAxisValue, 0.0),
    child: Image.asset(
      imageName,
      width: screenWidth,
    ),
  );
}
showErrorDialog({String error = ''}){
  navigatorKey.currentState!.overlay!.context
      .read<AlertsProvider>()
      .setNewAlert(
    alertData: AlertModel(
      alertType: AlertType.alertDialog,
      title: ConstantString.error,
      message: error.toString(),
    ),
  );
}

showSeverCallDialog({String message = ''}){
  navigatorKey.currentState!.overlay!.context
      .read<AlertsProvider>()
      .setNewAlert(
    alertData: AlertModel(
      alertType: AlertType.alertDialog,
      title: "",
      message: message.toString(),
    ),
  );
}

/// show snack bar
showSnackBar(String message) {
  if(navigatorKey.currentState == null) return;
  ScaffoldMessenger.of(navigatorKey.currentState!.overlay!.context).showSnackBar(
    SnackBar(
      backgroundColor: ColorConstant.colorBlueDark,
      content: Text(message, style: AppTextStyle.smallTextStyle.copyWith(
        color: ColorConstant.colorThemeWhite,
        fontWeight: FontWeight.w400,
      ),),
      duration: Duration(seconds: 2,),
    ),
  );
}

// Capitalize First Letter of Each Word
String toTitleCase(String text) {
  if (text.isEmpty) return text;

  return text
      .split(' ') // Split the string by spaces
      .map((word) => word[0].toUpperCase() + word.substring(1).toLowerCase()) // Capitalize first letter of each word
      .join(' '); // Join the words back together
}

/// show custom dialog
void showCustomDialog(BuildContext context, Widget child,
    {isDismissible = true,
      bool autoDismiss = false}) {
  showDialog(
      context: context,
      barrierDismissible: isDismissible,
      builder: (BuildContext buildContext) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 10,),
          backgroundColor: ColorConstant.colorThemeWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isBigScreenResolution(context) ? 12.0 : 10.0),
          ),
          child: Container(
            child: child,
          ),
        );
      });
}