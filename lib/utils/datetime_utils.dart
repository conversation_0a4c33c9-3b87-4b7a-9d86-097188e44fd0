
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';


class DateTimeUtils {

  static const String dd_MM = "dd/MM h:mm a";
  static const String yyyy_MM_dd_T_HH_mm_ss_SSS_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
  static const String at_yyyy_MM_dd_hh_mm_a = "yyyy - MM - dd '|' hh:mm a";
  static const String dd_mm_yyyy_hh_mm_a = "dd/MM/yyyy hh:mm a";
  static const String dd_mm_yyyy_kk_mm_a = "dd/MM/yyyy h:mm a";
  static const String hh_mm_a = "hh:mma";
  static const String MMMM_d_yyyy = "MMM d, yyyy";
  static const String yyyy_mm_dd = "yyyy-MM-dd";


  ///Convert current time to local format
  static String utcTimeToConvertLocalTime(String utcTime,{String formattedString = dd_MM }){
    try{
      DateTime utcDateTime = DateTime.parse(utcTime);
      DateTime localDateTime = utcDateTime.toLocal();
      String formattedDate = DateFormat(formattedString).format(localDateTime);
      return formattedDate;
    } catch(e,stacktrace){
      debugPrint("Error utcTimeToConvertLocalTime ${stacktrace.toString()}");
      return utcTime;
    }
  }




  static getTimeDifference(String? cartTime) {
    if (cartTime == null) return 0;
    try {
      String cartTimeString = utcTimeToConvertLocalTime(cartTime ?? '',formattedString:  yyyy_MM_dd_T_HH_mm_ss_SSS_Z,);
      String now =utcTimeToConvertLocalTime(DateTime.now().toString() ?? '', formattedString:  yyyy_MM_dd_T_HH_mm_ss_SSS_Z,);

      Duration difference =  DateTime.parse(now).difference(DateTime.parse(cartTimeString));
      return printDuration(difference);

    } catch (e) {
      print('Exception while get time difference for order');
    }
  }

  /// print duration
  static String printDuration(Duration duration) {
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
      return "${twoDigits(duration.inHours)}:$twoDigitMinutes";
  }

  /// convert to 2 digits
  static String twoDigits(int n) {
    if (n >= 10) return "$n";
    return "0$n";
  }

  /// Date time convert to time ago label format
  /// DateTime pastDate = DateTime.now().subtract(Duration(minutes: 2));
  static String timeAgo(String dateTime) {

    String cartTimeString = utcTimeToConvertLocalTime(dateTime ?? '',formattedString:  yyyy_MM_dd_T_HH_mm_ss_SSS_Z,);
    String now =utcTimeToConvertLocalTime(DateTime.now().toString() ?? '', formattedString:  yyyy_MM_dd_T_HH_mm_ss_SSS_Z,);

    final Duration difference = DateTime.parse(now).difference(DateTime.parse(cartTimeString));

    if (difference.inDays > 365) {
      final int years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    } else if (difference.inDays > 30) {
      final int months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? '1 day ago' : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? '1 hour ago' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? '1 minute ago' : '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

 /// Compare utc time with local time and return in minutes
static int compareTimeWithCurrentInMinutes(String utcTime) {
    int differenceInMinutes = 0;

    String cartTimeString = utcTimeToConvertLocalTime(utcTime ?? '',formattedString:  yyyy_MM_dd_T_HH_mm_ss_SSS_Z,);
    String now =utcTimeToConvertLocalTime(DateTime.now().toString() ?? '', formattedString:  yyyy_MM_dd_T_HH_mm_ss_SSS_Z,);

    final Duration difference = DateTime.parse(now).difference(DateTime.parse(cartTimeString));

    if (difference.inDays > 0) {
      differenceInMinutes = difference.inMinutes;
    } else if (difference.inHours > 0) {
      differenceInMinutes = difference.inMinutes;
    } else if (difference.inMinutes > 0) {
      differenceInMinutes = difference.inMinutes;
    } else {
      differenceInMinutes = 0;
    }

    return differenceInMinutes;
  }

  /// get current date in utc
  static String currentDateInUTC(){
    String formatted='';
    try {
      var dateUtc = DateTime.now().toUtc();
      formatted = DateFormat(yyyy_mm_dd).format(dateUtc);
      return formatted;
    }catch(e){
      return formatted;
    }
  }
}