import '../constants/app_string.dart';

class Validator {
  static String? validateEmail(value) {
    if (value.isEmpty) {
      return ConstantMessages.enterEmail;
    } else if (!RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value)) {
      return ConstantMessages.enterValidEmail;
    }
    return null;
  }

  static String? validatePassword(value) {
    if (value.isEmpty) {
      return ConstantMessages.enterPassword;
    }

    String pattern = r'^(?=.*?[A-Z])(?=.*?[-!@#\$&*~]).{9,}$';
    RegExp regExp = RegExp(pattern);
    if (!regExp.hasMatch(value)) {
      return ConstantMessages.enterValidPassword;
    }
    return null;
  }

}