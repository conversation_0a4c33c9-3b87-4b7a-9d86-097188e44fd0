import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/my_app.dart';
import 'package:gr8tables_server_manager/screens/alerts/alerts_view.dart';
import 'package:gr8tables_server_manager/screens/cart/cash_payment_view.dart';
import 'package:gr8tables_server_manager/screens/history/customer_order_history_view.dart';
import 'package:gr8tables_server_manager/screens/history/order_details.dart';
import 'package:gr8tables_server_manager/screens/orders/dine_in_takeout_view.dart';
import 'package:gr8tables_server_manager/screens/orders/dine_in_view.dart';
import 'package:provider/provider.dart';
import '../constants/app_string.dart';
import '../models/menu/menu_data.dart';
import '../models/notification/all_notification_response.dart';
import '../providers/menuview/item_modifier_view_provider.dart';
import '../screens/alerts/notif_item_discount_details_screen.dart';
import '../screens/alerts/notif_cart_discount_sent_view.dart';
import '../screens/alerts/notif_cart_discount_details_screen.dart';
import '../screens/auth/forgot_password.dart';
import '../screens/auth/login_screen.dart';
import '../screens/cart/bill_view.dart';
import '../screens/cart/cart_custom_discountview.dart';
import '../screens/cart/cart_view.dart';
import '../screens/history/history_view.dart';
import '../screens/home/<USER>';
import '../screens/menu/item_modifier_view.dart';
import '../screens/menu/menu_view.dart';

class AppRoutes {
  static const String LOGIN_ROUTE = '/login';
  static const String FORGOT_PASSWORD_ROUTE = '/forgotpassword';
  static const String HOME_SCREEN = '/homescreen';
  static const String DINE_IN_SCREEN = '/dineinscreen';
  static const String DINE_IN_TAKEOUT_SCREEN = '/dineintakeoutviewscreen';
  static const String CART_SCREEN = '/cartscreen';
  static const String HISTORY_SCREEN = '/historyscreen';
  static const String ALERTS_SCREEN = '/alertsscreen';
}

navigateToBack(BuildContext context) {
  FocusScope.of(context).unfocus();
  Navigator.of(context).pop();
}

navigateToLogin(BuildContext context) {
  Navigator.of(context).pushAndRemoveUntil(
    MaterialPageRoute(
      builder: (_) => const LoginScreen(),
    ),
    (Route<dynamic> route) => false,
  );
}

navigateToForgotPassword(BuildContext context) {
  Navigator.of(context, rootNavigator: true).push(
    MaterialPageRoute(
      builder: (context) => const ForgotPassword(),
    ),
  );
}

navigateToHomeScreen(BuildContext context, [String pageName = NavigationString.homeView]) {
  // Navigator.of(context).pushAndRemoveUntil(
  //   MaterialPageRoute(
  //     builder: (_) => const HomeScreen(),
  //   ),
  //       (Route<dynamic> route) => false,
  // );
  Navigator.of(context).pushAndRemoveUntil(
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => HomeScreen(selectedPage: pageName),
      transitionDuration: Duration.zero, // No animation
    ),
        (Route<dynamic> route) => false,
  );
}

navigateToLoginAutoLogout(BuildContext context) {
  Navigator.of(context).pushReplacement(
    MaterialPageRoute(
      builder: (_) => const LoginScreen(),
    ),
  );
}

navigateToDineInTakeoutScreen(BuildContext context) {
  Navigator.of(context).pushReplacement(
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) =>
          const DineInTakeoutView(),
      transitionDuration: Duration(seconds: 0), // Removes animation duration
    ),
  );
}

navigateToCartScreen(BuildContext context, int cartId ,
     int tableId) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => CartView(cartId: cartId,tableId: tableId,),
  ));
}

navigateToMenuScreen(BuildContext context,) async{
  Navigator.of(context).push(CupertinoPageRoute(
    builder: (context) =>  MenuView(),
  ));
}

navigateToBillScreen(BuildContext context,) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => const BillView(),
  ));
}

navigateToItemModifierScreen(BuildContext context,Items itemData,
     String categoryName) async{
  Navigator.push(context ,CupertinoPageRoute(
    builder: (_) =>
        ChangeNotifierProvider(create: (_) => ItemModifierViewProvider(),
             child: ItemModifierView(itemData: itemData,categoryName: categoryName,),
        ),
  )).then((value)  {
    debugPrint(':: then of item $value ::');
    if(value != null) {
      if(value == true) {
        if(Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
      }
    }
  });
}

navigateToCashPaymentScreen(BuildContext context) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => CashPaymentView(),
  ));
}

navigateToHistoryScreen(BuildContext context) {
  Navigator.of(context).pushReplacement(PageRouteBuilder(
    pageBuilder: (context, animation, secondaryAnimation) => const HistoryView(),
    transitionDuration: Duration(seconds: 0), // Removes animation duration
  ));
}

navigateToAlertsScreen(BuildContext context) {
  Navigator.of(context).pushReplacement(PageRouteBuilder(
    pageBuilder: (context, animation, secondaryAnimation) => const AlertsView(),
    transitionDuration: Duration(seconds: 0), // Removes animation duration
  ));
}

navigateToCustomerDetailsScreen(BuildContext context, String userId, String userName) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => CustomerOrderHistoryView(userId: userId,userName: userName,),
  ));
}

navigateToOrderDetailsScreen(BuildContext context, int cartId ,
    int tableId) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => OrderDetails(cartId: cartId,tableId: tableId,),
  ));
}

navigateToNotifCartDiscountDetailsScreen(BuildContext context,UserNotificationData userData) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => NotifCartDiscountDetailsScreen(notification: userData),
  ));
}

navigateToNotifItemDiscountDetailsScreen(BuildContext context,UserNotificationData userData) {
  Navigator.of(context, rootNavigator: false).push(CupertinoPageRoute(
    builder: (context) => NotifItemDiscountDetailsScreen(notification: userData),
  ));
}
