

import '../../cart/cart_promotions/cart_promocode.dart';

class CartPromoCodeResponse {
  String? message;
  CartPromoCode? payload;

  CartPromoCodeResponse({this.message, this.payload});

  CartPromoCodeResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  CartPromoCode.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.toJson();
    }
    return data;
  }
}

