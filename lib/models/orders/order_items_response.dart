

import '../item/batch/batch_data.dart';
import '../item/batch/custom_lot_data.dart';

class OrderItemsResponse {
  String? message;
  List<Items>? payload;

  OrderItemsResponse({this.message, this.payload});

  OrderItemsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <Items>[];
      json['payload'].forEach((v) {
        payload!.add(new Items.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Items {
  int? id;
  int? orderId;
  String? locationId;
  int? categoryId;
  String? refId;
  String? categoryName;
  int? categorySortOrder;
  String? brandName;
  int? brandId;
  String? productName;
  String? barcode;
  int? productId;
  int? parentProductId;
  String? productImage;
  String? productComplianceType;
  String? productVariationName;
  String? sku;
  num? scannedCount;
  num? unit;
  num? refundedUnit;
  bool? refunded;
  num? unitPrice;
  num? lineTotal;
  num? unitActualPrice;
  num? cannabisEquivalentWeight;
  String? note;
  String? addedBy;
  String? addedById;
  String? addedByName;
  String? updatedBy;
  String? updatedById;
  String? updatedByName;
  String? platform;
  String? deletedAt;
  String? createdAt;
  String? updatedAt;
  bool? selected = false;
  OrderPromotionItem? orderDiscountItem;
  OrderPromotionItem? orderPromoCodeItem;
  OrderPromotionItem? orderRewardItem;
  num? afterPromotionValue = 0;
  num? customDiscount = 0;
  List<CustomLotData>? customLotData; //Used for show unique dropdown batch data
  List<BatchData>? productLots; //Getting  individual batch data for the product
  List<OrderItemLots>? orderItemLots; //Lot created for the unit and getting from the server
  List<OrderItemLots>? finalOrderItemLots; // Updated final batch data sent to the server
  String? oldLotNameValue;
  List<OrderPromotionItem>? orderCustomDiscountItem;
  List<OrderItemTax>? orderItemTax;
  num? itemTotalTax;
  bool? listItemSelected;
  bool? freeItem;

  Items({
    this.id,
    this.orderId,
    this.locationId,
    this.categoryId,
    this.categoryName,
    this.categorySortOrder,
    this.brandName,
    this.brandId,
    this.productName,
    this.barcode,
    this.productId,
    this.parentProductId,
    this.productImage,
    this.productComplianceType,
    this.productVariationName,
    this.sku,
    this.scannedCount,
    this.unit,
    this.refundedUnit,
    this.refunded,
    this.unitPrice,
    this.lineTotal,
    this.unitActualPrice,
    this.cannabisEquivalentWeight,
    this.note,
    this.addedBy,
    this.addedById,
    this.addedByName,
    this.updatedBy,
    this.updatedById,
    this.updatedByName,
    this.platform,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.selected,
    this.orderDiscountItem,
    this.orderPromoCodeItem,
    this.afterPromotionValue,
    this.customDiscount,
    this.customLotData,
    this.productLots,
    this.orderItemLots,
    this.finalOrderItemLots,
    this.oldLotNameValue,
    this.orderCustomDiscountItem,
    this.orderItemTax,
    this.itemTotalTax,
    this.listItemSelected,
    this.refId,
    this.orderRewardItem,
    this.freeItem,
  });

  factory Items.deepCopy(Items itemToCopy) =>  new Items(
    id :itemToCopy.id,
    orderId: itemToCopy.orderId,
    locationId: itemToCopy.locationId,
    categoryId:itemToCopy.categoryId,
    categoryName:itemToCopy.categoryName,
    categorySortOrder :itemToCopy.categorySortOrder,
    brandName: itemToCopy.brandName,
    brandId:itemToCopy.brandId,
    productName : itemToCopy.productName,
    barcode :itemToCopy.barcode,
    productId :itemToCopy.productId,
    parentProductId :itemToCopy.parentProductId,
    productImage:itemToCopy.productImage,
    productComplianceType :itemToCopy.productComplianceType,
    productVariationName:itemToCopy.productVariationName,
    sku:itemToCopy.sku,
    scannedCount :itemToCopy.scannedCount,
    unit:itemToCopy.unit,
    refundedUnit:itemToCopy.refundedUnit,
    refunded:itemToCopy.refunded,
    unitPrice:itemToCopy.unitPrice,
    lineTotal : itemToCopy.lineTotal,
    unitActualPrice:itemToCopy.unitActualPrice,
    cannabisEquivalentWeight: itemToCopy.cannabisEquivalentWeight,
    note:itemToCopy.note,
    addedBy:itemToCopy.addedBy,
    addedById:itemToCopy.addedById,
    addedByName: itemToCopy.addedByName,
    updatedBy :itemToCopy.updatedBy,
    updatedById:itemToCopy.updatedById,
    updatedByName:itemToCopy.updatedByName,
    platform:itemToCopy.platform,
    deletedAt:itemToCopy.deletedAt,
    createdAt:itemToCopy.createdAt,
    updatedAt:itemToCopy.updatedAt,
    selected:itemToCopy.selected,
    orderDiscountItem :itemToCopy.orderDiscountItem,
    orderPromoCodeItem :itemToCopy.orderPromoCodeItem,
    afterPromotionValue :itemToCopy.afterPromotionValue,
    customDiscount :itemToCopy.customDiscount,
    customLotData: itemToCopy.customLotData,
    productLots: itemToCopy.productLots,
    orderItemLots:itemToCopy.orderItemLots,
    finalOrderItemLots:itemToCopy.finalOrderItemLots,
    oldLotNameValue:itemToCopy.oldLotNameValue,
    orderCustomDiscountItem: itemToCopy.orderCustomDiscountItem,
    orderItemTax: itemToCopy.orderItemTax,
    itemTotalTax:itemToCopy.itemTotalTax,
    listItemSelected:itemToCopy.listItemSelected,
    refId:itemToCopy.refId,
    orderRewardItem : itemToCopy.orderRewardItem,
    freeItem: itemToCopy.freeItem,
  );

  Items.fromJson(Map<String, dynamic> json) {
    try {
      id = json['id'];
      orderId = json['orderId'];
      locationId = json['locationId'];
      categoryId = json['categoryId'];
      categoryName = json['categoryName']??'';
      categorySortOrder = json['categorySortOrder'] ?? 0;
      brandName = json['brandName'] ??'';
      brandId = json['brandId'];
      productName = json['productName'];
      barcode = json['barcode'] ?? '';
      productId = json['productId'];
      parentProductId = json['parentProductId'];
      productImage = json['productImage'];
      productComplianceType = json['productComplianceType'];
      productVariationName = json['productVariationName'];
      sku = json['sku'] ?? '';
      unit = json['unit'];
      refundedUnit = json['refundedUnit'];
      refunded = json['refunded'] ?? false;
      scannedCount = json['scannedCount'] ?? 0;
      unitPrice = json['unitPrice'];
      lineTotal = json['lineTotal'] ?? 0;
      unitActualPrice = json['unitActualPrice'];
      cannabisEquivalentWeight = json['cannabisEquivalentWeight'] ?? 0;
      note = json['note'];
      addedBy = json['addedBy'];
      addedById = json['addedById'];
      addedByName = json['addedByName'];
      updatedBy = json['updatedBy'];
      updatedById = json['updatedById'];
      updatedByName = json['updatedByName'];
      platform = json['platform'];
      deletedAt = json['deletedAt'];
      createdAt = json['createdAt'];
      updatedAt = json['updatedAt'];
      freeItem = json['freeItem'] ?? false;
      orderDiscountItem = json['orderDiscountItem'] != null
          ? OrderPromotionItem.fromJson(json['orderDiscountItem'])
          : null;
      orderPromoCodeItem = json['orderPromoCodeItem'] != null
          ? OrderPromotionItem.fromJson(json['orderPromoCodeItem'])
          : null;
      orderRewardItem = json['orderRewardItem'] != null
          ? OrderPromotionItem.fromJson(json['orderRewardItem'])
          : null;

      selected = false;
      afterPromotionValue = 0;
      customDiscount = 0;
      if (json['orderCustomDiscountItem'] != null) {
        orderCustomDiscountItem = <OrderPromotionItem>[];
        json['orderCustomDiscountItem'].forEach((v) {
          orderCustomDiscountItem!.add( OrderPromotionItem.fromJson(v));
        });
      }else {
        orderCustomDiscountItem = <OrderPromotionItem>[];
      }

      if (json['orderItemLots'] != null) {
        orderItemLots = <OrderItemLots>[];
        json['orderItemLots'].forEach((v) {
          orderItemLots!.add(new OrderItemLots.fromJson(v));
        });
      } else {
        orderItemLots = <OrderItemLots>[];
      }

      if (json['orderItemTax'] != null) {
        orderItemTax = <OrderItemTax>[];
        json['orderItemTax'].forEach((v) {
          orderItemTax!.add(new OrderItemTax.fromJson(v));
        });
      }else {
        orderItemTax = <OrderItemTax>[];
      }

      finalOrderItemLots = [];
      customLotData = [];
      productLots = [];
      oldLotNameValue = '';
      itemTotalTax = 0;
      listItemSelected = false;
      refId ='';
    } catch (e, stacktrace) {
      print('Items response model class ${stacktrace.toString()}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['orderId'] = this.orderId;
    data['locationId'] = this.locationId;
    data['categoryId'] = this.categoryId;
    data['categoryName'] = this.categoryName;
    data['categorySortOrder'] = this.categorySortOrder;
    data['brandName'] = this.brandName;
    data['brandId'] = this.brandId;
    data['productName'] = this.productName;
    data['barcode'] = this.barcode;
    data['productId'] = this.productId;
    data['parentProductId'] = this.parentProductId;
    data['productImage'] = this.productImage;
    data['productComplianceType'] = this.productComplianceType;
    data['productVariationName'] = this.productVariationName;
    data['sku'] = this.sku;
    data['unit'] = this.unit;
    data['refundedUnit'] = this.refundedUnit;
    data['refunded'] = this.refunded;
    data['scannedCount'] = this.scannedCount;
    data['unitPrice'] = this.unitPrice;
    data['lineTotal'] = this.lineTotal;
    data['unitActualPrice'] = this.unitActualPrice;
    data['cannabisEquivalentWeight'] = this.cannabisEquivalentWeight;
    data['note'] = this.note;
    data['addedBy'] = this.addedBy;
    data['addedById'] = this.addedById;
    data['addedByName'] = this.addedByName;
    data['updatedBy'] = this.updatedBy;
    data['updatedById'] = this.updatedById;
    data['updatedByName'] = this.updatedByName;
    data['platform'] = this.platform;
    data['deletedAt'] = this.deletedAt;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['selected'] = this.selected;
    data['freeItem'] = this.freeItem;
    if (this.orderDiscountItem != null) {
      data['orderDiscountItem'] = this.orderDiscountItem!.toJson();
    }
    if (this.orderPromoCodeItem != null) {
      data['orderPromoCodeItem'] = this.orderPromoCodeItem!.toJson();
    }
    if (this.orderRewardItem != null) {
      data['orderRewardItem'] = this.orderRewardItem!.toJson();
    }

    if (this.orderCustomDiscountItem != null) {
      data['orderCustomDiscountItem'] =
          this.orderCustomDiscountItem!.map((v) => v.toJson()).toList();
    }
    if (this.orderItemLots != null) {
      data['orderItemLots'] =
          this.orderItemLots!.map((v) => v.toJson()).toList();
    }
    if (this.orderItemTax != null) {
      data['orderItemTax'] = this.orderItemTax!.map((v) => v.toJson()).toList();
    }
    data['oldLotNameValue'] = this.oldLotNameValue;
    return data;
  }
}



class OrderPromotionItem {
  num? id;
  num? orderDiscountId;
  num? orderCustomDiscountId;
  num? orderRewardId;
  num? orderPromocodeId;
  num? orderId;
  num? orderItemId;
  num? productId;
  String? sku;
  num? unit;
  num? discount;
  num? appliedValue;
  String? applyOn;
  num? value;
  String? valueType;

  OrderPromotionItem({
    this.id,
    this.orderRewardId,
    this.orderDiscountId,
    this.orderPromocodeId,
    this.orderId,
    this.orderItemId,
    this.productId,
    this.sku,
    this.unit,
    this.discount,
    this.applyOn,
    this.appliedValue,
    this.orderCustomDiscountId,
    this.value,
    this.valueType,
  });

  factory OrderPromotionItem.deepCopy(OrderPromotionItem orderPromotionItemCopy) =>  OrderPromotionItem(
    id : orderPromotionItemCopy.id,
    orderRewardId: orderPromotionItemCopy.orderRewardId,
    orderDiscountId : orderPromotionItemCopy.orderDiscountId,
    orderPromocodeId : orderPromotionItemCopy.orderPromocodeId,
    orderId : orderPromotionItemCopy.orderId,
    orderItemId : orderPromotionItemCopy.orderItemId,
    productId : orderPromotionItemCopy.productId,
    sku : orderPromotionItemCopy.sku,
    unit: orderPromotionItemCopy.unit,
    discount : orderPromotionItemCopy.discount,
    applyOn : orderPromotionItemCopy.applyOn,
    appliedValue : orderPromotionItemCopy.appliedValue,
    orderCustomDiscountId : orderPromotionItemCopy.orderCustomDiscountId,
    value : orderPromotionItemCopy.value,
    valueType : orderPromotionItemCopy.valueType,
  );

  OrderPromotionItem.fromJson(Map<String, dynamic> json) {
    try {
      id = json['id'] ?? 0;
      orderDiscountId = json['orderDiscountId'] ?? 0;
      orderRewardId = json['orderRewardId'] ?? 0;
      orderPromocodeId = json['orderPromocodeId'] ?? 0;
      orderCustomDiscountId = json['orderCustomDiscountId']??0;
      appliedValue = json['appliedValue'] ??0;
      applyOn = json['applyOn'];
      value = json['value'] ??0;
      valueType = json['valueType'];
      orderId = json['orderId'] ?? 0;
      orderItemId = json['orderItemId'] ?? 0;
      productId = json['productId'] ?? 0;
      sku = json['sku'] ?? 0;
      unit = json['unit'] ?? 0;
      discount = json['discount'] ?? 0;
    } catch (e, stacktrace) {
      print('OrderPromotionItem response model class ${stacktrace.toString()}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['orderDiscountId'] = this.orderDiscountId;
    data['orderRewardId'] = this.orderRewardId;
    data['orderPromocodeId'] = this.orderPromocodeId;
    data['orderCustomDiscountId'] = this.orderCustomDiscountId;
    data['orderId'] = this.orderId;
    data['orderItemId'] = this.orderItemId;
    data['productId'] = this.productId;
    data['appliedValue'] = this.appliedValue;
    data['applyOn'] = this.applyOn;
    data['value'] = this.value;
    data['valueType'] = this.valueType;
    data['sku'] = this.sku;
    data['unit'] = this.unit;
    data['discount'] = this.discount;
    return data;
  }
}

class OrderItemLots {
  int? id;
  int? orderItemId;
  int? lotId;
  int? inventoryCount;
  int? unit;
  bool? isOld;
  bool? isScanned;
  String? lotName;
  String? createdBy;
  String? updatedBy;
  String? deletedAt;
  String? createdAt;
  String? updatedAt;

  OrderItemLots(
      {this.id,
        this.orderItemId,
        this.lotId,
        this.inventoryCount,
        this.unit,
        this.lotName,
        this.isOld,
        this.isScanned,
        this.createdBy,
        this.updatedBy,
        this.deletedAt,
        this.createdAt,
        this.updatedAt});

  factory OrderItemLots.deepCopy(OrderItemLots copiedLots) =>  new OrderItemLots(
    id: copiedLots.id,
    orderItemId: copiedLots.orderItemId,
    lotId :copiedLots.lotId,
    inventoryCount:copiedLots.inventoryCount,
    unit:copiedLots.unit,
    lotName:copiedLots.lotName,
    isOld:copiedLots.isOld,
    isScanned:copiedLots.isScanned,
    createdBy:copiedLots.createdBy,
    updatedBy:copiedLots.updatedBy,
    deletedAt:copiedLots.deletedAt,
    createdAt:copiedLots.createdAt,
    updatedAt :copiedLots.updatedAt,
  );

  OrderItemLots.fromJson(Map<String, dynamic> json) {
    try {
      id = json['id'];
      orderItemId = json['orderItemId'];
      lotId = json['lotId'];
      inventoryCount = json['inventoryCount'];
      unit = json['unit'];
      lotName = json['lotName'];
      isOld = json['isOld'] ?? false;
      isScanned = json['isScanned'] ?? false;
      createdBy = json['createdBy'];
      updatedBy = json['updatedBy'];
      deletedAt = json['deletedAt'];
      createdAt = json['createdAt'];
      updatedAt = json['updatedAt'];
    } catch (e, stacktrace) {
      print('OrderItemLots response model class ${stacktrace.toString()}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['orderItemId'] = this.orderItemId;
    data['lotId'] = this.lotId;
    data['inventoryCount'] = this.inventoryCount;
    data['unit'] = this.unit;
    data['lotName'] = this.lotName;
    data['isOld'] = this.isOld;
    data['isScanned'] = this.isScanned;
    data['createdBy'] = this.createdBy;
    data['updatedBy'] = this.updatedBy;
    data['deletedAt'] = this.deletedAt;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}

class OrderItemTax {
  int? id;
  int? orderId;
  int? orderItemId;
  num? taxId;
  num? taxAmount;
  String? name;
  String? code;
  String? taxType;
  num? taxRate;
  String? taxRateType;
  bool? status;
  String? createdAt;
  String? updatedAt;

  OrderItemTax(
      {this.id,
        this.orderId,
        this.orderItemId,
        this.taxId,
        this.taxAmount,
        this.name,
        this.code,
        this.taxType,
        this.taxRate,
        this.taxRateType,
        this.status,
        this.createdAt,
        this.updatedAt});

  factory OrderItemTax.deepCopy(OrderItemTax orderItemTaxCopy) =>  OrderItemTax(
      id:orderItemTaxCopy.id,
      orderId:orderItemTaxCopy.orderId,
      orderItemId:orderItemTaxCopy.orderItemId,
      taxId:orderItemTaxCopy.taxId,
      taxAmount:orderItemTaxCopy.taxAmount,
      name:orderItemTaxCopy.name,
      code:orderItemTaxCopy.code,
      taxType:orderItemTaxCopy.taxType,
      taxRate:orderItemTaxCopy.taxRate,
      taxRateType:orderItemTaxCopy.taxRateType,
      status:orderItemTaxCopy.status,
      createdAt:orderItemTaxCopy.createdAt,
      updatedAt:orderItemTaxCopy.updatedAt
  );

  OrderItemTax.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderId = json['orderId'];
    orderItemId = json['orderItemId'];
    taxId = json['taxId'];
    taxAmount = json['taxAmount'];
    name = json['name'];
    code = json['code'];
    taxType = json['taxType'];
    taxRate = json['taxRate'];
    taxRateType = json['taxRateType'];
    status = json['status'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['orderId'] = this.orderId;
    data['orderItemId'] = this.orderItemId;
    data['taxId'] = this.taxId;
    data['taxAmount'] = this.taxAmount;
    data['name'] = this.name;
    data['code'] = this.code;
    data['taxType'] = this.taxType;
    data['taxRate'] = this.taxRate;
    data['taxRateType'] = this.taxRateType;
    data['status'] = this.status;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}
