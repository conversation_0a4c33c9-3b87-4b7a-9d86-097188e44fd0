class CartCustomDiscount {
  num? id;
  String? userId;
  String? userType;
  String? note;
  num? value;
  String? valueType;
  num? appliedValue;
  String? applyOn;
  String? addedById;
  String? addedByName;
  bool? approved;

  CartCustomDiscount(
      {this.id,
        this.userId,
        this.userType,
        this.note,
        this.value,
        this.valueType,
        this.appliedValue,
        this.applyOn,
        this.addedById,
        this.addedByName,
        this.approved});

  CartCustomDiscount.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    userType = json['userType'];
    note = json['note'];
    value = json['value'];
    valueType = json['valueType'];
    appliedValue = json['appliedValue'];
    applyOn = json['applyOn'];
    addedById = json['addedById'];
    addedByName = json['addedByName'];
    approved = json['approved'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  {};
    data['id'] = id;
    data['userId'] = userId;
    data['userType'] = userType;
    data['note'] = note;
    data['value'] = value;
    data['valueType'] = valueType;
    data['appliedValue'] = appliedValue;
    data['applyOn'] = applyOn;
    data['addedById'] = addedById;
    data['addedByName'] = addedByName;
    data['approved'] = approved;
    return data;
  }
}