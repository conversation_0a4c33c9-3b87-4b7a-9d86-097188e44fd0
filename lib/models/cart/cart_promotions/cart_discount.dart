class CartDiscount {
  num? id;
  num? cartId;
  String? userId;
  String? userType;
  num? discountId;
  String? description;
  num? value;
  String? valueType;
  num? appliedValue;
  String? applyOn;
  String? title;
  bool? approved;

  CartDiscount(
      {this.id,
        this.cartId,
        this.userId,
        this.userType,
        this.discountId,
        this.description,
        this.value,
        this.valueType,
        this.appliedValue,
        this.applyOn,
        this.title,
        this.approved,
      });

  CartDiscount.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    cartId = json['cartId']??0;
    userId = json['userId']??'';
    userType = json['userType']??'';
    discountId = json['discountId']??0;
    description = json['description'] ??'';
    value = json['value']??0;
    valueType = json['valueType']??'';
    appliedValue = json['appliedValue'] ??0;
    applyOn = json['applyOn']??'';
    title = json['title'] ?? '';
    approved = json['approved'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  {};
    data['id'] = id;
    data['cartId'] = cartId;
    data['userId'] = userId;
    data['userType'] = userType;
    data['discountId'] = discountId;
    data['description'] = description;
    data['value'] = value;
    data['valueType'] = valueType;
    data['appliedValue'] = appliedValue;
    data['applyOn'] = applyOn;
    data['title'] = title;
    data['approved'] = approved;
    return data;
  }
}