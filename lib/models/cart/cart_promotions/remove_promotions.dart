class RemovePromotionsResponse {
  String? message;
  dynamic payload;

  RemovePromotionsResponse({this.message, this.payload});

  RemovePromotionsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  json['payload'] : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.toJson();
    }
    return data;
  }
}