class CartReward {
  num? id;
  num? cartId;
  String? rewardType;
  num? rewardId;
  num? programTierId;
  String? name;
  num? points;
  num? value;
  String? txnKey;
  String? programTierName;
  num? rewardAmount;
  String? createdBy;
  String? updatedBy;
  String? platform;

  CartReward(
      {this.id,
        this.cartId,
        this.rewardType,
        this.rewardId,
        this.programTierId,
        this.name,
        this.points,
        this.value,
        this.txnKey,
        this.programTierName,
        this.rewardAmount,
        this.createdBy,
        this.updatedBy,
        this.platform,
        });

  CartReward.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    cartId = json['cartId'] ??0;
    rewardType = json['rewardType'] ??'';
    rewardId = json['rewardId'] ??0;
    name = json['name'] ??'';
    points = json['points'] ??0;
    value = json['value'] ??0;
    txnKey = json['txnKey'] ??'';
    programTierName = json['programTierName'] ??'';
    rewardAmount = json['rewardAmount'] ??0;
    programTierId = json['programTierId'] ??0;
    createdBy = json['createdBy'] ??'';
    updatedBy = json['updatedBy'] ??'';
    platform = json['platform'] ??'';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['cartId'] = cartId;
    data['rewardType'] = rewardType;
    data['rewardId'] = rewardId;
    data['name'] = name;
    data['points'] = points;
    data['value'] = value;
    data['txnKey'] = txnKey;
    data['programTierName'] = programTierName;
    data['rewardAmount'] = rewardAmount;
    data['programTierId'] = programTierId;
    data['createdBy'] = createdBy;
    data['updatedBy'] = updatedBy;
    data['platform'] = platform;
    return data;
  }
}