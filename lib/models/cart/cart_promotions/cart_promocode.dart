class CartPromoCode {
  num? id;
  num? cartId;
  String? userId;
  String? userType;
  num? promocodeId;
  String? code;
  String? description;
  num? value;
  String? valueType;
  num? appliedValue;
  String? applyOn;


  CartPromoCode(
      {this.id,
        this.cartId,
        this.userId,
        this.userType,
        this.promocodeId,
        this.code,
        this.description,
        this.value,
        this.valueType,
        this.appliedValue,
        this.applyOn,
      });

  CartPromoCode.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    cartId = json['cartId'];
    userId = json['userId'];
    userType = json['userType'];
    promocodeId = json['promocodeId'];
    code = json['code'] ?? '';
    description = json['description'];
    value = json['value'] ?? 0;
    valueType = json['valueType'];
    appliedValue = json['appliedValue'] ?? 0;
    applyOn = json['applyOn'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  {};
    data['id'] = id;
    data['cartId'] = cartId;
    data['userId'] = userId;
    data['userType'] = userType;
    data['promocodeId'] = promocodeId;
    data['code'] = code;
    data['description'] = description;
    data['value'] = value;
    data['valueType'] = valueType;
    data['appliedValue'] = appliedValue;
    data['applyOn'] = applyOn;

    return data;
  }
}