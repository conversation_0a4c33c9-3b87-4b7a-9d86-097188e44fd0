class CartLoyalty {
  num? id;
  num? pointsEarned;
  num? pointsRedeemed;
  num? rewardAmount;
  num ? pointsRefunded;

  CartLoyalty(
      {this.id,
        this.pointsEarned,
        this.pointsRedeemed,
        this.rewardAmount,
        this.pointsRefunded});

  CartLoyalty.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    pointsEarned = json['pointsEarned'] ?? 0;
    pointsRedeemed = json['pointsRedeemed'] ?? 0;
    rewardAmount = json['rewardAmount'] ?? 0;
    pointsRefunded = json['pointsRefunded']??0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['pointsEarned'] = pointsEarned;
    data['pointsRedeemed'] = pointsRedeemed;
    data['rewardAmount'] = rewardAmount;
    data['pointsRefunded'] = pointsRefunded;
    return data;
  }
}