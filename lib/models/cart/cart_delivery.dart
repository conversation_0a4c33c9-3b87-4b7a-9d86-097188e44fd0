class CartDelivery {
  int? id;
  String? address1;
  String? address2;
  String? postalCode;
  String? city;
  String? province;
  String? latitude;
  String? longitude;
  String? note;
  String? phone;

  CartDelivery(
      {this.id,
        this.address1,
        this.address2,
        this.postalCode,
        this.city,
        this.province,
        this.latitude,
        this.longitude,
        this.note,
        this.phone});

  CartDelivery.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    address1 = json['address1'] ??'';
    address2 = json['address2']??'';
    postalCode = json['postalCode']??'';
    city = json['city']??'';
    province = json['province']??'';
    latitude = json['latitude']??'';
    longitude = json['longitude']??'';
    note = json['note']??'';
    phone = json['phone']??'';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['address1'] = address1;
    data['address2'] = address2;
    data['postalCode'] = postalCode;
    data['city'] = city;
    data['province'] = province;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['note'] = note;
    data['phone'] = phone;
    return data;
  }
}