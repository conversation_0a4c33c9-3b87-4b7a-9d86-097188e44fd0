import 'cart_data.dart';

class CreateCartItemResponse {
  String? message;
  CartData? payload;

  CreateCartItemResponse({this.message, this.payload});

  CreateCartItemResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  CartData.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = message;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    return data;
  }
}