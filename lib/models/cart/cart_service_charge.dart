class CartServiceCharge{
  int? id;
  num? value;
  String? type;
  num? amount;
  String? addedBy;

  CartServiceCharge(
      {this.id, this.value, this.type, this.amount, this.addedBy});

  CartServiceCharge.fromJson(Map<String, dynamic> json) {
    id = json['id']??0;
    value = json['value']??0;
    type = json['type']??'';
    amount = json['amount']??0;
    addedBy = json['addedBy']??'';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['value'] = value;
    data['type'] = type;
    data['amount'] = amount;
    data['addedBy'] = addedBy;
    return data;
  }
}