
import 'package:gr8tables_server_manager/models/menu/menu_data.dart';

import '../../utils/app_utils.dart';

class CreateCartItemObject {
  int? cartId;
  int? id;
  int? categoryId;
  String? category;
  String? itemSku;
  String? name;
  String? imageUrl;
  int? itemId;
  int? itemQty;
  num? basePrice;
  num? actualPrice;
  bool? userDefinedCustomItem;
  int? course;
  bool? sentToKitchen;
  String? userId;
  String? userName;
  String? note;
  String? addedBy;
  String? addedById;
  List<ItemModifiers>? cartItemModifiers;
  num? lineTotal;
  num? cost;
  CartUser? cartUser;
  int? cartUserId;

  CreateCartItemObject({this.cartId,
    this.id,
    this.categoryId,
    this.category,
    this.itemSku,
    this.name,
    this.imageUrl,
    this.itemId,
    this.itemQty,
    this.basePrice,
    this.actualPrice,
    this.userDefinedCustomItem,
    this.course,
    this.sentTo<PERSON>itchen,
    this.userId,
    this.userName,
    this.note,
    this.cartItemModifiers,
    this.lineTotal , this.addedBy , this.addedById,
    this.cost,
    this.cartUser,
    this.cartUserId,
  });

  CreateCartItemObject.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    categoryId = json['categoryId'] ?? 0;
    category = json['category'] ?? '';
    itemSku = json['itemSku'] ?? '';
    name = json['name'] ?? '';
    imageUrl = json['imageUrl'] ?? '';
    itemId = json['itemId'] ?? 0;
    itemQty = json['itemQty'] ?? 0;
    basePrice = json['basePrice'] ?? 0;
    actualPrice = json['actualPrice'] ?? 0;
    userDefinedCustomItem = json['userDefinedCustomItem'] ?? false;
    course = json['course'] ?? 0;
    sentToKitchen = json['sentToKitchen'] ?? false;
    userId = json['userId'] ?? '';
    userName = json['userName'] ?? '';
    note = json['note'] ?? '';
    addedBy = json['addedBy'] ?? '';
    addedById = json['addedById'] ?? '';
    lineTotal = roundDouble(actualPrice! * itemQty!);
    if (json['cartItemModifiers'] != null) {
      cartItemModifiers = <ItemModifiers>[];
      json['cartItemModifiers'].forEach((v) {
        cartItemModifiers!.add(ItemModifiers.fromJson(v));
      });
    } else {
      cartItemModifiers = <ItemModifiers>[];
    }
    cost = json['cost']??0;
    cartUser = json['cartUser'] != null
        ?  CartUser.fromJson(json['cartUser'])
        : null;
    cartUserId = json['cartUserId'] ??0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if(data['id'] != null) {
      data['id'] = id;
    }
    data['categoryId'] = categoryId;
    data['category'] = category;
    data['itemSku'] = itemSku;
    data['name'] = name;
    data['imageUrl'] = imageUrl;
    data['itemId'] = itemId;
    data['itemQty'] = itemQty;
    data['basePrice'] = basePrice;
    data['actualPrice'] = actualPrice;
    data['userDefinedCustomItem'] = userDefinedCustomItem;
    data['course'] = course;
    data['sentToKitchen'] = sentToKitchen;
    data['userId'] = userId;
    data['userName'] = userName;
    data['note'] = note;
    data['addedBy'] = addedBy;
    data['addedById'] = addedById;
    data['cartId'] = cartId;
    if (cartItemModifiers != null) {
      data['cartItemModifiers'] =
          cartItemModifiers!.map((v) => v.toJson()).toList();
    }
    data['cost'] = cost;
    if (cartUser != null) {
      data['cartUser'] = cartUser!.toJson();
    }
    if(cartUserId!= null){
      data['cartUserId'] = cartUserId;
    }
    return data;
  }

}

class CartUser {
  String? userId;
  String? name;
  String? email;
  String? phone;

  CartUser({this.userId, this.name, this.email, this.phone});

  CartUser.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if(userId != null) {
      data['userId'] = userId;
    }
    if(name != null) {
      data['name'] = name;
    }
    if(email != null) {
      data['email'] = email;
    }
    if(phone != null) {
      data['phone'] = phone;
    }
    return data;
  }
}


