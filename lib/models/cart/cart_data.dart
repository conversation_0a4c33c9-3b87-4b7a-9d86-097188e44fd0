
import '../dinein_takeout/table_data.dart';
import '../menu/menu_data.dart';
import 'cart_delivery.dart';
import 'cart_items/cart_item_promotions.dart';
import 'cart_payments.dart';
import 'cart_promotions/cart_custom_discount.dart';
import 'cart_promotions/cart_discount.dart';
import 'cart_promotions/cart_loyalty.dart';
import 'cart_promotions/cart_promocode.dart';
import 'cart_promotions/cart_reward.dart';
import 'cart_service_charge.dart';
import 'cart_taxes.dart';
import 'cart_user.dart';
import 'cart_items/cart_items.dart';
import 'cart_users/cart_user_data.dart';

class CartData {
  int? id;
  num? parentOrderId;
  String? locationId;
  String? businessId;
  String? userId;
  CartUser? user;
  int? tableId;
  String? addedBy;
  String? addedById;
  String? orderReceiveMethod;
  bool? receiveLater;
  num? subtotal;
  num? deliveryFee;
  num? tax;
  num? tip;
  num? total;
  num? changeAmount;
  bool? payLater;
  bool? paymentReceived;
  String? platform;
  String? status;
  String? closedById;
  String? closedByName;
  int? posStationId;
  int? shiftId;
  String? customerNote;
  String? serverNote;
  String? receiveTime;
  String? createdAt;
  String? updatedAt;
  String? closedAt;
  int? itemCount;
  List<CartItems>? cartItems;
  List<CartCustomDiscount>? cartCustomDiscount;
  num? dueAmount;
  num? paidAmount;
  List<CartPayments>? cartPayments;
  TableData? table;
  String? submittedAt;
  String? addedByName;
  CartDelivery? cartDelivery;
  CartServiceCharge? cartServiceCharge;
  CartServiceCharge? cartTip;
  CartDiscount? cartDiscount;
  List<CartTaxes>? cartTaxes;
  CartPromoCode? cartPromoCode;
  CartLoyalty? cartLoyalty;
  num? userPointBalance;
  CartReward? cartReward;
  List<CartUserData>? cartUsers;
  int? usersCount;
  String? locationName;
  bool isExpanded = false;
  bool? isSelected = false;


  CartData(
      {this.id,
        this.parentOrderId,
        this.locationId,
        this.businessId,
        this.userId,
        this.user,
        this.tableId,
        this.addedBy,
        this.addedById,
        this.orderReceiveMethod,
        this.receiveLater,
        this.subtotal,
        this.deliveryFee,
        this.tax,
        this.tip,
        this.total,
        this.changeAmount,
        this.payLater,
        this.paymentReceived,
        this.platform,
        this.status,
        this.closedById,
        this.closedByName,
        this.posStationId,
        this.shiftId,
        this.customerNote,
        this.serverNote,
        this.receiveTime,
        this.createdAt,
        this.updatedAt,
        this.closedAt,
        this.itemCount,
        this.cartItems,
        this.cartCustomDiscount,
        this.dueAmount,
        this.paidAmount,
        this.cartPayments,
        this.table,
        this.submittedAt,
        this.addedByName,
        this.cartDelivery,
        this.cartServiceCharge,
        this.cartTip,
        this.cartDiscount,
        this.cartTaxes,
        this.cartPromoCode,
        this.cartLoyalty,
        this.userPointBalance,
        this.cartReward,
        this.cartUsers,
        this.usersCount,
        this.locationName,
        this.isExpanded =false,
        this.isSelected,
      });

  CartData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    parentOrderId = json['parentOrderId'] ??0;
    locationId = json['locationId']??'';
    businessId = json['businessId']??'';
    userId = json['userId']??'';
    user = json['user'] != null ?  CartUser.fromJson(json['user']) : null;
    tableId = json['tableId']??0;
    addedBy = json['addedBy']??'';
    addedById = json['addedById']??'';
    orderReceiveMethod = json['orderReceiveMethod']??'';
    receiveLater = json['receiveLater']?? false;
    subtotal = json['subtotal'] ??0;
    deliveryFee = json['deliveryFee'] ??0;
    tax = json['tax']??0;
    tip = json['tip']??0;
    total = json['total']??0;
    changeAmount = json['changeAmount']??0;
    payLater = json['payLater']??false;
    paymentReceived = json['paymentReceived']?? false;
    platform = json['platform']??'';
    status = json['status']??'';
    closedById = json['closedById']??'';
    closedByName = json['closedByName']??'';
    posStationId = json['posStationId']??0;
    shiftId = json['shiftId']??0;
    customerNote = json['customerNote'] ??'';
    serverNote = json['serverNote']??'';
    receiveTime = json['receiveTime']??'';
    createdAt = json['createdAt']??'';
    updatedAt = json['updatedAt']??'';
    closedAt = json['closedAt']??'';
    itemCount = json['itemCount']??0;
    usersCount = json['usersCount']??0;
    locationName = json['locationName'] ?? '';
    if (json['cartItems'] != null) {
      cartItems = <CartItems>[];
      json['cartItems'].forEach((v) {
        cartItems!.add( CartItems.fromJson(v));
      });
    } else {
      cartItems = <CartItems>[];
    }
    if (json['cartCustomDiscount'] != null) {
      cartCustomDiscount = <CartCustomDiscount>[];
      json['cartCustomDiscount'].forEach((v) {
        cartCustomDiscount!.add(CartCustomDiscount.fromJson(v));
      });
    } else {
      cartCustomDiscount = <CartCustomDiscount>[];
    }
    dueAmount = json['dueAmount']??0;
    paidAmount = json['paidAmount']??0;
    if (json['cartPayments'] != null) {
      cartPayments = <CartPayments>[];
      json['cartPayments'].forEach((v) {
        cartPayments!.add(CartPayments.fromJson(v));
      });
    } else {
      cartPayments = <CartPayments>[];
    }
    table = json['table'] != null ? TableData.fromJson(json['table']) : null;
    submittedAt = json['submittedAt']??'';
    addedByName = json['addedByName']??'';
    cartDelivery = json['cartDelivery'] != null
        ? CartDelivery.fromJson(json['cartDelivery'])
        : null;
    cartServiceCharge = json['cartServiceCharge'] != null
        ?  CartServiceCharge.fromJson(json['cartServiceCharge'])
        : null;
    cartTip = json['cartTip'] != null
        ?  CartServiceCharge.fromJson(json['cartTip'])
        : null;
    cartDiscount = json['cartDiscount'] != null
        ?  CartDiscount.fromJson(json['cartDiscount']) : null;
    if (json['cartTaxes'] != null) {
      cartTaxes = <CartTaxes>[];
      json['cartTaxes'].forEach((v) {
        cartTaxes!.add(CartTaxes.fromJson(v));
      });
    }else {
      cartTaxes = <CartTaxes>[];
    }

    cartPromoCode = json['cartPromoCode'] != null
        ?  CartPromoCode.fromJson(json['cartPromoCode']) : null;

    cartLoyalty = json['cartLoyalty'] != null
        ? CartLoyalty.fromJson(json['cartLoyalty'])
        : null;
    userPointBalance = json['userPointBalance']??0;

    cartReward = json['cartReward'] != null
        ? CartReward.fromJson(json['cartReward'])
        : null;
    if (json['cartUsers'] != null) {
      cartUsers = <CartUserData>[];
      json['cartUsers'].forEach((v) {
        cartUsers!.add(CartUserData.fromJson(v));
      });
    }else {
      cartUsers = <CartUserData>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] =id;
    data['parentOrderId'] = parentOrderId;
    data['locationId'] = locationId;
    data['businessId'] = businessId;
    data['userId'] = userId;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['tableId'] = tableId;
    data['addedBy'] = addedBy;
    data['addedById'] = addedById;
    data['orderReceiveMethod'] = orderReceiveMethod;
    data['receiveLater'] = receiveLater;
    data['subtotal'] = subtotal;
    data['deliveryFee'] = deliveryFee;
    data['tax'] = tax;
    data['tip'] = tip;
    data['total'] = total;
    data['changeAmount'] = changeAmount;
    data['payLater'] = payLater;
    data['paymentReceived'] = paymentReceived;
    data['platform'] = platform;
    data['status'] = status;
    data['closedById'] = closedById;
    data['closedByName'] = closedByName;
    data['posStationId'] = posStationId;
    data['shiftId'] = shiftId;
    data['customerNote'] = customerNote;
    data['serverNote'] = serverNote;
    data['receiveTime'] = receiveTime;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['closedAt'] = closedAt;
    data['itemCount'] = itemCount;
    data['usersCount'] = usersCount;
    data['locationName'] = this.locationName;
    if (cartItems != null) {
      data['cartItems'] = cartItems!.map((v) => v.toJson()).toList();
    }
    if (cartCustomDiscount != null) {
      data['cartCustomDiscount'] =
          cartCustomDiscount!.map((v) => v.toJson()).toList();
    }
    data['dueAmount'] = dueAmount;
    data['paidAmount'] = paidAmount;
    if (cartPayments != null) {
      data['cartPayments'] =
          cartPayments!.map((v) => v.toJson()).toList();
    }
    if (table != null) {
      data['table'] = table!.toJson();
    }
    data['submittedAt'] = submittedAt;
    data['addedByName'] = addedByName;

    if (cartDelivery != null) {
      data['cartDelivery'] = cartDelivery!.toJson();
    }

    if (cartServiceCharge != null) {
      data['cartServiceCharge'] = cartServiceCharge!.toJson();
    }
    if (cartTip != null) {
      data['cartTip'] = cartTip!.toJson();
    }
    if (cartDiscount != null) {
      data['cartDiscount'] = cartDiscount!.toJson();
    }

    if (cartTaxes != null) {
      data['cartTaxes'] = cartTaxes!.map((v) => v.toJson()).toList();
    }

    if (cartPromoCode != null) {
      data['cartPromoCode'] = cartPromoCode!.toJson();
    }

    if (cartLoyalty != null) {
      data['cartLoyalty'] = cartLoyalty!.toJson();
    }
    data['userPointBalance'] = userPointBalance;

    if (cartReward != null) {
      data['cartReward'] = cartReward!.toJson();
    }

    if (cartUsers != null) {
      data['cartUsers'] = cartUsers!.map((v) => v.toJson()).toList();
    }
    return data;
  }


}