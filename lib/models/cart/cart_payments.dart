class CartPayments{
  int? id;
  num? total;
  String? transactionType;
  String? paymentType;
  String? status;
  String? txnNumber;
  String? cardNumber;
  num? refundedAmount;
  String? cardType;
  String? processor;
  String? updatedAt;
  // OrderPaymentResponse? response;

  CartPayments({
    this.id,
    this.total,
    this.transactionType,
    this.paymentType,
    this.status,
    this.txnNumber,
    this.cardNumber,
    this.cardType,
    this.refundedAmount,
    this.updatedAt,
    this.processor,
    // this.response,
  });
  factory CartPayments.deepCopy(CartPayments orderPayments)=> CartPayments(
    id : orderPayments.id,
    total: orderPayments.total,
    transactionType: orderPayments.transactionType,
    paymentType: orderPayments.paymentType,
    status: orderPayments.status,
    txnNumber: orderPayments.txnNumber,
    cardNumber: orderPayments.cardNumber,
    cardType: orderPayments.cardType,
    refundedAmount: orderPayments.refundedAmount,
    processor: orderPayments.processor,
    updatedAt: orderPayments.updatedAt,
    // response: orderPayments.response,
  );

  CartPayments.fromJson(Map<String, dynamic> json) {

    id = json['id'];
    total = json['total'] ?? 0;
    refundedAmount = json['refundedAmount'] ?? 0;
    transactionType = json['transactionType'];
    paymentType = json['paymentType']??'';
    status = json['status']??'';
    txnNumber = json['txnNumber']??'';
    cardNumber = json['cardNumber'] ?? 'N/A';
    cardType = json['cardType'] ?? 'N/A';
    updatedAt = json['updatedAt'] ?? '';
    processor = json['processor'] ?? '';
    // response = json['response'] != null
    //     ? OrderPaymentResponse.fromJson(json['response'])
    //     : null;

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['total'] = total;
    data['refundedAmount'] = refundedAmount;
    data['transactionType'] = transactionType;
    data['paymentType'] = paymentType;
    data['status'] = status;
    data['txnNumber'] = txnNumber;
    data['cardNumber'] = cardNumber;
    data['cardType'] = cardType;
    data['updatedAt'] = updatedAt;
    data['processor'] = processor;
    // if (response != null) {
    //   data['response'] = response!.toJson();
    // }
    return data;
  }
}