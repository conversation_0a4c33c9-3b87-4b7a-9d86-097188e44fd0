import 'cart_data.dart';

class GetCartDataResponse {
  String? message;
  CartData? payload;

  GetCartDataResponse({this.message, this.payload});

  GetCartDataResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  CartData.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = message;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    return data;
  }
}