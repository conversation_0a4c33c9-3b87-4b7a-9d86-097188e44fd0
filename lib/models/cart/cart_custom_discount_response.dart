
import 'cart_promotions/cart_custom_discount.dart';

class CartCustomDiscountResponse {
  String? message;
  List<CartCustomDiscountPayload>? payload;

  CartCustomDiscountResponse({this.message, this.payload});

  CartCustomDiscountResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <CartCustomDiscountPayload>[];
      // json['payload'].forEach((key, v) {
      //   payload!.add(CartCustomDiscountPayload.fromJson(v));
      // });
    }else {
      payload = <CartCustomDiscountPayload>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CartCustomDiscountPayload {
  int? id;
  int? orderId;
  String? userId;
  String? userType;
  String? note;
  num? value;
  String? valueType;
  num? appliedValue;
  String? applyOn;
  String? addedById;
  String? addedByName;
  List<CartCustomDiscount>? cartCustomDiscountItem;

  CartCustomDiscountPayload(
      {this.id,
        this.orderId,
        this.userId,
        this.userType,
        this.note,
        this.value,
        this.valueType,
        this.appliedValue,
        this.applyOn,
        this.addedById,
        this.addedByName,
        this.cartCustomDiscountItem});

  CartCustomDiscountPayload.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderId = json['orderId'];
    userId = json['userId'];
    userType = json['userType'];
    note = json['note'];
    value = json['value'] ??0;
    valueType = json['valueType'];
    appliedValue = json['appliedValue'] ??0;
    applyOn = json['applyOn'];
    addedById = json['addedById'];
    addedByName = json['addedByName'];
    if (json['cartCustomDiscountItem'] != null) {
      cartCustomDiscountItem = <CartCustomDiscount>[];
      json['cartCustomDiscountItem'].forEach((v) {
        cartCustomDiscountItem!.add( CartCustomDiscount.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  {};
    data['id'] = this.id;
    data['orderId'] = this.orderId;
    data['userId'] = this.userId;
    data['userType'] = this.userType;
    data['note'] = this.note;
    data['value'] = this.value;
    data['valueType'] = this.valueType;
    data['appliedValue'] = this.appliedValue;
    data['applyOn'] = this.applyOn;
    data['addedById'] = this.addedById;
    data['addedByName'] = this.addedByName;
    if (this.cartCustomDiscountItem != null) {
      data['cartCustomDiscountItem'] =
          this.cartCustomDiscountItem!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
