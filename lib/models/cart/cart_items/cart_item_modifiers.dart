class CartItemModifiers {
  int? id;
  int? cartItemId;
  String? name;
  int? modifierId;
  String? sku;
  num? basePrice;
  int? included;
  int? order;
  int? min;
  int? max;
  List<CartItemModifierItems>? cartItemModifierItems;

  CartItemModifiers(
      {this.id,
        this.cartItemId,
        this.name,
        this.modifierId,
        this.sku,
        this.basePrice,
        this.included,
        this.order,
        this.min,
        this.max,
        this.cartItemModifierItems});

  CartItemModifiers.fromJson(Map<String, dynamic> json) {
    id = json['id']??0;
    cartItemId = json['cartItemId'] ??0;
    name = json['name']??'';
    modifierId = json['modifierId'] ??0;
    sku = json['sku']??'';
    basePrice = json['basePrice'] ??0;
    included = json['included']??0;
    order = json['order']??0;
    min = json['min']??0;
    max = json['max']??0;
    if (json['cartItemModifierItems'] != null) {
      cartItemModifierItems = <CartItemModifierItems>[];
      json['cartItemModifierItems'].forEach((v) {
        cartItemModifierItems!.add(CartItemModifierItems.fromJson(v));
      });
    } else{
      cartItemModifierItems = <CartItemModifierItems>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if(id != null) {
      data['id'] = id;
    }
    if(cartItemId != null) {
      data['cartItemId'] = cartItemId;
    }
    data['name'] = name;
    data['modifierId'] = modifierId;
    data['sku'] = sku;
    data['basePrice'] = basePrice;
    data['included'] = included;
    data['order'] = order;
    data['min'] = min;
    data['max'] = max;
    if (cartItemModifierItems != null) {
      data['cartItemModifierItems'] =
          cartItemModifierItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CartItemModifierItems {
  int? id;
  int? cartItemModifierId;
  String? name;
  int? modifierItemId;
  String? sku;
  num? price;
  int? included;
  int? order;
  int? min;
  int? max;
  int? qty;


  CartItemModifierItems(
      {this.id,
        this.cartItemModifierId,
        this.name,
        this.modifierItemId,
        this.sku,
        this.price,
        this.included,
        this.order,
        this.min,
        this.max,
        this.qty,
      });

  CartItemModifierItems.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    cartItemModifierId = json['cartItemModifierId'] ??0;
    name = json['name']??'';
    modifierItemId = json['modifierItemId'] ??0;
    sku = json['sku']??'';
    price = json['price']??0;
    included = json['included']??0;
    order = json['order']??0;
    min = json['min']??0;
    max = json['max']??0;
    qty = json['qty']??0;

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if(id != null) {
      data['id'] = id;
    }
    if(cartItemModifierId != null) {
      data['cartItemModifierId'] = cartItemModifierId;
    }
    data['name'] = name;
    data['modifierItemId'] = modifierItemId;
    data['sku'] = sku;
    data['price'] = price;
    data['included'] = included;
    data['order'] = order;
    data['min'] = min;
    data['max'] = max;
    data['qty'] = qty;

    return data;
  }
}