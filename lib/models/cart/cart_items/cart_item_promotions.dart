class CartPromotionItem {
  num? id;
  num? cartDiscountId;
  num? cartCustomDiscountId;
  num? cartRewardId;
  num? cartPromocodeId;
  num? cartId;
  num? cartItemId;
  num? itemId;
  String? sku;
  num? qty;
  num? discount;
  num? appliedValue;
  String? applyOn;
  num? value;
  String? valueType;

  CartPromotionItem({
    this.id,
    this.cartRewardId,
    this.cartDiscountId,
    this.cartPromocodeId,
    this.cartId,
    this.cartItemId,
    this.itemId,
    this.sku,
    this.qty,
    this.discount,
    this.applyOn,
    this.appliedValue,
    this.cartCustomDiscountId,
    this.value,
    this.valueType,
  });

  factory CartPromotionItem.deepCopy(CartPromotionItem orderPromotionItemCopy) =>  CartPromotionItem(
    id : orderPromotionItemCopy.id,
    cartRewardId: orderPromotionItemCopy.cartRewardId,
    cartDiscountId : orderPromotionItemCopy.cartPromocodeId,
    cartPromocodeId : orderPromotionItemCopy.cartPromocodeId,
    cartId : orderPromotionItemCopy.cartId,
    cartItemId : orderPromotionItemCopy.cartItemId,
    itemId : orderPromotionItemCopy.itemId,
    sku : orderPromotionItemCopy.sku,
    qty: orderPromotionItemCopy.qty,
    discount : orderPromotionItemCopy.discount,
    applyOn : orderPromotionItemCopy.applyOn,
    appliedValue : orderPromotionItemCopy.appliedValue,
    cartCustomDiscountId : orderPromotionItemCopy.cartCustomDiscountId,
    value : orderPromotionItemCopy.value,
    valueType : orderPromotionItemCopy.valueType,
  );

  CartPromotionItem.fromJson(Map<String, dynamic> json) {
    try {
      id = json['id'] ?? 0;
      cartDiscountId = json['cartDiscountId'] ?? 0;
      cartRewardId = json['cartRewardId'] ?? 0;
      cartPromocodeId = json['cartPromocodeId'] ?? 0;
      cartCustomDiscountId = json['cartCustomDiscountId']??0;
      appliedValue = json['appliedValue'] ??0;
      applyOn = json['applyOn'];
      value = json['value'] ??0;
      valueType = json['valueType'];
      cartId = json['cartId'] ?? 0;
      cartItemId = json['cartItemId'] ?? 0;
      itemId = json['itemId'] ?? 0;
      sku = json['sku'] ?? '';
      qty = json['qty'] ?? 0;
      discount = json['discount'] ?? 0;
    } catch (e, stacktrace) {
      print('OrderPromotionItem response model class ${stacktrace.toString()}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['cartDiscountId'] = cartDiscountId;
    data['cartRewardId'] = cartRewardId;
    data['cartPromocodeId'] = cartPromocodeId;
    data['cartCustomDiscountId'] = cartCustomDiscountId;
    data['cartId'] = cartId;
    data['cartItemId'] = cartItemId;
    data['itemId'] = itemId;
    data['appliedValue'] = appliedValue;
    data['applyOn'] = applyOn;
    data['value'] = value;
    data['valueType'] = valueType;
    data['sku'] = sku;
    data['qty'] = qty;
    data['discount'] = discount;
    return data;
  }
}