import '../../../utils/app_utils.dart';
import 'cart_item_modifiers.dart';
import 'cart_item_promotions.dart';

class CartItems {
  int? cartId;
  int? id;
  int? categoryId;
  String? category;
  String? itemSku;
  String? name;
  String? imageUrl;
  int? itemId;
  int? itemQty;
  num? basePrice;
  num? actualPrice;
  bool? userDefinedCustomItem;
  int? course;
  bool? sentToKitchen;
  String? userId;
  String? userName;
  String? note;
  List<CartItemModifiers>? cartItemModifiers;
  num? lineTotal;
  List<CartPromotionItem>? cartCustomDiscountItem;
  bool? selected = false;
  num? afterPromotionValue = 0;
  num? customDiscount = 0;
  CartPromotionItem? cartDiscountItem;
  CartPromotionItem? cartPromocodeItem;
  CartPromotionItem? cartRewardItem;
  bool? freeItem;
  int? cartUserId;

  CartItems(
      {this.cartId,
        this.id,
        this.categoryId,
        this.category,
        this.itemSku,
        this.name,
        this.imageUrl,
        this.itemId,
        this.itemQty,
        this.basePrice,
        this.actualPrice,
        this.userDefinedCustomItem,
        this.course,
        this.sentToKitchen,
        this.userId,
        this.userName,
        this.note,
        this.cartItemModifiers,
        this.cartCustomDiscountItem,
        this.selected,
        this.afterPromotionValue,
        this.customDiscount,
        this.lineTotal,
        this.cartDiscountItem,
        this.cartPromocodeItem,
        this.cartRewardItem,
        this.freeItem,
        this.cartUserId,
      });

  CartItems.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    categoryId = json['categoryId'] ??0;
    category = json['category']??'';
    itemSku = json['itemSku']??'';
    name = json['name']??'';
    imageUrl = json['imageUrl']??'';
    itemId = json['itemId'] ??0;
    itemQty = json['itemQty'] ??0;
    basePrice = json['basePrice'] ??0;
    actualPrice = json['actualPrice']??0;
    userDefinedCustomItem = json['userDefinedCustomItem'] ?? false;
    course = json['course'] ??0;
    sentToKitchen = json['sentToKitchen']?? false;
    userId = json['userId'] ??'';
    userName = json['userName'] ??'';
    note = json['note'] ??'';
    lineTotal = roundDouble(actualPrice! * itemQty!);
    if (json['cartItemModifiers'] != null) {
      cartItemModifiers = <CartItemModifiers>[];
      json['cartItemModifiers'].forEach((v) {
        cartItemModifiers!.add( CartItemModifiers.fromJson(v));
      });
    } else {
      cartItemModifiers = <CartItemModifiers>[];
    }

    selected = false;
    afterPromotionValue = 0;
    customDiscount = 0;
    if (json['cartCustomDiscountItem'] != null) {
      cartCustomDiscountItem = <CartPromotionItem>[];
      json['cartCustomDiscountItem'].forEach((v) {
        cartCustomDiscountItem!.add( CartPromotionItem.fromJson(v));
      });
    }else {
      cartCustomDiscountItem = <CartPromotionItem>[];
    }

    cartDiscountItem = json['cartDiscountItem'] != null
        ? CartPromotionItem.fromJson(json['cartDiscountItem'])
        : null;

    cartPromocodeItem = json['cartPromocodeItem'] != null
        ? CartPromotionItem.fromJson(json['cartPromocodeItem'])
        : null;

    cartRewardItem = json['cartRewardItem'] != null
        ? CartPromotionItem.fromJson(json['cartRewardItem'])
        : null;

    freeItem = json['freeItem'] ?? false;
    cartUserId = json['cartUserId'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['categoryId'] = categoryId;
    data['category'] = category;
    data['itemSku'] = itemSku;
    data['name'] = name;
    data['imageUrl'] = imageUrl;
    data['itemId'] = itemId;
    data['itemQty'] = itemQty;
    data['basePrice'] = basePrice;
    data['actualPrice'] = actualPrice;
    data['userDefinedCustomItem'] = userDefinedCustomItem;
    data['course'] = course;
    data['sentToKitchen'] = sentToKitchen;
    data['userId'] = userId;
    data['userName'] = userName;
    data['note'] = note;
    data['cartId'] = cartId;

    if (cartItemModifiers != null) {
      data['cartItemModifiers'] =
          cartItemModifiers!.map((v) => v.toJson()).toList();
    }

    if (cartCustomDiscountItem != null) {
      data['cartCustomDiscountItem'] =
          cartCustomDiscountItem!.map((v) => v.toJson()).toList();
    }
    if (cartDiscountItem != null) {
      data['cartDiscountItem'] = cartDiscountItem!.toJson();
    }

    if (cartPromocodeItem != null) {
      data['cartPromocodeItem'] = cartPromocodeItem!.toJson();
    }

    if (cartRewardItem != null) {
      data['cartRewardItem'] = cartRewardItem!.toJson();
    }

    data['freeItem'] = freeItem;
    data['cartUserId'] = cartUserId;

    return data;
  }
}