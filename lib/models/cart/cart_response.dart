import 'cart_data.dart';

class CartResponse {
  String? message;
  CartPayloadData? payload;

  CartResponse({this.message, this.payload});

  CartResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  CartPayloadData.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = message;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    return data;
  }
}

class CartPayloadData {
  List<CartData>? carts;
  int? currentPage;
  int? totalRecords;
  int? perPage;
  int? totalPages;

  CartPayloadData(
      {this.carts,
        this.currentPage,
        this.totalRecords,
        this.perPage,
        this.totalPages});

  CartPayloadData.fromJson(Map<String, dynamic> json) {
    if (json['order'] != null) {
      carts = <CartData>[];
      json['order'].forEach((v) {
        carts!.add(CartData.fromJson(v));
      });
    } else {
      carts = <CartData>[];
    }
    currentPage = json['currentPage'] ??0;
    totalRecords = json['totalRecords'] ??0;
    perPage = json['perPage']??0;
    totalPages = json['totalPages'] ??0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (carts != null) {
      data['order'] = carts!.map((v) => v.toJson()).toList();
    }
    data['currentPage'] = currentPage;
    data['totalRecords'] = totalRecords;
    data['perPage'] = perPage;
    data['totalPages'] = totalPages;
    return data;
  }
}