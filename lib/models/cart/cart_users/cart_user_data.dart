class CartUserData {
  int? id;
  String? userId;
  String? name;
  String? userType;
  String? phone;
  String? email;
  String? addedByUserId;
  String? addedByUserName;

  CartUserData(
      {this.id,
        this.userId,
        this.name,
        this.userType,
        this.phone,
        this.email,
        this.addedByUserId,
        this.addedByUserName,
      });

  CartUserData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    userId = json['userId'] ?? '$id';
    name = json['name']??'';
    userType = json['userType']??'';
    phone = json['phone']??'';
    email = json['email']??'';
    addedByUserId = json['addedByUserId']??'';
    addedByUserName = json['addedByUserName']??'';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['userId'] = userId;
    data['name'] = name;
    data['userType'] = userType;
    data['phone'] = phone;
    data['email'] = email;
    data['addedByUserId'] = addedByUserId;
    data['addedByUserName'] = addedByUserName;
    return data;
  }
}