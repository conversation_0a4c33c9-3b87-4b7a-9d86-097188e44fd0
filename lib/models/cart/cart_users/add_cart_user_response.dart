import 'cart_user_data.dart';

class AddCartUserResponse {
  String? message;
  CartUserData? payload;

  AddCartUserResponse({this.message, this.payload});

  AddCartUserResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ? CartUserData.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = message;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    return data;
  }
}