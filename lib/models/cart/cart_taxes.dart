class CartTaxes {
  num? id;
  num? taxId;
  num? taxAmount;
  String? name;
  String? code;
  String? taxType;
  num? taxRate;
  String? taxRateType;
  bool? status;

  CartTaxes(
      {this.id,
        this.taxId,
        this.taxAmount,
        this.name,
        this.code,
        this.taxType,
        this.taxRate,
        this.taxRateType,
        this.status});

  CartTaxes.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    taxId = json['taxId'] ??0;
    taxAmount = json['taxAmount'] ??0;
    name = json['name'] ?? '';
    code = json['code'] ??'';
    taxType = json['taxType']??'';
    taxRate = json['taxRate'] ??0;
    taxRateType = json['taxRateType'] ??'';
    status = json['status'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['taxId'] = taxId;
    data['taxAmount'] = taxAmount;
    data['name'] = name;
    data['code'] = code;
    data['taxType'] = taxType;
    data['taxRate'] = taxRate;
    data['taxRateType'] = taxRateType;
    data['status'] = status;
    return data;
  }
}