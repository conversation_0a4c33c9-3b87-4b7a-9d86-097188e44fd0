import 'package:flutter/material.dart';

class CartUser {
  String? id;
  String? lastName;
  String? firstName;
  String? email;
  String? phone;

  CartUser({this.id, this.lastName, this.firstName,this.email,this.phone});
  factory CartUser.deepCopy(CartUser user) =>  CartUser(
    id : user.id,
    lastName : user.lastName,
    firstName : user.firstName,
    email:user.email,
    phone : user.phone,
  );

  CartUser.fromJson(Map<String, dynamic> json) {
    try{
      id = json['id'];
      lastName = json['lastName'] ?? '';
      firstName = json['firstName'] ?? '';
      email = json['email'] ?? '';
      phone = json['phone'] ?? '';
    }catch(e,stacktrace){
      debugPrint('User in location cart model class ${stacktrace.toString()}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['lastName'] = lastName;
    data['firstName'] = firstName;
    data['email'] = email;
    data['phone'] = phone;
    return data;
  }
}