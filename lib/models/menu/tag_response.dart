class TagResponse{
  List<TagData>? tags;

  TagResponse({this.tags});

  TagResponse.fromJson(Map<String, dynamic> json) {
    if (json['payload'] != null) {
      tags = <TagData>[];
      json['payload'].forEach((v) {
        tags!.add(TagData.fromJson(v));
      });
    }else {
      tags = <TagData>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (tags != null) {
      data['payload'] = tags!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TagData {
  int? id;
  String? name;
  String? image;
  bool? selected = false;
  TagData({this.id, this.name, this.image, this.selected});

  TagData.fromJson(Map<String, dynamic> json) {
    id = json['id']??0;
    name = json['name']??'';
    image = json['image']??'';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    return data;
  }
}