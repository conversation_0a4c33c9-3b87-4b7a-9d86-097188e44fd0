import 'menu_data.dart';

class SearchItemResponse {
  String? message;
  List<Items>? payload;

  SearchItemResponse({this.message, this.payload});

  SearchItemResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <Items>[];
      json['payload'].forEach((v) {
        payload!.add(Items.fromJson(v));
      });
    }else {
      payload = <Items>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = message;
    if (payload != null) {
      data['payload'] = payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}