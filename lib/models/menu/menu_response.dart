import 'menu_data.dart';

class MenuResponse {
  List<MenuData>? menusData;

  MenuResponse({this.menusData,});

  MenuResponse.fromJson(Map<String, dynamic> json) {
    if (json['payload'] != null) {
      menusData = <MenuData>[];
      json['payload'].forEach((v) {
        menusData!.add( MenuData.fromJson(v));
      });
    }else {
      menusData = <MenuData>[];
    }

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (menusData != null) {
      data['payload'] = menusData!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}


