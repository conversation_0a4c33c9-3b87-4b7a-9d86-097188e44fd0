class MenuData {
  String? id;
  String? name;
  String? desc;
  int? order;
  bool? status;
  String? menuImage;
  List<MenuCategories>? menuCategories;

  MenuData(
      {this.id,
        this.name,
        this.desc,
        this.order,
        this.status,
        this.menuImage,
        this.menuCategories});

  MenuData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    name = json['name']??'';
    desc = json['desc']??'';
    order = json['order']??0;
    status = json['status']??false;
    menuImage = json['menuImage']??'';
    if (json['menuCategories'] != null) {
      menuCategories = <MenuCategories>[];
      json['menuCategories'].forEach((v) {
        menuCategories!.add( MenuCategories.fromJson(v));
      });
    }else {
      menuCategories = <MenuCategories>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  {};
    data['id'] = id;
    data['name'] = name;
    data['desc'] = desc;
    data['order'] = order;
    data['status'] = status;
    data['menuImage'] = menuImage;
    if (menuCategories != null) {
      data['menuCategories'] =
          menuCategories!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MenuCategories {
  int? id;
  String? categoryName;
  bool? categoryStatus;
  int? course;
  String? menuId;
  List<Items>? items;

  MenuCategories(
      {this.id,
        this.categoryName,
        this.categoryStatus,
        this.course,
        this.menuId,
        this.items});

  MenuCategories.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    categoryName = json['categoryName'] ??'';
    categoryStatus = json['categoryStatus']?? false;
    course = json['course']??0;
    menuId = json['menuId']??0;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(Items.fromJson(v));
      });
    }else {
      items = <Items>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['categoryName'] = categoryName;
    data['categoryStatus'] = categoryStatus;
    data['course'] = course;
    data['menuId'] = menuId;
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Items {
  int? itemId;
  String? name;
  int? categoryId;
  int? course;
  ItemPriceInventory? itemPriceInventory;
  bool? status;
  String? shortDescription;
  int? itemOrder;
  String? itemSku;
  String? imageUrl;
  List<ItemTags>? itemTags;
  List<ItemModifiers>? itemModifiers;
  MenuCategories? category;

  Items(
      {this.itemId,
        this.name,
        this.categoryId,
        this.course,
        this.itemPriceInventory,
        this.status,
        this.shortDescription,
        this.itemOrder,
        this.itemSku,
        this.imageUrl,
        this.itemTags,
        this.itemModifiers,
        this.category,
      });

  factory Items.deepCopy(Items itemToCopy) =>  Items(
    itemId: itemToCopy.itemId,
    name: itemToCopy.name,
    categoryId: itemToCopy.categoryId,
    status: itemToCopy.status,
    shortDescription: itemToCopy.shortDescription,
    itemOrder: itemToCopy.itemOrder,
    itemSku: itemToCopy.itemSku,
    imageUrl: itemToCopy.imageUrl,
    itemTags: itemToCopy.itemTags,
    itemModifiers: itemToCopy.itemModifiers?.map((e) => ItemModifiers.deepCopy(e)).toList(),
    itemPriceInventory: itemToCopy.itemPriceInventory,
    course: itemToCopy.course,
  );

  Items.fromJson(Map<String, dynamic> json) {
    itemId = json['itemId']??0;
    name = json['name']??'';
    categoryId = json['categoryId']??0;
    course = json['course']??0;
    itemPriceInventory = json['itemPriceInventory'] != null
        ?  ItemPriceInventory.fromJson(json['itemPriceInventory'])
        : null;
    status = json['status']??false;
    shortDescription = json['shortDescription'] ??'';
    itemOrder = json['itemOrder'];
    itemSku = json['itemSku']??'';
    imageUrl = json['imageUrl']??'';
    if (json['itemTags'] != null) {
      itemTags = <ItemTags>[];
      json['itemTags'].forEach((v) {
        itemTags!.add( ItemTags.fromJson(v));
      });
    }else {
      itemTags = <ItemTags>[];
    }
    if (json['itemModifiers'] != null) {
      itemModifiers = <ItemModifiers>[];
      json['itemModifiers'].forEach((v) {
        itemModifiers!.add( ItemModifiers.fromJson(v));
      });
    }else {
      itemModifiers = <ItemModifiers>[];
    }
    category = json['category'] != null
        ?  MenuCategories.fromJson(json['category'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['itemId'] = itemId;
    data['name'] = name;
    data['categoryId'] = categoryId;
    data['course'] = course;
    if (itemPriceInventory != null) {
      data['itemPriceInventory'] = itemPriceInventory!.toJson();
    }
    data['status'] = status;
    data['shortDescription'] = shortDescription;
    data['itemOrder'] = itemOrder;
    data['itemSku'] = itemSku;
    data['imageUrl'] = imageUrl;
    if (itemTags != null) {
      data['itemTags'] = itemTags!.map((v) => v.toJson()).toList();
    }
    if (itemModifiers != null) {
      data['itemModifiers'] =
          itemModifiers!.map((v) => v.toJson()).toList();
    }
    if (category != null) {
      data['category'] = category!.toJson();
    }
    return data;
  }
}

class ItemPriceInventory {
  int? id;
  String? locationId;
  num? price;
  num? cost;
  int? currentInventory;
  bool? enableInventoryCountdown;

  ItemPriceInventory(
      {this.id, this.locationId, this.price, this.cost, this.currentInventory, this.enableInventoryCountdown});

  ItemPriceInventory.fromJson(Map<String, dynamic> json) {
    id = json['id'] ??0;
    locationId = json['locationId']??'';
    price = json['price']??0;
    cost = json['cost']??0;
    currentInventory = json['currentInventory']??0;
    enableInventoryCountdown = json['enableInventoryCountdown']??false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['locationId'] = locationId;
    data['price'] = price;
    data['cost'] = cost;
    data['currentInventory'] = currentInventory;
    data['enableInventoryCountdown'] = this.enableInventoryCountdown;
    return data;
  }
}

class ItemTags {
  String? businessId;
  int? id;
  String? name;
  String? image;

  ItemTags({this.businessId, this.id, this.name, this.image});

  factory ItemTags.deepCopy(ItemTags tagsToCopy) => ItemTags(
      id: tagsToCopy.id,
      name:  tagsToCopy.name,
      image:  tagsToCopy.image,
      businessId:  tagsToCopy.businessId
  );

  ItemTags.fromJson(Map<String, dynamic> json) {
    businessId = json['businessId'] ??'';
    id = json['id']??0;
    name = json['name']??'';
    image = json['image']??'';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['businessId'] = businessId;
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    return data;
  }
}

class ItemModifiers {
  int? modifierId;
  String? name;
  num? basePrice;
  int? min;
  int? max;
  int? included;
  String? image;
  String? sku;
  bool? status;
  int? order;
  List<ModifierItems>? modifierItems;

  ItemModifiers(
      {this.modifierId,
        this.name,
        this.basePrice,
        this.min,
        this.max,
        this.included,
        this.image,
        this.sku,
        this.status,
        this.order,
        this.modifierItems});

  factory ItemModifiers.deepCopy(ItemModifiers modifiersToCopy) =>  ItemModifiers(
      modifierId : modifiersToCopy.modifierId,
      name : modifiersToCopy.name,
      basePrice : modifiersToCopy.basePrice,
      min :modifiersToCopy.min,
      max : modifiersToCopy.max,
      included : modifiersToCopy.included,
      image : modifiersToCopy.image,
      status: modifiersToCopy.status,
      order : modifiersToCopy.order,
      sku : modifiersToCopy.sku,
      modifierItems : modifiersToCopy.modifierItems?.map((e) =>ModifierItems.deepCopy(e)).toList()
  );

  ItemModifiers.fromJson(Map<String, dynamic> json) {
    modifierId = json['modifierId']??0;
    name = json['name']??'';
    basePrice = json['basePrice']??0;
    min = json['min']??0;
    max = json['max']??0;
    included = json['included']??0;
    image = json['image']??'';
    sku = json['sku']??'';
    status = json['status']??false;
    order = json['order']??0;
    if (json['modifierItems'] != null) {
      modifierItems = <ModifierItems>[];
      json['modifierItems'].forEach((v) {
        modifierItems!.add( ModifierItems.fromJson(v));
      });
    }else {
      modifierItems = <ModifierItems>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['modifierId'] = modifierId;
    data['name'] = name;
    data['basePrice'] = basePrice;
    data['min'] = min;
    data['max'] = max;
    data['included'] = included;
    data['image'] = image;
    data['sku'] = sku;
    if(status != null) {
      data['status'] = status;
    }
    data['order'] = order;
    if (modifierItems != null) {
      /// intentionally taken this key
      data['cartItemModifierItems'] =
     // data['modifierItems'] =
          modifierItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ModifierItems {
  int? modifierItemId;
  String? name;
  num? price;
  int? min;
  int? max;
  int? order;
  String? sku;
  bool? status;
  int? modifierId;
  dynamic qty = 0;
  int? included = 0;

  ModifierItems(
      {this.modifierItemId,
        this.name,
        this.price,
        this.min,
        this.max,
        this.order,
        this.sku,
        this.status,
        this.modifierId,
        this.qty,
        this.included,
      });

  factory ModifierItems.deepCopy(ModifierItems modifiersItemsToCopy)=>
      ModifierItems(
      modifierItemId : modifiersItemsToCopy.modifierItemId,
      name : modifiersItemsToCopy.name,
      price : modifiersItemsToCopy.price,
      min:modifiersItemsToCopy.min,
      max : modifiersItemsToCopy.max,
      order: modifiersItemsToCopy.order,
      status: modifiersItemsToCopy.status,
      sku: modifiersItemsToCopy.sku,
      modifierId: modifiersItemsToCopy.modifierId,
      qty : modifiersItemsToCopy.qty,
      included : modifiersItemsToCopy.included
  );

  ModifierItems.fromJson(Map<String, dynamic> json) {
    modifierItemId = json['modifierItemId'] ??0;
    name = json['name']??'';
    price = json['price']??0;
    min = json['min']??0;
    max = json['max']??0;
    order = json['order']??0;
    sku = json['sku']??'';
    status = json['status']?? false;
    modifierId = json['modifierId']??0;
    qty = json['qty'] ??0;
    included = json['included']??0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['modifierItemId'] = modifierItemId;
    data['name'] = name;
    data['price'] = price;
    data['min'] = min;
    data['max'] = max;
    data['order'] = order;
    data['sku'] = sku;
    if(status != null) {
      data['status'] = status;
    }
    if(modifierId != null) {
      data['modifierId'] = modifierId;
    }
    data['qty'] = qty;
    data['included'] = included;
    return data;
  }
}

/// custom class
class ItemCourse{
  int? id;
  String? name;

  ItemCourse({this.id, this.name});
}