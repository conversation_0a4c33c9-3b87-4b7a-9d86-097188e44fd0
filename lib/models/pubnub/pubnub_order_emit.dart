class PubNubOrderEmit {
  String? title;
  PubNubOrderPayload? payload;
  String? declaration;

  PubNubOrderEmit({this.title, this.payload, this.declaration});

  PubNubOrderEmit.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    payload =
    json['payload'] != null ? PubNubOrderPayload.fromJson(json['payload']) : null;
    declaration = json['declaration'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['title'] = title;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    data['declaration'] =declaration;
    return data;
  }
}

class PubNubOrderPayload {
  int? cartId;
  String? locationId;
  String? title;
  String? cartStatus;
  String? platform;
  String? orderReceiveMethod;
  String? userName;
  num? orderAmount;
  int? tableId;

  PubNubOrderPayload({this.cartId, this.locationId, this.title, this.cartStatus, this.platform,
  this.orderReceiveMethod, this.userName, this.orderAmount});

  PubNubOrderPayload.fromJson(Map<String, dynamic> json) {
    cartId = json['cartId'] ?? 0;
    locationId = json['locationId'] ??'';
    title = json['title'] ??'';
    cartStatus = json['cartStatus'] ??'';
    platform = json['platform'] ??'';
    orderReceiveMethod = json['orderReceiveMethod'] ??'';
    userName = json['userName'] ??'';
    orderAmount = json['orderAmount'] ??0;
    tableId = json['tableId'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data ={};
    data['cartId'] = cartId;
    data['locationId'] = locationId;
    data['title'] = title;
    data['cartStatus'] = cartStatus;
    data['platform'] = platform;
    data['orderReceiveMethod'] = orderReceiveMethod;
    data['userName'] = userName;
    data['orderAmount'] = orderAmount;
    data['tableId'] = tableId;
    return data;
  }
}
