
import '../../menu/menu_data.dart';

class CustomerFrequentOrders {
  String? message;
  List<CustomerFrequentOrderData>? payload;

  CustomerFrequentOrders({this.message, this.payload});

  CustomerFrequentOrders.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <CustomerFrequentOrderData>[];
      json['payload'].forEach((v) {
        payload!.add(new CustomerFrequentOrderData.fromJson(v));
      });
    } else {
      payload = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CustomerFrequentOrderData {
  int? itemId;
  int? purchasedCount;
  int? lastPurchasedItemId;
  Items? itemData;

  CustomerFrequentOrderData(
      {this.itemId, this.purchasedCount, this.lastPurchasedItemId,this.itemData});

  CustomerFrequentOrderData.fromJson(Map<String, dynamic> json) {
    itemId = json['itemId'];
    purchasedCount = json['purchasedCount'] ?? 0;
    lastPurchasedItemId = json['lastPurchasedItemId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['itemId'] = this.itemId;
    data['purchasedCount'] = this.purchasedCount;
    data['lastPurchasedItemId'] = this.lastPurchasedItemId;
    return data;
  }
}