
import '../../cart/cart_data.dart';

class CustomerPastOrder {
  String? message;
  List<CartData>? payload;

  CustomerPastOrder({this.message, this.payload});

  CustomerPastOrder.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <CartData>[];
      json['payload'].forEach((v) {
        payload!.add(new CartData.fromJson(v));
      });
    } else {
      payload = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}



