class CustomersResponse {
  String? message;
  Payload? payload;

  CustomersResponse({this.message, this.payload});

  CustomersResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ? new Payload.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.toJson();
    }
    return data;
  }
}

class Payload {
  List<Customer>? users;
  int? totalRecords;
  int? numberOfPages;

  Payload({this.users, this.totalRecords, this.numberOfPages});

  Payload.fromJson(Map<String, dynamic> json) {
    if (json['users'] != null) {
      users = <Customer>[];
      json['users'].forEach((v) {
        users!.add(new Customer.fromJson(v));
      });
    }
    totalRecords = json['totalRecords'];
    numberOfPages = json['numberOfPages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.users != null) {
      data['users'] = this.users!.map((v) => v.toJson()).toList();
    }
    data['totalRecords'] = this.totalRecords;
    data['numberOfPages'] = this.numberOfPages;
    return data;
  }
}

class Customer {
  String? id;
  String? businessId;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;
  String? password;
  String? province;
  String? dob;
  bool? marketing;
  bool? smsNotification;
  bool? emailNotification;
  bool? active;
  String? status;
  bool? emailVerificationStatus;
  bool? phoneVerificationStatus;
  String? registeredFrom;
  String? registeredBy;
  String? registeredById;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  num? averageOrderValue;
  num? totalOrderValue;
  List<UserAddress>? userAddress;
  UserAddress? deliveryAddress;

  Customer({
    this.id,
    this.businessId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.password,
    this.province,
    this.dob,
    this.marketing,
    this.smsNotification,
    this.emailNotification,
    this.active,
    this.status,
    this.emailVerificationStatus,
    this.phoneVerificationStatus,
    this.registeredFrom,
    this.registeredBy,
    this.registeredById,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.averageOrderValue,
    this.totalOrderValue,
    this.userAddress,
    this.deliveryAddress,
  });

  Customer.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    businessId = json['businessId'];
    firstName = json['firstName'] ?? '';
    lastName = json['lastName'] ?? '';
    email = json['email'] ?? '';
    phone = json['phone'] ?? '';
    password = json['password'];
    province = json['province'];
    dob = json['dob'];
    marketing = json['marketing'];
    smsNotification = json['smsNotification'];
    emailNotification = json['emailNotification'];
    active = json['active'];
    status = json['status'] ?? '';
    emailVerificationStatus = json['emailVerificationStatus'];
    phoneVerificationStatus = json['phoneVerificationStatus'];
    registeredFrom = json['registeredFrom'];
    registeredBy = json['registeredBy'];
    registeredById = json['registeredById'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    deletedAt = json['deletedAt'];
    totalOrderValue = json['totalOrderValue'] ?? 0;
    averageOrderValue = json['averageOrderValue'] ?? 0;
    if (json['userAddress'] != null) {
      userAddress = <UserAddress>[];
      json['userAddress'].forEach((v) {
        userAddress!.add(new UserAddress.fromJson(v));
      });
    } else {
      userAddress = [];
    }
    deliveryAddress = json['deliveryAddress'] != null
        ? new UserAddress.fromJson(json['deliveryAddress'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['businessId'] = this.businessId;
    data['firstName'] = this.firstName;
    data['lastName'] = this.lastName;
    data['email'] = this.email;
    data['phone'] = this.phone;
    data['password'] = this.password;
    data['province'] = this.province;
    data['dob'] = this.dob;
    data['marketing'] = this.marketing;
    data['smsNotification'] = this.smsNotification;
    data['emailNotification'] = this.emailNotification;
    data['active'] = this.active;
    data['status'] = this.status;
    data['emailVerificationStatus'] = this.emailVerificationStatus;
    data['phoneVerificationStatus'] = this.phoneVerificationStatus;
    data['registeredFrom'] = this.registeredFrom;
    data['registeredBy'] = this.registeredBy;
    data['registeredById'] = this.registeredById;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['deletedAt'] = this.deletedAt;
    data['totalOrderValue'] = this.totalOrderValue;
    data['averageOrderValue'] = this.averageOrderValue;
    if (this.userAddress != null) {
      data['userAddress'] = this.userAddress!.map((v) => v.toJson()).toList();
    }
    if (this.deliveryAddress != null) {
      data['deliveryAddress'] = this.deliveryAddress!.toJson();
    }
    return data;
  }
}

class UserAddress {
  int? id;
  String? userId;
  String? address1;
  String? address2;
  String? postalCode;
  String? city;
  String? province;
  String? latitude;
  String? longitude;
  String? note;
  String? phone;
  bool? defaultAddress;
  bool? deliveryWithinZone;

  UserAddress(
      {this.id,
      this.userId,
      this.address1,
      this.address2,
      this.postalCode,
      this.city,
      this.province,
      this.latitude,
      this.longitude,
      this.note,
      this.phone,
      this.defaultAddress,
      this.deliveryWithinZone,
      });

  UserAddress.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    address1 = json['address1'];
    address2 = json['address2'];
    postalCode = json['postalCode'];
    city = json['city'];
    province = json['province'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    note = json['note'];
    phone = json['phone'];
    defaultAddress = json['defaultAddress'];
    deliveryWithinZone = json['deliveryWithinZone'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userId'] = this.userId;
    data['address1'] = this.address1;
    data['address2'] = this.address2;
    data['postalCode'] = this.postalCode;
    data['city'] = this.city;
    data['province'] = this.province;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    data['note'] = this.note;
    data['phone'] = this.phone;
    data['default'] = this.defaultAddress;
    data['deliveryWithinZone'] = this.deliveryWithinZone;
    return data;
  }
}