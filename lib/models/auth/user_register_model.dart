class UserData {
  String? id;
  String? name;
  String? email;
  String? phone;
  String? userType;
  String? status;
  List<String>? permissions;
  String? businessId;

  UserData(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.userType,
      this.status,
      this.permissions,
      this.businessId});

  UserData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    userType = json['userType'];
    status = json['status'];
    permissions = json['permissions'].cast<String>();
    businessId = json['businessId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['userType'] = userType;
    data['status'] = status;
    data['permissions'] = permissions;
    data['businessId'] = businessId;
    return data;
  }
}
