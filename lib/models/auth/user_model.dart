class UserModel {
  String? id;
  String? name;
  String? email;
  String? phone;
  String? userType;
  String? status;
  String? token;

  UserModel(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.userType,
      this.status,
      this.token});

  UserModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    userType = json['userType'];
    status = json['status'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['userType'] = userType;
    data['status'] = status;
    data['token'] = token;
    return data;
  }
}

class RefreshTokenModel {
  String? newToken;

  RefreshTokenModel({this.newToken});

  RefreshTokenModel.fromJson(Map<String, dynamic> json) {
    newToken = json['newToken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['newToken'] = newToken;
    return data;
  }
}
