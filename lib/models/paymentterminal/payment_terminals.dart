class PaymentTerminalResponse {
  String? message;
  List<PaymentTerminal>? payload;

  PaymentTerminalResponse({this.message, this.payload});

  PaymentTerminalResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <PaymentTerminal>[];
      json['payload'].forEach((v) {
        payload!.add(new PaymentTerminal.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PaymentTerminal {
  num? id;
  String? locationId;
  String? processor;
  String? merchantId;
  String? terminalId;
  bool? active;
  String? createdBy;
  String? updatedBy;
  String? createdAt;
  String? updatedAt;
  bool? isSelected;

  PaymentTerminal({
    this.id,
    this.locationId,
    this.processor,
    this.merchantId,
    this.terminalId,
    this.active,
    this.createdBy,
    this.updatedBy,
    this.createdAt,
    this.updatedAt,
    this.isSelected,
  });

  PaymentTerminal.fromJson(Map<String, dynamic> json) {
    try {
      id = json['id'];
      locationId = json['locationId'];
      processor = json['processor'];
      merchantId = json['merchantId'];
      terminalId = json['terminalId'];
      active = json['active'];
      createdBy = json['createdBy'];
      updatedBy = json['updatedBy'];
      createdAt = json['createdAt'];
      updatedAt = json['updatedAt'];
      isSelected = false;
    } catch (exception, stacktrace) {
      print('Payment terminal model class ${stacktrace.toString()}');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['locationId'] = this.locationId;
    data['processor'] = this.processor;
    data['merchantId'] = this.merchantId;
    data['terminalId'] = this.terminalId;
    data['active'] = this.active;
    data['createdBy'] = this.createdBy;
    data['updatedBy'] = this.updatedBy;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['isSelected'] = this.isSelected;
    return data;
  }
}
