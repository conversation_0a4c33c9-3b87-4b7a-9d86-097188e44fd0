class LocationUserModel {
  String? id;
  String? name;
  String? email;
  String? phone;
  String? userType;
  String? createdBy;
  String? updatedBy;
  String? status;
  String? createdAt;
  String? updatedAt;

  LocationUserModel(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.userType,
      this.createdBy,
      this.updatedBy,
      this.status,
      this.createdAt,
      this.updatedAt});

  LocationUserModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'] ?? '';
    phone = json['phone'];
    userType = json['userType'];
    createdBy = json['createdBy'];
    updatedBy = json['updatedBy'];
    status = json['status'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['email'] = this.email;
    data['phone'] = this.phone;
    data['userType'] = this.userType;
    data['createdBy'] = this.createdBy;
    data['updatedBy'] = this.updatedBy;
    data['status'] = this.status;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }

  factory LocationUserModel.fromLocationUser(Map<String, dynamic> json) {
    return LocationUserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      userType: json['userType'],
      createdBy: json['createdBy'],
      updatedBy: json['updatedBy'],
      status: json['status'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }
}
