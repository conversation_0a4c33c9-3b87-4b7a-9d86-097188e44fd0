class LocationModel {
  String? id;
  String? businessId;
  String? name;
  String? slug;
  String? licenseId;
  bool? corporate;
  String? internalId;
  String? address1;
  String? address2;
  String? city;
  String? postalCode;
  String? country;
  String? province;
  String? timezone;
  double? lat;
  double? long;
  String? phone;
  String? email;

  LocationModel(
      {this.id,
      this.businessId,
      this.name,
      this.slug,
      this.licenseId,
      this.corporate,
      this.internalId,
      this.address1,
      this.address2,
      this.city,
      this.postalCode,
      this.country,
      this.province,
      this.timezone,
      this.lat,
      this.long,
      this.phone,
      this.email,
      });

  LocationModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    businessId = json['businessId'];
    name = json['name'] ?? '';
    slug = json['slug'];
    licenseId = json['licenseId'];
    corporate = json['corporate'];
    internalId = json['internalId']??'';
    address1 = json['address1'];
    address2 = json['address2'];
    city = json['city'];
    postalCode = json['postalCode'];
    country = json['country'];
    province = json['province'];
    timezone = json['timezone'];
    lat = json['lat'];
    long = json['long'];
    phone = json['phone'];
    email = json['email'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['businessId'] = businessId;
    data['name'] = name;
    data['slug'] = slug;
    data['licenseId'] = licenseId;
    data['corporate'] = corporate;
    data['internalId'] = internalId;
    data['address1'] = address1;
    data['address2'] = address2;
    data['city'] = city;
    data['postalCode'] = postalCode;
    data['country'] = country;
    data['province'] = province;
    data['timezone'] = timezone;
    data['lat'] = lat;
    data['long'] = long;
    data['phone'] = phone;
    data['email'] = email;
    return data;
  }

  factory LocationModel.fromBusinessLocation(Map<String, dynamic> json) {
    return LocationModel(
      id: json['id'],
      businessId: json['businessId'],
      name: json['name'],
      slug: json['slug'],
      licenseId: json['licenseId'],
      corporate: json['corporate'],
      internalId: json['internalId'],
      address1: json['address1'],
      address2: json['address2'],
      city: json['city'],
      postalCode: json['postalCode'],
      country: json['country'],
      province: json['province'],
      timezone: json['timezone'],
      lat: json['lat'],
      long: json['long'],
      phone: json['phone'],
      email: json['email'],
    );
  }
}



