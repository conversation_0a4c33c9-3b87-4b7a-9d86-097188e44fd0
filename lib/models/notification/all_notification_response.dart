class UserNotificationResponse {
  int? totalRecords;
  int? currentPage;
  int? totalPages;
  int? perPage;
  List<UserNotificationData>? userData;

  UserNotificationResponse(
      {this.totalRecords,
        this.currentPage,
        this.totalPages,
        this.perPage,
        this.userData});

  UserNotificationResponse.fromJson(Map<String, dynamic> json) {

    totalRecords = json['totalRecords'];
    currentPage = json['currentPage'];
    totalPages = json['totalPages'];
    perPage = json['perPage'];
    if (json['data'] != null) {
      userData = <UserNotificationData>[];
      json['data'].forEach((v) {
        userData!.add(new UserNotificationData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalRecords'] = this.totalRecords;
    data['currentPage'] = this.currentPage;
    data['totalPages'] = this.totalPages;
    data['perPage'] = this.perPage;
    if (this.userData != null) {
      data['data'] = this.userData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class UserNotificationData {
  String? id;
  String? businessId;
  String? locationId;
  String? userId;
  String? topicKey;
  String? priority;
  String? title;
  String? body;
  RowData? data;
  bool? isRead;
  String? createdAt;
  String? updatedAt;

  UserNotificationData(
      {this.id,
        this.businessId,
        this.locationId,
        this.userId,
        this.topicKey,
        this.priority,
        this.title,
        this.body,
        this.data,
        this.isRead,
        this.createdAt,
        this.updatedAt});

  UserNotificationData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    businessId = json['businessId'];
    locationId = json['locationId'];
    userId = json['userId'];
    topicKey = json['topicKey'];
    priority = json['priority'];
    title = json['title'];
    body = json['body'];
    data = json['data'] != null ? new RowData.fromJson(json['data']) : null;
    isRead = json['isRead'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['businessId'] = this.businessId;
    data['locationId'] = this.locationId;
    data['userId'] = this.userId;
    data['topicKey'] = this.topicKey;
    data['priority'] = this.priority;
    data['title'] = this.title;
    data['body'] = this.body;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['isRead'] = this.isRead;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}

class RowData {
  int? cartId;
  int? tableId;
  String? topicKey;
  String? cartStatus;
  String? tableLabel;
  int? discountId;
  String? discountType;
  String? cartCreated;
  String? dateTime;

  RowData(
      {this.cartId,
        this.tableId,
        this.topicKey,
        this.cartStatus,
        this.discountId,
        this.discountType,
        this.tableLabel,
        this.cartCreated,
        this.dateTime,});

  RowData.fromJson(Map<String, dynamic> json) {
    cartId = json['cartId'];
    tableId = json['tableId'];
    topicKey = json['topicKey'];
    cartStatus = json['cartStatus'];
    discountId = json['discountId'];
    discountType = json['discountType'];
    tableLabel = json['tableLabel'];
    cartCreated = json['cartCreated'];
    dateTime = json['dateTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> rowData = new Map<String, dynamic>();
    rowData['cartId'] = this.cartId;
    rowData['tableId'] = this.tableId;
    rowData['topicKey'] = this.topicKey;
    rowData['cartStatus'] = this.cartStatus;
    rowData['discountId'] = this.discountId;
    rowData['discountType'] = this.discountType;
    rowData['tableLabel'] = this.tableLabel;
    rowData['cartCreated'] = this.cartCreated;
    rowData['dateTime'] = this.dateTime;
    return rowData;
  }
}
