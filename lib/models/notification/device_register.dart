class NotificationData {
  String? id;
  String? deviceType;
  String? deviceToken;
  String? businessId;
  String? locationId;
  String? userId;
  String? createdBy;
  String? updatedAt;
  String? createdAt;
  int? unreadCount;

  NotificationData(
      {this.id,
      this.deviceType,
      this.deviceToken,
      this.businessId,
      this.locationId,
      this.userId,
      this.createdBy,
      this.updatedAt,
      this.createdAt,
      this.unreadCount
      });

  NotificationData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    deviceType = json['deviceType'];
    deviceToken = json['deviceToken'];
    businessId = json['businessId'];
    locationId = json['locationId'];
    userId = json['userId'];
    createdBy = json['createdBy'];
    updatedAt = json['updatedAt'];
    createdAt = json['createdAt'];
    unreadCount = json['unreadCount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['deviceType'] = this.deviceType;
    data['deviceToken'] = this.deviceToken;
    data['businessId'] = this.businessId;
    data['locationId'] = this.locationId;
    data['userId'] = this.userId;
    data['createdBy'] = this.createdBy;
    data['updatedAt'] = this.updatedAt;
    data['createdAt'] = this.createdAt;

    data['unreadCount'] = this.unreadCount;
    return data;
  }
}