import 'package:flutter/material.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../services/notification/notification_service.dart';
import '../../utils/color_pair.dart';
import '../../utils/datetime_utils.dart';
import 'all_notification_response.dart';

class ActiveViewProvider with ChangeNotifier{
  NotificationService _notificationService = NotificationService();

  String? _selectedNotificationId;
  get  selectedNotificationId => _selectedNotificationId;
  set  selectedNotificationId(value) {
    _selectedNotificationId = value;
    notifyListeners();
  }

  RowData? _data;
  RowData? get selectedNotificationData => _data;
  set selectedNotificationData(RowData? value) {
    _data = value;
    notifyListeners();
  }

  bool _isNewestAsc = true;
  bool get isNewest => _isNewestAsc;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  int _currentPage = 1;
  int get currentPage => _currentPage;

  int _totalPages = 1;
  int get totalPages => _totalPages;


  List<UserNotificationData> _userDataList = [];
  List<UserNotificationData> get userDataList => _userDataList;

  String _sortByCreatedAt= '';
  get sortByCreatedAt => _sortByCreatedAt;

  ///Update notification list
  updateUserNotificationList(List<UserNotificationData> userDataList) {
    _userDataList.addAll(userDataList);
    notifyListeners();
  }

  removeCartList(UserNotificationData userData){
    _userDataList.remove(userData);
    notifyListeners();
  }

  Future<void> sortNewestOldest() async{
    if(_isNewestAsc){
      _sortByCreatedAt = 'asc';
      _isNewestAsc = false;
    }else{
      _sortByCreatedAt = 'desc';
      _isNewestAsc = true;
    }
    notifyListeners();
  }

  resetCurrentPage(){
    _currentPage = 1;
    _totalPages = 1;
    _userDataList = [];
    notifyListeners();
  }

  resetSorting() {
    _sortByCreatedAt ='';
    notifyListeners();
  }

  setCurrentActiveViewPage(){
    _currentPage++;
    /// if current page is last page then no need to call
    if(_currentPage >_totalPages) {
      _currentPage = _totalPages;
      return;
    }
    getActiveAllNotification();
  }

  setCurrentHistoryViewPage(){
    _currentPage++;
    /// if current page is last page then no need to call
    if(_currentPage >_totalPages) {
      _currentPage = _totalPages;
      return;
    }
    getHistoryAllNotification();
  }

  /// get active list of all notifications
  getActiveAllNotification() async {
    try {
      if(sortByCreatedAt.isEmpty){
        _sortByCreatedAt = "desc";
      }
      UserNotificationResponse response = await _notificationService.listOfAllNotification(
          sortByCreatedAt: _sortByCreatedAt ,page: "$_currentPage", isRead: "false");

      if (response.userData != null) {
        _totalPages = response.totalPages ?? 1;
        updateUserNotificationList(response.userData!);
      }

    } catch (error) {
      throw error;
    }
  }

  /// Mark as read notification `
  markReadNotification({required String notificationId, required bool isRead}) async {
    try {
      await _notificationService.markAsReadUnreadNotification(notificationId: notificationId,isRead: isRead);

    } catch (error) {
      throw error;
    }
  }

  /// Mark all notification read
  markAllReadNotification() async {
    try {
      await _notificationService.markAllReadNotification();

    } catch (error) {
      throw error;
    }
  }

  // Calculate row background color
  ColorPair calculateRowBackgroundColor(UserNotificationData userData){
    int differenceInMinutes = 0;
    if(userData.data!.topicKey == NotificationsString.tableActive
     || userData.data!.topicKey == NotificationsString.discountApproval){
      if(userData.createdAt != null){
        differenceInMinutes = DateTimeUtils.compareTimeWithCurrentInMinutes(userData.createdAt!);
      }
    }else if(userData.data!.topicKey == NotificationsString.serverRequest
        || userData.data!.topicKey == NotificationsString.bills){
      if(userData.data!.dateTime != null){
        differenceInMinutes = DateTimeUtils.compareTimeWithCurrentInMinutes(userData.data!.dateTime!);
      }else{
        differenceInMinutes = DateTimeUtils.compareTimeWithCurrentInMinutes(userData.createdAt!);
      }
    }

    // debugPrint("differenceInMinutes: $differenceInMinutes");

    // Apply color coding based on the time difference
    //If less than 10 minutes time elapsed is blue
    // Determine the font color and background color based on the time difference
    if (differenceInMinutes < 10) {
      return ColorPair(ColorConstant.elapsedBlueFontColor, ColorConstant.elapsedBlueColor);
    } else if (differenceInMinutes <= 15) {
      return ColorPair(ColorConstant.elapsedYellowFontColor, ColorConstant.elapsedYellow);
    } else {
      return ColorPair(ColorConstant.elapsedRedFontColor, ColorConstant.elapsedRedPaidColor);
    }
  }


  /// get active list of all notifications
  getHistoryAllNotification() async {
    try {
      if(sortByCreatedAt.isEmpty){
        _sortByCreatedAt = "desc";
      }
      UserNotificationResponse response = await _notificationService.listOfAllNotification(
          sortByCreatedAt: _sortByCreatedAt,
          page: "${_currentPage}", isRead: "true");

      if (response.userData != null) {
        _totalPages = response.totalPages ?? 1;
        updateUserNotificationList(response.userData!);
      }

    } catch (error) {
      throw error;
    }
  }

  reset(){
    _data = null;
    _isNewestAsc = true;
    _isLoading = false;
    _userDataList.clear();
    _sortByCreatedAt = '';
    _currentPage = 1;
    _totalPages = 1;
    _selectedNotificationId = null;
  }

}