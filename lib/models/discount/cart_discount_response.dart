import '../cart/cart_promotions/cart_discount.dart';

class CartDiscountResponse {
  String? message;
  CartDiscount? payload;

  CartDiscountResponse({this.message, this.payload});

  CartDiscountResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  CartDiscount.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  {};
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.toJson();
    }
    return data;
  }
}





