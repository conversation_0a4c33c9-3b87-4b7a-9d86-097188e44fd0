class DiscountResponse {

  String? message;
  DiscountPayload? payload;

  DiscountResponse({this.message, this.payload});

  DiscountResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ?  DiscountPayload.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.toJson();
    }
    return data;
  }

}


class DiscountPayload {
  List<DiscountsData>? discounts;
  int? totalRecords;
  int? numberOfPages;

  DiscountPayload({this.discounts, this.totalRecords, this.numberOfPages});

  DiscountPayload.fromJson(Map<String, dynamic> json) {
    if (json['discounts'] != null) {
      discounts = <DiscountsData>[];
      json['discounts'].forEach((v) {
        discounts!.add( DiscountsData.fromJson(v));
      });
    }

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  Map<String, dynamic>();
    if (this.discounts != null) {
      data['discounts'] = this.discounts!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class DiscountsData {
  num? id;
  String? businessId;
  String? description;
  String? title;
  String? userType;
  String? startDate;
  String? endDate;
  String? discountType;
  String? applyDiscountOn;
  num? discount;
  num? maxDiscountAmount;
  num? minAmount;
  num? maxUse;
  num? maxUsePerPerson;
  num? totalUsage;
  bool? status;


  DiscountsData(
      {this.id,
        this.businessId,
        this.description,
        this.title,
        this.userType,
        this.startDate,
        this.endDate,
        this.discountType,
        this.applyDiscountOn,
        this.discount,
        this.maxDiscountAmount,
        this.minAmount,
        this.maxUse,
        this.maxUsePerPerson,
        this.totalUsage,
        this.status,
        });

  DiscountsData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    businessId = json['businessId'];
    description = json['description'] ?? '';
    title = json['title'];
    userType = json['userType'];
    startDate = json['startDate'];
    endDate = json['endDate'];
    discountType = json['discountType'];
    applyDiscountOn = json['applyDiscountOn'];
    discount = json['discount'] ?? 0;
    maxDiscountAmount = json['maxDiscountAmount'] ?? 0;
    minAmount = json['minAmount'] ?? 0;
    maxUse = json['maxUse'] ?? 0;
    maxUsePerPerson = json['maxUsePerPerson'] ?? 0;
    totalUsage = json['totalUsage'] ?? 0;
    status = json['status'] ?? false;

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  Map<String, dynamic>();
    data['id'] = this.id;
    data['businessId'] = this.businessId;
    data['description'] = this.description;
    data['title'] = this.title;
    data['userType'] = this.userType;
    data['startDate'] = this.startDate;
    data['endDate'] = this.endDate;
    data['discountType'] = this.discountType;
    data['applyDiscountOn'] = this.applyDiscountOn;
    data['discount'] = this.discount;
    data['maxDiscountAmount'] = this.maxDiscountAmount;
    data['minAmount'] = this.minAmount;
    data['maxUse'] = this.maxUse;
    data['maxUsePerPerson'] = this.maxUsePerPerson;
    data['totalUsage'] = this.totalUsage;
    data['status'] = this.status;
    return data;
  }
}