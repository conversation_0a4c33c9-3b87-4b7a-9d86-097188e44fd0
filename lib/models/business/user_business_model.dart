class UserBusinessModel {
  String? id;
  String? name;
  String? slug;
  bool? status;
  Branding? branding;
  List<BusinessTaxes>? taxes;

  UserBusinessModel(
      {this.id,
      this.name,
      this.slug,
      this.status,
      this.branding,
      this.taxes,
      });

  UserBusinessModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    slug = json['slug'];
    status = json['status'] ?? false;
    branding = json['branding'] != null
        ? Branding.fromJson(json['branding'])
        : null;
    if (json['taxes'] != null) {
      taxes = <BusinessTaxes>[];
      json['taxes'].forEach((v) {
        taxes!.add(BusinessTaxes.fromJson(v));
      });
    } else {
      taxes = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['slug'] = slug;
    data['status'] = status;
    if (branding != null) {
      data['branding'] = branding!.toJson();
    }
    if (taxes != null) {
      data['taxes'] = taxes!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  factory UserBusinessModel.fromGetMyBusinesses(Map<String, dynamic> json) {
    return UserBusinessModel(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      status: json['status'],
      branding: json['branding'] != null
          ? Branding.fromJson(json['branding'])
          : null,
    );
  }
}

class Branding {
  int? id;
  String? businessId;
  String? logo;
  String? primaryColor;
  String? secondaryColor;

  Branding(
      {this.id,
      this.businessId,
      this.logo,
      this.primaryColor,
      this.secondaryColor,
      });

  Branding.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    businessId = json['businessId'];
    logo = json['logo'] ?? '';
    primaryColor = json['primaryColor'];
    secondaryColor = json['secondaryColor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['businessId'] = businessId;
    data['logo'] = logo;
    data['primaryColor'] = primaryColor;
    data['secondaryColor'] = secondaryColor;
    return data;
  }
}

class BusinessTaxes {
  int? id;
  String? locationId;
  String? name;
  String? code;
  String? taxType;
  int? taxRate;
  String? taxRateType;
  bool? status;
  bool? selected;
  String? displayValue;


  BusinessTaxes(
      {this.id,
        this.locationId,
        this.name,
        this.code,
        this.taxType,
        this.taxRate,
        this.taxRateType,
        this.status,
        this.selected,
        this.displayValue,
      });

  BusinessTaxes.fromJson(Map<String, dynamic> json) {
    id = json['id']??0;
    locationId = json['locationId'] ??'';
    name = json['name']??'';
    code = json['code']??'';
    taxType = json['taxType']??'';
    taxRate = json['taxRate']??0;
    taxRateType = json['taxRateType'] ??'';
    status = json['status']?? false;
    selected = false;
    displayValue = '$name${taxRateType=='percentage' ? ' $taxRate%' : ' \$$taxRate'}';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['locationId'] = locationId;
    data['name'] = name;
    data['code'] = code;
    data['taxType'] = taxType;
    data['taxRate'] = taxRate;
    data['taxRateType'] = taxRateType;
    data['status'] = status;
    return data;
  }
}
