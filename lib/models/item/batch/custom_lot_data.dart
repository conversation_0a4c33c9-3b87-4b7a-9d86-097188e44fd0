class CustomLotData {
  int? orderItemId;
  int? lotId;
  String? name;
  String? lotName;
  int? inventoryCount;
  bool? isOld;
  bool? isScanned;
  int? selectedDropDown;
  String? createdBy;
  String? updatedBy;
  bool? isAvailable;

  CustomLotData({
    this.orderItemId,
    this.lotId,
    this.name,
    this.lotName,
    this.inventoryCount,
    this.isOld,
    this.isScanned,
    this.selectedDropDown,
    this.createdBy,
    this.updatedBy,
    this.isAvailable,
  });

}
