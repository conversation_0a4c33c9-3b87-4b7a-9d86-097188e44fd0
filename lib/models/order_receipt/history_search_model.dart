
import '../../constants/app_string.dart';

enum SortOrderOn {
  asc,
  desc,
}

enum SortOrderBy {
  id,
  createdAt,
  total,
  status,
  user,
  pointsEarned,
}

class HistoryModel {

  String key;
  bool isSelected;

  static List<HistoryModel> historyActions = [
    HistoryModel(HistoryViewString.customer,false),
    HistoryModel(HistoryViewString.order,false),
    HistoryModel(HistoryViewString.date,false),
    HistoryModel(HistoryViewString.loyaltyPoints,false),
    HistoryModel(HistoryViewString.total,false),
    HistoryModel(HistoryViewString.status,false),
  ];

  HistoryModel(this.key, this.isSelected,);
}