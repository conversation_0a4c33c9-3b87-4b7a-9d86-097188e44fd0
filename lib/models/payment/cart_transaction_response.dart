class CartTransactionResponse {
  String? message;
  CartTransaction? payload;

  CartTransactionResponse({this.message, this.payload});

  CartTransactionResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    payload =
    json['payload'] != null ? new CartTransaction.fromJson(json['payload']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    if (this.payload != null) {
      data['payload'] = this.payload!.toJson();
    }
    return data;
  }
}

class CartTransaction {
  int? id;
  int? parentPaymentId;
  int? cartId;
  num? total;
  num? refundedAmount;
  String? transactionType;
  String? paymentType;
  String? status;
  String? processor;
  String? txnNumber;
  bool? isRefunded;
  num? cardNumber;
  String? cardType;
  String? checkoutType;
  /*String? request;
  String? response;
  String? deletedAt;
  String? createdAt;
  String? updatedAt;*/

  CartTransaction(
      {this.id,
        this.parentPaymentId,
        this.cartId,
        this.total,
        this.refundedAmount,
        this.transactionType,
        this.paymentType,
        this.status,
        this.processor,
        this.txnNumber,
        this.isRefunded,
        this.cardNumber,
        this.cardType,
        this.checkoutType,
        /*this.request,
        this.response,
        this.deletedAt,
        this.createdAt,
        this.updatedAt*/});

  CartTransaction.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    parentPaymentId = json['parentPaymentId'] ?? 0;
    cartId = json['cartId'] ?? 0;
    total = json['total'] ?? 0;
    refundedAmount = json['refundedAmount'] ?? 0;
    transactionType = json['transactionType'] ?? '';
    paymentType = json['paymentType'] ?? '';
    status = json['status'] ?? '';
    processor = json['processor'] ?? '';
    txnNumber = json['txnNumber'] ?? '';
    isRefunded = json['isRefunded'] ?? false;
    cardNumber = json['cardNumber'] ?? 0;
    cardType = json['cardType'] ?? '';
    checkoutType = json['checkoutType'] ?? '';
    /*request = json['request'] ?? '';
    response = json['response'] ?? '';
    deletedAt = json['deletedAt'] ?? '';
    createdAt = json['createdAt'] ?? '';
    updatedAt = json['updatedAt'] ?? '';*/
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['parentPaymentId'] = this.parentPaymentId;
    data['cartId'] = this.cartId;
    data['total'] = this.total;
    data['refundedAmount'] = this.refundedAmount;
    data['transactionType'] = this.transactionType;
    data['paymentType'] = this.paymentType;
    data['status'] = this.status;
    data['processor'] = this.processor;
    data['txnNumber'] = this.txnNumber;
    data['isRefunded'] = this.isRefunded;
    data['cardNumber'] = this.cardNumber;
    data['cardType'] = this.cardType;
    data['checkoutType'] = this.checkoutType;
   /* data['request'] = this.request;
    data['response'] = this.response;
    data['deletedAt'] = this.deletedAt;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;*/
    return data;
  }
}