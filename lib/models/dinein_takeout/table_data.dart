import '../cart/cart_data.dart';

class TableData {
  int? id;
  String? locationId;
  int? floorId;
  String? label;
  int? sort;
  CartData? cartData;
  bool? isSelected = false;

  TableData({
    this.id,
    this.locationId,
    this.floorId,
    this.label,
    this.sort,
    this.cartData,
    this.isSelected,
  });

  TableData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    locationId = json['locationId'] ?? '';
    floorId = json['floorId'] ?? 0;
    label = json['label'] ?? '';
    sort = json['sort'] ?? 0;
    cartData = json['cart'] != null ?  CartData.fromJson(json['cart']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['locationId'] = locationId;
    data['floorId'] = floorId;
    data['label'] = label;
    data['sort'] = sort;
    if (cartData != null) {
      data['cart'] = cartData!.toJson();
    }
    return data;
  }
}