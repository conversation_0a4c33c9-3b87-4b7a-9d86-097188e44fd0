import 'package:gr8tables_server_manager/models/dinein_takeout/table_data.dart';


class FloorsResponse {
  String? message;
  List<FloorData>? payload;

  FloorsResponse({this.message, this.payload});

  FloorsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['payload'] != null) {
      payload = <FloorData>[];
      json['payload'].forEach((v) {
        payload!.add(FloorData.fromJson(v));
      });
    } else {
      payload = <FloorData>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['message'] = message;
    if (payload != null) {
      data['payload'] = payload!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FloorData {
  int? id;
  String? locationId;
  String? name;
  int? sort;
  List<TableData>? tables;
  bool? isSelected = false;

  FloorData(
      {this.id,
      this.locationId,
      this.name,
      this.sort,
      this.tables,
      this.isSelected});

  FloorData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    locationId = json['locationId'] ?? '';
    name = json['name'] ?? '';
    sort = json['sort'] ?? 0;
    if (json['tables'] != null) {
      tables = <TableData>[];
      json['tables'].forEach((v) {
        tables!.add(TableData.fromJson(v));
      });
    } else {
      tables = <TableData>[];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['locationId'] = locationId;
    data['name'] = name;
    data['sort'] = sort;
    if (tables != null) {
      data['tables'] = tables!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}


