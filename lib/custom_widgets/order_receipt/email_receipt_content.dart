
import 'package:flutter/material.dart';
import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../utils/app_utils.dart';
import '../app_text_style.dart';
import '../common_widget.dart';



class EmailReceiptContent extends StatefulWidget {

  final Function(String value)? sendButtonClick;

  EmailReceiptContent({
    Key? key,
    required this.sendButtonClick,
  }) : super(key: key);

  @override
  State<EmailReceiptContent> createState() => _EmailReceiptContentState();
}

class _EmailReceiptContentState extends State<EmailReceiptContent> {

  late TextEditingController _emailController;

  @override
  void initState() {
    _emailController = TextEditingController();
    super.initState();
  }
  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: SingleChildScrollView(
        child: ValueListenableBuilder<TextEditingValue>(
          valueListenable: _emailController,
          builder: (context, value, child) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        EmailReceiptString.emailReceipt,
                        textAlign: TextAlign.center,
                        style: AppTextStyle.largeTextStyle.copyWith(
                          fontSize: isBigScreenResolution(context) ?20.0 : 14.0 ,
                          letterSpacing: 0.5,
                          color: ColorConstant.colorBlueDark,
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: (){
                        _emailController.clear();
                        Navigator.of(context).pop();
                      },
                      child: closeIcon(isBigScreenResolution(context) ? 25.0 : 20.0,),
                    ),
                  ],
                ),
                Container(
                  margin:  EdgeInsets.only(top: isBigScreenResolution(context) ? 30 : 20,),
                  padding: const EdgeInsets.all(7.0),
                  decoration: BoxDecoration(
                    color:  Colors.transparent,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(color: value.text.isEmpty ? ColorConstant.colorBlueLight_50 : validateEmail(value.text) ? ColorConstant.buttonBgColor :
                    ColorConstant.colorBlueLight_32,
                      width: 1,),
                  ),
                  child: Container(
                    margin: const EdgeInsets.only(top: 5,left: 5),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(EmailReceiptString.enterCustomerEmail, style: AppTextStyle.smallTextStyle.copyWith(
                          fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                          color: ColorConstant.colorBlueDark,
                        ),),
                        const SizedBox(height: 15,),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: TextField(
                                  showCursor: true,
                                  keyboardType: TextInputType.emailAddress,
                                  maxLines: 1,
                                  cursorColor: ColorConstant.colorBlueDark,
                                  controller: _emailController,
                                  style: AppTextStyle.largeTextStyle.copyWith(
                                    fontSize: isBigScreenResolution(context) ? 18.0 : 12.0,
                                    letterSpacing: 0.5,
                                    color:  ColorConstant.colorBlueDark,
                                  ),
                                  onChanged: (value){

                                  },
                                  //textDirection: TextDirection.rtl,
                                  decoration: InputDecoration(
                                    filled: false,
                                    border: InputBorder.none,
                                    hintText: EmailReceiptString.sampleEmailHint,
                                    contentPadding: EdgeInsets.zero,
                                    hintStyle: AppTextStyle.largeTextStyle.copyWith(
                                      color: ColorConstant.colorBlueLight_50,
                                      letterSpacing: 0.5,
                                      fontSize: isBigScreenResolution(context) ? 18.0 : 12.0,
                                    ),
                                  )
                              ),
                            ),
                          ],
                        ),

                      ],
                    ),
                  ),
                ),

                if(value.text.isNotEmpty && validateEmail(value.text) )...[
                Container(
                  margin: const EdgeInsets.only(left: 15, top:5,bottom: 10,),
                  child: Text(EmailReceiptString.theEmailAddressIisCorrect, style: AppTextStyle.smallTextStyle.copyWith(
                    color: ColorConstant.buttonBgColor,
                    fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
                  ),),
                ),
                ],

              /*  Container(
                  margin:  EdgeInsets.only(top: isBigScreenResolution(context) ? 25 : 15,),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Checkbox(
                        visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        checkColor: Colors.white,
                        activeColor: ColorConstant.buttonBgColor,
                        value: false,
                        onChanged:  (bool? value) {

                        },
                        side: BorderSide(color: Colors.white),
                      ),
                      SizedBox(width: isBigScreenResolution(context) ? 10 : 5,),
                      Text(
                        EmailReceiptString.OPT_IN_TO_RECEIVE_NEW_OFFERS,
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: AppFonts.ROBOTO,
                          fontSize: isBigScreenResolution(context)
                              ? 14.0
                              : 12.0,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),*/
                Container(
                  margin:  EdgeInsets.only(top: isBigScreenResolution(context) ? 30 : 25,
                    bottom: isBigScreenResolution(context) ? 5 : 2.5,),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: (value.text.isNotEmpty && validateEmail(value.text)) ?(){
                            widget.sendButtonClick!(value.text.trim());
                          }: null,
                          child: Container(
                            margin: EdgeInsets.only(left:isBigScreenResolution(context) ? 50:25 , right: isBigScreenResolution(context) ?50:25),
                            padding:  EdgeInsets.symmetric(
                                vertical: isBigScreenResolution(context) ? 10.0 : 8.0),
                            decoration: BoxDecoration(
                                color: value.text.isNotEmpty ?  validateEmail(value.text) ?  ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_16 : ColorConstant.colorBlueLight_16,
                                borderRadius: BorderRadius.circular(5.0)),
                            child: Text(
                              EmailReceiptString.send.toUpperCase(),
                              textAlign: TextAlign.center,
                              style: AppTextStyle.largeTextStyle.copyWith(
                                fontSize: isBigScreenResolution(context) ? 16.0 : 12.0,
                                letterSpacing: 0.5,
                                color:  value.text.isNotEmpty ? validateEmail(value.text) ?  ColorConstant.colorThemeWhite :ColorConstant.colorBlueDark  : ColorConstant.colorBlueDark,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// validate email
  bool validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? false
        : true;
  }
}

