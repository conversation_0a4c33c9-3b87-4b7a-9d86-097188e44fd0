import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/providers/notification/notification_provider.dart';
import 'package:provider/provider.dart';

import '../constants/app_color.dart';
import '../constants/app_string.dart';
import '../providers/homescreen/bottom_sheet_provider.dart';
import '../providers/homescreen/home_screen_provider.dart';
import '../utils/app_routes.dart';
import '../utils/app_utils.dart';


class NavigationMenu extends StatefulWidget {
  final Function(String)? onMenuSelected; // Define the callback

  NavigationMenu({this.onMenuSelected}); // Constructor to accept callback

  @override
  State<NavigationMenu> createState() => _NavigationMenuState();
}

class _NavigationMenuState extends State<NavigationMenu> {
  @override
  void initState() {
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstant.colorBlueDark,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _navBarItem(Icons.home_outlined, Icons.home_filled, BottomBarNavigationString.home),
          _navBarItem(Icons.receipt_outlined, Icons.receipt_sharp, BottomBarNavigationString.order),
          _navBarItem(Icons.add_circle_outline, Icons.add_circle_sharp, BottomBarNavigationString.more),
          _navBarItem(Icons.access_time_outlined, Icons.access_time_filled, BottomBarNavigationString.history),
          _navBarItem(Icons.notifications_active_outlined, Icons.notifications_active, BottomBarNavigationString.alerts),
        ],
      ),
    );
  }

  Widget _navBarItem(IconData icon, IconData iconSelected, String label) {
    return InkWell(
      onTap: () {
        if (context.read<HomeScreenProvider>().activeMenuName != label || context.read<HomeScreenProvider>().activeMenuName == BottomBarNavigationString.more) {
          setState(() {
            context.read<HomeScreenProvider>().setActiveMenuName(label); // Update the active menu when it's tapped
            _onMenuSelected(label); // Call the callback for the selected menu
          });
        }else {
          debugPrint("$label is clicked Navigator.canPop(context) ${Navigator.canPop(context)}");
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
        }
      },
      splashColor: Colors.white24,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.only(left:isBigScreenResolution(context)?18:14,
                                 right:isBigScreenResolution(context)?18:14,
                                  top: 7, bottom:20),
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if(label == BottomBarNavigationString.alerts)...[
              Badge(
                isLabelVisible: context.watch<NotificationProvider>().totalNotifications > 0 ? true : false,
                label: Text("${context.watch<NotificationProvider>().getTotalNotifications()}"),
                child: Icon(
                  (context.read<HomeScreenProvider>().activeMenuName == label) ? iconSelected : icon, // Use selected icon if active
                  size: 28,
                  color: context.read<HomeScreenProvider>().activeMenuName == label ? Colors.white : ColorConstant.colorWhiteLight,
                ),
              ),
            ]else...[
              Icon(
                (context.read<HomeScreenProvider>().activeMenuName == label) ? iconSelected : icon, // Use selected icon if active
                size: 28,
                color: context.read<HomeScreenProvider>().activeMenuName == label ? Colors.white : ColorConstant.colorWhiteLight,
              ),
            ],
            SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: context.read<HomeScreenProvider>().activeMenuName == label ?FontWeight.w700 : FontWeight.w500,
                color: (context.read<HomeScreenProvider>().activeMenuName == label) ? Colors.white : ColorConstant.colorWhiteLight, // Text color
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Calls the provided callback and navigates when a new menu is selected
  void _onMenuSelected(String menu) {
    widget.onMenuSelected!(menu);
  }

  // // Programmatically select a tab
  void selectTab(String label) {
    if (context.read<HomeScreenProvider>().activeMenuName != label) {
      setState(() {
        context.read<HomeScreenProvider>().setActiveMenuName(label);  // Update the active menu when selected programmatically
        _onMenuSelected(label); // Call the callback method
      });
    }
  }
}