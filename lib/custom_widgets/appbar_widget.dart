import 'package:flutter/material.dart';
import '../constants/app_color.dart';
import '../utils/app_routes.dart';
import '../utils/app_utils.dart';
import 'app_text_style.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final BuildContext context;
  final String appBarTitleText;
  final bool backButtonEnabled;

  const CustomAppBar({
    super.key,
    required this.context,
    this.appBarTitleText = '',
    this.backButtonEnabled = true
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        appBarTitleText,
        style: AppTextStyle.mediumTextStyle.copyWith(
          fontSize: isBigScreenResolution(context) ? 18.0 : 16.0,
        ),
      ),
      automaticallyImplyLeading: backButtonEnabled,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
