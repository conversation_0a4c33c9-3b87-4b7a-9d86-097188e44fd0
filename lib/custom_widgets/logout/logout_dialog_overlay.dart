import 'package:flutter/material.dart';

import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../utils/app_utils.dart';
import '../app_text_style.dart';
import '../common_widget.dart';

class LogOutDialogOverlay extends StatefulWidget {

  final VoidCallback? onPositiveButtonClick;
  final VoidCallback? onNegativeButtonClick;

  const LogOutDialogOverlay({
    Key? key,
    required this.onPositiveButtonClick,
    required this.onNegativeButtonClick,
  }) : super(key: key);

  @override
  State<LogOutDialogOverlay> createState() => _LogOutDialogOverlayState();
}

class _LogOutDialogOverlayState extends State<LogOutDialogOverlay> {

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: isBigScreenResolution(context) ? 400.0 : 350.0,
          height: isBigScreenResolution(context) ? 220.0 : 180.0,
          decoration: BoxDecoration(
            color: ColorConstant.colorThemeWhite,
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: EdgeInsets.symmetric(vertical: isBigScreenResolution(context) ? 16.0: 14.0,),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Text(
                        LogoutDialogOverLayString.logoutTitle,
                        textAlign: TextAlign.center,
                        style: AppTextStyle.largeTextStyle.copyWith(
                          fontSize: isBigScreenResolution(context) ? 20.0 : 18.0,
                          letterSpacing: 0.3,
                          color: ColorConstant.colorBlueDark,
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(right: 10.0),
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: closeIcon(24.0,color: ColorConstant.colorBlueDark),
                      ),
                    ),
                  ],
                ),
              ),
              Divider(height: 1.0,color: ColorConstant.colorBlueLight_16,),
              Expanded(
                child: Container(
                  width: isBigScreenResolution(context) ? 400.0 : 300.0,
                  height: 100.0,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            LogoutDialogOverLayString.logoutMessage,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.smallTextStyle.copyWith(
                              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
                              letterSpacing: 0.3,
                              color: ColorConstant.colorBlueDark,
                            ),
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(bottom: 14.0,),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: widget.onPositiveButtonClick,
                              child: Container(
                                width: isBigScreenResolution(context) ? 170.0 : 140.0,
                                height: 50.0,
                                padding: EdgeInsets.symmetric(
                                  vertical: 10.0,
                                  horizontal: isBigScreenResolution(context) ? 16.0 : 8.0,
                                ),
                                margin: EdgeInsets.only(
                                  top: 16.0,
                                ),
                                child: Center(
                                  child: Text(
                                    LogoutDialogOverLayString.yes,
                                    style: AppTextStyle.mediumTextStyle.copyWith(
                                      color: ColorConstant.colorThemeWhite,
                                      fontSize: isBigScreenResolution(context)
                                          ? 14.0
                                          : 12.0,
                                      letterSpacing: 2.0,
                                    ),
                                  ),
                                ),
                                decoration: BoxDecoration(
                                  color: ColorConstant.colorRedDark,
                                  borderRadius: BorderRadius.circular(
                                    4.0,
                                  ),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: widget.onNegativeButtonClick,
                              child: Container(
                                width: isBigScreenResolution(context) ? 170.0 : 140.0,
                                height: 50.0,
                                padding: EdgeInsets.symmetric(
                                  vertical: 10.0,
                                  horizontal: isBigScreenResolution(context) ? 16.0 : 8.0,
                                ),
                                margin: EdgeInsets.only(
                                  left: 16.0,
                                  top: 16.0,
                                ),
                                child: Center(
                                  child: Text(
                                    LogoutDialogOverLayString.no,
                                    style: AppTextStyle.mediumTextStyle.copyWith(
                                      color: ColorConstant.colorThemeWhite,
                                      fontSize: isBigScreenResolution(context)
                                          ? 14.0
                                          : 12.0,
                                      letterSpacing: 2.0,
                                    ),
                                  ),
                                ),
                                decoration: BoxDecoration(
                                  color: ColorConstant.colorBlueDark,
                                  borderRadius: BorderRadius.circular(
                                    4.0,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
