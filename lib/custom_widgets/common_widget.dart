import 'package:flutter/material.dart';
import '../constants/app_color.dart';
import '../utils/app_utils.dart';
import 'app_text_style.dart';

Widget commonButton({
  required String labelText,
  required onPressed,
  required BuildContext context,
  fontWeight = FontWeight.w500,
  Color bgColor = ColorConstant.colorBlueDark,
  bool isEnable = true,
}) {
  return GestureDetector(
    onTap: onPressed,
    child: Container(
      width: double.infinity,
      margin: EdgeInsets.only(
          top: isBigScreenResolution(context) ? 20.0 : 18.0,
          bottom: isBigScreenResolution(context) ? 16.0 : 14.0),
      padding: EdgeInsets.symmetric(
          horizontal: isBigScreenResolution(context) ? 15.0 : 13.0,
          vertical: isBigScreenResolution(context) ? 10.0 : 8.0),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(4)
      ),
      child: Text(
        labelText,
        style: TextStyle(
          color: ColorConstant.colorThemeWhite,
          fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
          fontWeight: fontWeight,
          decoration: TextDecoration.none,
        ),
        textAlign: TextAlign.center,
      ),
    ),
  );
}

Widget alertCommonText({
  required String labelText,
  color = ColorConstant.colorBlueDark,
  double fontSize = 14.0,
  fontWeight = FontWeight.normal,
  textAlign = TextAlign.start,
}) {
  return Text(
    labelText,
    textAlign: textAlign,
    style: TextStyle(
      color: color,
      fontWeight: fontWeight,
      fontSize: fontSize,
    ),
  );
}

Widget commonDialogHeader(
    {required String title, required VoidCallback? onPressed}) {
  return Stack(
    alignment: Alignment.topRight,
    children: [
      SizedBox(
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.only(left: 24.0, right: 24.0),
          child: commonText(
              textAlign: TextAlign.center,
              labelText: title,
              fontSize: 20.0,
              fontWeight: FontWeight.w700),
        ),
      ),
      IconButton(
        padding: const EdgeInsets.only(bottom: 20),
        constraints: const BoxConstraints(),
        icon: const Icon(Icons.close, color: ColorConstant.colorBlueDark),
        onPressed: onPressed,
      )
    ],
  );
}

Widget commonText({
  required String labelText,
  color = ColorConstant.colorBlueDark,
  double fontSize = 14.0,
  fontWeight = FontWeight.normal,
  textAlign = TextAlign.start,
}) {
  return Text(
    labelText,
    textAlign: textAlign,
    style: TextStyle(
      color: color,
      fontWeight: fontWeight,
      fontSize: fontSize,
    ),
  );
}

/// custom bottom app bar
Widget  customBottomAppBar({
   required BuildContext context,
   String rightLabelValue='',
   String leftLabelValue='',
   VoidCallback? rightButtonPressed,
   VoidCallback? leftButtonPressed,
   VoidCallback? actionButtonPressed,
   bool enableRightButton = true,
   bool enableLeftButton = true,
   bool enableBottomSheet = false,
   bool visibleOptionButton = true,
}){
  return  Row(
    mainAxisSize: MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.start,
    crossAxisAlignment: CrossAxisAlignment.end,
    children: [
      Expanded(child: GestureDetector(
        onTap: leftButtonPressed,
        behavior: HitTestBehavior.opaque,
        child: Container(
          decoration:   BoxDecoration(
            color: enableLeftButton ?ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_16,
            shape: BoxShape.rectangle,
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(75),
              bottomRight: Radius.circular(-5),
            ),
          ),
          padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context)? 14 : 12,),

          child: _bottomBarLabel(context ,leftLabelValue, color:enableLeftButton ?
          ColorConstant.colorThemeWhite: ColorConstant.colorBlueDark,),),
      ),),
      if(visibleOptionButton)...[
        GestureDetector(
          onTap: actionButtonPressed,
          child: Container(
            width: 50.0,
            height: 50.0,
            margin: const EdgeInsets.symmetric(horizontal: 5,vertical: 5,),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: ColorConstant.colorBlueDark,
            ),
            child: Icon(enableBottomSheet?Icons.keyboard_arrow_down : Icons.keyboard_arrow_up , color: ColorConstant.colorThemeWhite,),
          ),
        ),

      ]else...[
        SizedBox(width: 10,),
      ],
      Expanded(child: GestureDetector(
        onTap: rightButtonPressed,
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: isBigScreenResolution(context)? 14 : 12,),
          decoration:  BoxDecoration(
            color: enableRightButton ?ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_16,
            shape: BoxShape.rectangle,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(75),
              bottomLeft: Radius.circular(-5),
            ),
          ),
          child: _bottomBarLabel(context, rightLabelValue,color:enableRightButton ?
          ColorConstant.colorThemeWhite: ColorConstant.colorBlueDark,),),
      ),),
    ],
  );
}

/// bottom bar labels
Widget _bottomBarLabel(BuildContext context, String label,
    { Color color=ColorConstant.colorThemeWhite }){
  return Text(
    label.toUpperCase(),
    textAlign: TextAlign.center,
    style: AppTextStyle.smallTextStyle.copyWith(
      fontSize: isBigScreenResolution(context) ? 14.0 : 12.0,
      color: color,
    ),
  );
}

Widget closeIcon(double size, {Color? color}){
  return Icon(
    Icons.close,
    color: color,
    size: size,
  );

}