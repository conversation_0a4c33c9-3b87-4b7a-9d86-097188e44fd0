import 'package:flutter/material.dart';
import '../constants/app_color.dart';
import '../constants/app_string.dart';
import '../utils/app_utils.dart';

import '../providers/alerts/alerts_provider.dart';
import 'common_widget.dart';

AlertDialog getAlertDialog(AlertModel alertData, void Function() onDismiss, BuildContext context) {
  return AlertDialog(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(10.0),
    ),
    elevation: 0,
    title: commonDialogHeader(
      title: alertData.title,
      onPressed: onDismiss,
    ),
    content: SingleChildScrollView(
      child: ListBody(
        children: [
          alertCommonText(
            labelText: alertData.message,
            textAlign: TextAlign.center,
            fontSize: 15.0,
          ),
          SizedBox(
            height: isBigScreenResolution(context) ? 30 : 28,
          ),
          commonButton(
            labelText: alertData.alertButtonTitle!,
            onPressed: alertData.alertOnPressed ?? onDismiss,
            context: context,
          )
        ],
      ),
    ),
  );
}

AlertDialog getConfirmationDialog(
    AlertModel alertData, {required VoidCallback? onYes, required VoidCallback? onNo}) {
  return AlertDialog(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(10.0),
    ),
    elevation: 0,
    title: alertCommonText(
      textAlign: TextAlign.center,
      labelText: alertData.title,
      fontSize: 20.0,
      fontWeight: FontWeight.w700,
    ),
    content: alertCommonText(
      labelText: alertData.message,
      textAlign: TextAlign.center,
      fontSize: 15.0,
    ),
    actions: [
      TextButton(
        onPressed: () => onNo,
        child: const Text(ConstantString.no,style: TextStyle(color: ColorConstant.colorBlueDark),),
      ),
      TextButton(
        onPressed: () => onYes,
        child: const Text(ConstantString.yes,style: TextStyle(color: ColorConstant.colorRedDark),),
      ),
    ],
  );
}
