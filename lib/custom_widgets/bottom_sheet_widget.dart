import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../constants/app_color.dart';
import '../constants/app_string.dart';
import 'app_text_style.dart';
import 'common_widget.dart';

class BottomSheetContent extends StatelessWidget {
  final Map<String, bool> menuItems;
  final Function(String) onMenuItemSelected;

  BottomSheetContent({required this.menuItems, required this.onMenuItemSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstant.colorThemeWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0),),
        border: Border(
          top: BorderSide(width: 2.0, color: Colors.grey.shade400),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: menuItems.entries.map((e) {
            return Container(
              margin: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(child: _menuButton(context, e.key, e.value)),
                    ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _menuButton(BuildContext context, String title, bool isFilled) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3.0),
      child: GestureDetector(
        onTap: () {
          onMenuItemSelected(title);
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isFilled ? ColorConstant.colorBlueDark : ColorConstant.colorBlueLight_16,
            // borderRadius: BorderRadius.circular(4.0),
            // border: Border.all(color: ColorConstant.colorGrayLight),
          ),
          child: Center(
            child: Text(
              title.toUpperCase(),
              style: AppTextStyle.smallBottomSheetTextStyle.copyWith(
                color: isFilled ? ColorConstant.colorThemeWhite : ColorConstant.colorBlueDark,
              ),
            ),
          ),
        ),
      ),
    );
  }
}




