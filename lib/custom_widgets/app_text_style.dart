import 'package:flutter/material.dart';

import '../constants/app_color.dart';

extension AppTextStyle on TextStyle {
  static TextStyle get smallTextStyle => const TextStyle(
      color: ColorConstant.colorBlueDark,
      fontWeight: FontWeight.w400,
      fontSize: 16);

  static TextStyle get mediumTextStyle => const TextStyle(
      color: ColorConstant.colorBlueDark,
      fontWeight: FontWeight.w500,
      fontSize: 20);

  static TextStyle get largeTextStyle => const TextStyle(
      color: ColorConstant.colorBlueDark,
      fontWeight: FontWeight.w700,
      fontSize: 24);

  static TextStyle get smallBottomSheetTextStyle => const TextStyle(
      color: ColorConstant.colorBlueDark,
      fontWeight: FontWeight.w400,
      fontSize: 14);

}
