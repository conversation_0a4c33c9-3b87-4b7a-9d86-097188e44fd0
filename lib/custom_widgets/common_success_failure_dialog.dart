import 'package:flutter/material.dart';

import '../constants/app_color.dart';
import '../utils/app_utils.dart';
import 'app_text_style.dart';

class CustomSuccessFailureDialog extends StatelessWidget {

  String message, description;
   bool hasError;
   CustomSuccessFailureDialog({this.message='', this.description='', this.hasError =false});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if(hasError) ...[
            Icon(Icons.warning_sharp,
              color: ColorConstant.colorDarkYellow,size: 60.0,),
          ],
          if(!hasError) ...[
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                  color: ColorConstant.successColor,
                  borderRadius: BorderRadius.circular(60)
              ),
              child: Icon(Icons.check_sharp,
                color: Colors.white, size: 35,),
            ),

          ],
          const SizedBox(height: 20,),
          Row(
            children: [
              Expanded(
                child: Text(
                  message,
                  textAlign: TextAlign.center,
                  style: AppTextStyle.largeTextStyle.copyWith(
                    fontSize: isBigScreenResolution(context) ? 18.0 : 16.0,
                    letterSpacing: 0.5,
                    color: ColorConstant.colorBlueDark,
                  ),
                ),
              ),
            ],
          ),
          if(description.isNotEmpty) ...[
            const SizedBox(height: 7,),
          Text(
            description,
            style: AppTextStyle.smallTextStyle.copyWith(
              fontSize: isBigScreenResolution(context) ? 16.0 : 14.0,
              color: ColorConstant.colorBlueDark,
            ),
          ), ],

        ],
      ),
    );
  }
}
