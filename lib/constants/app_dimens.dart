import 'package:flutter/cupertino.dart';

class WidthDimens{
    getWidth2() => SizedBox(width: 2,);
    getWidth3() => SizedBox(width: 3,);
    getWidth4() => SizedBox(width: 4,);
    getWidth5() => SizedBox(width: 5,);
    getWidth7() => SizedBox(width: 7,);
    getWidth8() => SizedBox(width: 8,);
    getWidth10() => SizedBox(width: 10,);
    getWidth12() => SizedBox(width: 12,);
    getWidth15() => SizedBox(width: 15,);
    getWidth16() => SizedBox(width: 16,);
    getWidth20() => SizedBox(width: 20,);
    getWidth25() => SizedBox(width: 25,);
    getWidth60() => SizedBox(width: 60,);
}

class HeightDimens{
    getHeight3() =>  SizedBox(height: 3,);
    getHeight4() =>  SizedBox(height: 4,);
    getHeight5() =>  SizedBox(height: 5,);
    getHeight6() =>  SizedBox(height: 6,);
    getHeight7() =>  SizedBox(height: 7,);
    getHeight8() =>  SizedBox(height: 8,);
    getHeight10() =>  SizedBox(height: 10,);
    getHeight12() =>  SizedBox(height: 12,);
    getHeight15() =>  SizedBox(height: 15,);
    getHeight16() =>  SizedBox(height: 16,);
    getHeight20() =>  SizedBox(height: 20,);
    getHeight22() =>  SizedBox(height: 22,);
    getHeight24() =>  SizedBox(height: 24,);
    getHeight25() =>  SizedBox(height: 25,);
    getHeight30() =>  SizedBox(height: 30,);
    getHeight50() =>  SizedBox(height: 50,);
    getHeight60() =>  SizedBox(height: 60,);
    getHeight200() =>  SizedBox(height: 200,);
}