class ApiConstant {
  static const  String PLATFORM_VALUE = "app";
  /// Body parameters keys for a auth module
  static const String USERNAME = "username";
  static const String PASSWORD = "password";
  static const String EMAIL = "email";
  static const String BUSINESS_ID = "businessId";
  static const String LOCATION_ID = "locationId";
  static const String INCLUDE_VALUE = "1";
  static const String BEARER = "Bearer ";

  static const String INCLUDE_TABLES = "includeTables";
  static const String INCLUDE_CART = "includeCart";
  static const String STATUS_TRUE = "true";
  static const String ORDER_RECEIVE_METHOD = "orderReceiveMethod";
  static const String PAGE = "page";
  static const String PER_PAGE = "perPage";
  static const String PER_PAGE_VALUE = "16";
  static const String STATUSES = "statuses";
  static const String SEARCH = "search";
  static const String ID = "id";
  static const String CART_ITEM_IDS = "cartItemIds";

  static const String SEARCH_KEYWORD = "searchKeyword";
  static const String TAGS = "tags";
  static const String ITEM_IDS = "itemIds";
  static const String CART_ID = "cartId";
  static const String NAME = "name";
  static const String USER_ID = "userId";
  static const String PHONE = "phone";

  static const  String ADDED_BY_VALUE = "server";
  static const String ADDED_BY = "addedBy";
  static const String ADDED_BY_ID = "addedById";
  static const String PLATFORM = "platform";
  static const String PAY_LATER = "payLater";
  static const String RECEIVE_LATER = "receiveLater";
  static const String PAYMENT_RECEIVED = "paymentReceived";
  static const String STATUS= "status";
  static const String STATUS_PENDING = "pending";
  static const String PLATFORM_VALUE_POS = "pos";
  static const String TABLE_ID = "tableId";
  static const String DELIVERY = 'delivery';

  static const String REASON = "reason";

  static const String TOTAL= "total";
  static const String PAYMENT_TYPE = "paymentType";
  static const String CARD_TYPE = "cardType";

  static const String DEVICE_ID = "deviceId";

  static const String VALUE_TYPE_FLAT = "flat";
  static const String VALUE_TYPE_PERCENTAGE = "percentage";
  static const String TYPE = "type";
  static const String VALUE = "value";

  static const String LIMIT = "limit";
  static const String LIMIT_VALUE = "5";

  static const String ITEM_IDs = "itemIds";

  static const String TIMEZONE = "timezone";

  static const String STATUSES_VALUES = "processed,refunded,cancelled";// completed,refunded,cancelled
  static const String SORT = "sort";
  static const String SORT_BY = "sortBy";
  static const String SORT_ASC = "asc";
  static const String SORT_DESC = "desc";
  static const String SORT_LOCATION_NAME = "locationName";
  static const String SORT_CREATED_AT = "createdAt";
  static const String SORT_ITEM_COUNT = "itemCount";
  static const String SORT_TOTAL = "total";
  static const String SORT_ID = "id";

  /// apply promo code, discount
  static const String USERTYPE = "userType";
  static const String PROMO_CODE = "promoCode";
  static const String USERTYPE_GUEST = "guest";
  static const String USERTYPE_REGISTERED = "registered";

  static const String PERMISSIONS = "permissions";
  static const List<String> PERMISSIONS_VALUE = ["discount_capabilities"];
  static const String DISCOUNT_ID = "discountId";

  /// Custom discount
  static const String USER_TYPE = "userType";
  static const String NOTE = "note";
  static const String VALUE_TYPE = "valueType";
  static const String APPLIED_VALUE = "appliedValue";
  static const String APPLY_ON = "applyOn";
  static const String CART_ITEM_ID = "cartItemId";
  static const String DISCOUNTS = "discounts";
  static const String APPLY_ON_CART = "cart";
  static const String APPLY_ON_PRODUCT = "product";
  static const String QTY = "qty";

  // Notification
  static const String DEVICE_TYPE = "deviceType";
  static const String DEVICE_TOKEN = "deviceToken";
  static const String API_TOKEN = "apiToken";
  static const String SORT_BY_CREATED_AT = "sortByCreatedAt";
  static const String IS_READ = "isRead";
  static const String TOPIC_KEY = "topicKey";
  static const String CART_ID_NOTIFICATION = "cartId";
  static const String TABLE_ID_NOTIFICATION = "tableId";

  static const String DATE = "date";

  static const String APPROVED = "approved";
  static const String DISCOUNT_TYPE = "discountType";
  static const String DISCOUNT_DECLINE_NOTE = "note";


}