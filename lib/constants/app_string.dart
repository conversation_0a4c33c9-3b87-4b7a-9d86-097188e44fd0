class LoginViewString {
  static const String email = "Email";
  static const String password = "Password";
  static const String login = "LOGIN";
  static const String forgotPassword = "FORGOT PASSWORD";
}

class ResetPasswordViewString {
  static const String resetPassword = "Reset Password";
  static const String email = "Email";
  static const String resetPasswordMessage =
      "Enter the email, to get a verification code to\nreset your password";
  static const String submit = "SUBMIT";
}

class AppFonts {
  static const String roboto = "Roboto";
}

class AssetIcons {
  static const String assetRoute = 'assets/icons/';
  static const String iconLogo = "${assetRoute}ic_logo.png";
  static const String iconDishFill = "${assetRoute}dish_fill.png";
  static const String iconPeople = "${assetRoute}people.png";
  static const String pickup = "${assetRoute}pickup.png";
  static const String delivery = "${assetRoute}delivery.png";
  static const String guestIcon = "${assetRoute}icon_guest.png";
  static const String bottomCut = "${assetRoute}bottom_cut.png";
}

class AssetImages {
  static const String assetRoute = 'assets/images/';
  static const String pickup = "${assetRoute}pickup.png";
  static const String loyalty_image = "${assetRoute}loyalty.png";
  static const String inStore = "${assetRoute}inStore.png";
  static const String onlinePurchase = "${assetRoute}online_purchase.png";
  static const String refund = "${assetRoute}refund.png";
  static const String delivery = "${assetRoute}delivery.png";
  static const String dish_fill = "${assetRoute}dish_fill.png";
  static const String loyaltyImage = "${assetRoute}loyalty.png";
  static const String noImage = "${assetRoute}no_image.png";
  static const String note = "${assetRoute}note.png";
}

class ConstantMessages {
  static const String enterEmail = "Please enter an email address.";
  static const String enterValidEmail = "Please enter a valid email address.";

  static const String enterPassword = "Please enter a password.";
  static const String enterValidPassword = "Please enter a valid password.";
}

class ConstantString {
  static const String error = "Error";
  static const String internetError =
      "Error connecting to the internet. Please check your internet connection, and try again.";
  static const String ok = "Ok";
  static const String yes = "Yes";
  static const String no = "No";
  static const String cancel = "Cancel";
  static const String success = "Success";
  static const String platformWeb ="web";
  static const String unauthorized = "unauthorized";

}

class AuthString {
  static const String errorBusinessNotFound = "Logging out, No business found. Please contact support!.";
  static const String errorLocationNotFound = "Logging out, No location available for the user.";
}

class BusinessLocationString{
  static const String selectBusiness= 'Select business';
  static const String selectLocation= 'Select location';
}

class ConfirmationString{
  static const String areYouSure = "Are you sure?";
  static const String exitAnApp = "Do you want to exit an App";
  static const String yes = "Yes";
  static const String no = "No";
}

class HomeScreenString{
  static const String homeScreen= 'Home Screen';
}

class BottomMenuString{
  static const String back= 'Back';
  static const String servers= 'Servers';
  static const String addTakeout= 'Add Takeout';
  static const String addItem= 'Add Item';
  static const String bill= 'Bill';
  static const String reprint= 'Reprint';
  static const String cancel= 'Cancel';
  static const String send_to_kitchen= 'send to kitchen';
}

class BottomBarNavigationString{
  static const String home= 'Home';
  static const String order= 'Order';
  static const String more= 'More';
  static const String history= 'History';
  static const String alerts= 'Alerts';
}

class NavigationString{
  static const String homeView= 'Home';
  static const String orderView= 'Orders';
  static const String cartView= 'Cart';
  static const String menuView= 'Menu';
  static const String menuBill= 'Bill';
  static const String menuCash= 'Cash';
  static const String customerDetailsView= 'Customer Details';
}

class BottomSheetString {
  static const String payUsingCash = 'Pay Using Cash';
  static const String payUsingNonIntegrated = 'Pay Using Non Integrated';
  static const String applyPromo = 'Apply Promo';
  static const String applyCustomDiscount = 'Apply Custom Discount';
  static const String applyPresetDiscount = 'Apply Preset Discount';
  static const String voidItem = 'VOID ITEM';
  static const String clearAllItem = 'CANCEL';
  static const String printReceipt = 'Print Receipt';
  static const String printKot = 'Print Kot';
  static const String addServiceCharge = 'Add Service Charge';
  static const String removeServiceCharge = 'Remove Service Charge';
  static const String logout = 'Log Out';
  static const String closeBill = 'Close Bill';
  static const String addTips = 'Add tips';
  static const String removeTips = 'Remove tips';
  static const String addItems = 'Add Items';
  static const String sendToKitchen= 'send to kitchen';
  static const String enterReason= 'ENTER REASON';
}

class LogoutDialogOverLayString {
  static const String logoutTitle = "Log Out";
  static const String logoutMessage = "Are you sure you want to log out?";
  static const String yes = "Yes";
  static const String no = "No";
}

class DineInTakeoutString{
  static const String tabDineIn= 'Dine in';
  static const String tabTakeout= 'Takeout';

  static const String noTablesYet= 'No tables yet.';
  static const String noOrdersYet= 'No orders yet.';

  static const String orderId ="Order #";
  static const String placedDate = 'Placed @ %s';
  static const String totalItems = '(%d items)';
  static const String paid = "Paid";
  static const String unpaid = "Unpaid";
  static const String asap = "ASAP";
}

class OrderReceiveMethod {
  static const String pickup = "pickup";
  static const String delivery = "delivery";
  static const String dineIn = "dinein";
}

class OrderStatus {
  static const String pending = "pending";
  static const String placed = "submitted";
  static const String outForDelivery = "out for delivery";
  static const String ready = "ready";
  static const String cancelled = "cancelled";
  static const String refunded = "refunded";
  static const String processed = "processed";
}

class CartViewString{
  static const String noActiveCartYet= 'No active cart yet.';
  static const String subTotal= 'Subtotal';
  static const String newSubTotal = 'New Subtotal:';
  static const String tax= 'Tax';
  static const String pendingOrder= 'Pending order';
  static const String cartIsEmpty= 'Cart is empty. top some water and talk to the guest';
  static const String submittedOrder= 'Submitted order';
  static const String billIsEmpty= 'Bill is empty.';
  static const String note= 'Note:';
  static const String deleteItem= 'Delete item';
  static const String errorPleaseSelectItemToVoid = "Please select item(s) to void";
  static const String errorNoItemsToVoid = "No Items to void";
  static const String errorPendingItem = "You have must pending items send to kitchen";
  static const String otherPayment = 'Other Payment';
  static const String amount = "Amount";
  static const String total = "Total";
  static const String remaining = "Remaining";
  static const String  tip = 'Tip:';
  static const String serviceCharge = "Service charge:";
  static const String code = "Code";
  static const String preset = "Preset";
  static const String custom = "Custom";
  static const String discount = "Discount";
  static const String customDiscount = "Custom Discount";
  static const String pointsRedeemed = "Points Redeemed";
  static const String processed = "Processed";
}

class EmailReceiptString{
  static const String enterCustomerEmail = "Enter customer email address to send receipt";
  static const String emailReceipt = "Email Receipt";
  static const String sampleEmailHint = "<EMAIL>";
  static const String theEmailAddressIisCorrect = "The email address is correct.";
  static const String send = "Send";
}

class PaymentString{
  static const String totalItems = "%d items";
  static const String amountReceived = "Amount Received:";
  static const String cartTotalBoxSubTotal = "Subtotal:";
  static const String back = 'BACK';
  static const String continueLabel = "CONTINUE";
  static const String newSubTotal = "New Subtotal:";
  static const String tax = "Tax:";
  static const String paidAmount = "Already paid:";
  static const String dueAmount = "Due Amount:";
  static const String email = "EMAIL";
  static const String text = "TEXT";
  static const String print = "PRINT";
  static const String none = "NONE";
  static const String errorOrderAmount = "Please enter order amount.";
  static const String errorDueAmount = "Please pay the due amount.";
  static const String receivePayment = "Receive Payment";
  static const String errorOtherPayment = "Please select one of the payment.";
  static const String errorOrderAmountGreaterError = "Amount entered is too high.";
}

class PromotionsString {
  static const String code = "Code";
  static const String preset = "Preset";
  static const String custom = "Custom";
  static const String custom_discount = "Discount";
  static const String points = "Points";
  static const String reward = "Reward";
  static const String discountCannotGreaterThanPercentage = "Discount cannot greater than 100%.";
  static const String discountCannotGreaterThanTotal = "Discount cannot be greater than the cart total.";
  static const String discountCannotGreaterThanItemTotal = "Discount cannot be greater than the item total.";
}

class PresetDiscountString {
  static const String type= "Type";
  static const String value= "Value";
  static const String maxDiscount= "Max discount";
  static const String noDiscount= "No discounts yet.";
  static const String remove= "remove";
  static const String apply= "Apply";
  static const String na = "n/a";
  static const String description = "Description";
}

class PaymentType {
  static const String cash = "cash";
  static const String debit = "debit";
  static const String credit = "credit";
  static const String nonIntegrated = "non_integrated";
}

class CustomerOrderHistoryString{
  static const String tabFrequentlyOrder= 'Frequently ordered items';
  static const String tabPastOrdered= 'Past orders';
  static const String date ="Date";
  static const String orderId ="Order #";
  static const String location ="Location";
  static const String noOfItems ="# of Items";
  static const String amount ="Amount";
  static const String noItemYetErrorMessage ="No items yet.";
  static const String noOrderYetErrorMessage ="No orders yet.";
}

class AddServiceChargeViewString{
  static const String add ='Add';
  static const String serviceCharge = "Service charge";
  static const String serviceChargeNotGreaterThan100Percent = "Service charge cannot greater than 100%.";
}

class AddTipViewString{
  static const String add ='Add';
  static const String tip = "Tip";
  static const String tipNotGreaterThan100Percent = "Tip cannot greater than 100%.";
}

class ApplyPromoViewString{
  static const String promoCode = "Promo Code";
  static const String promoCodeHintLabel = "Enter a code here";
  static const String apply = "APPLY";
}

class MenuViewString{
  static const String noMenusYet= 'No menus yet.';
  static const String noItemsYet= 'No items yet.';
  static const String outOfStock= 'Out of stock';
}

class ItemModifierViewString {
  static const String selectUpToX = 'Select up to X';
  static const String selectAtLeastX = 'Select at least X';
  static const String notes = 'Notes:';
  static const String selectAtLeastXupToY = 'Select at least X, up to Y';
  static const String allergens = 'Allergens';
  static const String selectACourse = 'Select a course';
  static const String contains = 'CONTAINS:';
  static const String mayContain = 'MAY CONTAIN:';
  static const String addToOrder = 'Add to order';
}

class ItemCourseString{
  static const String now = 'Now';
  static const String first = 'First Course';
  static const String second = 'Second Course';
  static const String third = 'Third Course';
  static const String takeout = 'Takeout';
}

class CartUserSelectionContentString{
  static const String addItemTo = 'Add item to';
  static const String addGuest = 'Add Guest';
  static const String add = 'Add';
  static const String done = 'Done';
  static const String cancel = 'Cancel';
}

class VoidItemConfirmationViewString {

  static const String  header ='Void Item(s)';
  static const String  sub_header ='Enter a reason to void the item(s)';
  static const String  voidThisItem ='Void this item(s)';
  static const String  voidItemReasonHint ='Reason for void';
}

class PastOrderViewString {
  static const String order ='Order';
}

class HistoryViewString {
  static const String searchOrders = "Search by name or order number";
  static const String customer = "Customer";
  static const String order = "Order ID";
  static const String date = "Date";
  static const String loyaltyPoints = "Points";
  static const String earnedSpent = "Earned/Spent";
  static const String total = "Total";
  static const String status = "Status";
  static const String completed = "Completed";
  static const String cancelled = "Cancelled";
  static const String refunded = "Refunded";
  static const String partiallyRefunded = "Partially Refunded";
  static const String processed = "Processed";
}

class OrdersViewString {
  static const String searchOrders = "Search orders";
  static const String oldestFirst = "Oldest First";
  static const String newestFirst = "Newest First";
  static const String parked = "Parked";
  static const String pending = "Pending";
  static const String placed = "Placed";
  static const String ready = "Ready";
  static const String outForDelivery = "Out for Delivery";
  static const String paid = "Paid";
  static const String unpaid = "Unpaid";
  static const String completed = "Completed";
  static const String cancelled = "Cancelled";
  static const String refunded = "Refunded";
  static const String delivery = "Delivery";
  static const String scan = "Scan";
  static const String startNewOrder = "Start a new order";
  static const String hintTextOrder = "Tap on an order to see more info.";
  static const String errorOrderNotFound = "Order not found.";
}

class OrderDetailsString{
  static const String orderDetails = "Order Details";
  static const String email = "EMAIL";
  static const String print = "PRINT";
  static const String success = "Success";

}

class DiscountOptionString{
  static const String selectAnOption = "Select an option:";
  static const String optionCart = "Cart";
  static const String optionItem = "Item";
  static const String next = "Next";
}

class CartCustomDiscountString{
  static const String apply = "apply";
  static const String discountAmount= "Discount Amount";
  static const String redeemPoints = "Redeem";
  static const String newSubTotal = "New Subtotal:";
  static const String cartSummary = "Cart Summary";
  static const String subTotal = "Subtotal";
  static const String discount = "Discount";
  static const String totalSavings = "Total Savings: ";
  static const String membershipPoints = "Membership Points";
  static const String reward = "Reward";
  static const String selectAll = 'Select all';
  static const String preset = "Preset";
  static const String custom = "Custom";
  static const String code = "Code";
  static const String discountOption = "Discount Option";
  static const String optionCart = "Cart";
  static const String notes= "Notes";
  static const String describeReason= "Describe the reason in detail if you need to";
  static const String item= "item";
}

class ItemCustomDiscountString{
  static const String cartSummary = "Cart Summary";
  static const String custom = "Custom";
  static const String discount = "Discount";
}

class DiscountOptionViewString{
  static const String selectOption = "Select Option";
}

class AlertsString{
  static const String active = "Active";
  static const String history = "History";
}

class ActiveViewString{
  static const String emptyNotification = "Empty notification";
  static const String noHistoryFound = "No notification history found.";
  static const String newest = "Newest";
  static const String oldest = "Oldest";
  static const String clearAll = "Clear all";
  static const String errorUserDataNotFound = "No notification data found.";
}

class MethodChannelString{
  static const String permissionChannel = "notification/permission";
  static const String requestNotificationPermission = "requestNotificationPermission";
  static const String notificationChannel = "navigation_channel";
  static const String navigateToAlerts = "navigateToAlerts";
  static const String navigateToCart = "navigateToCart";
  static const String alerts = "alerts";
  static const String cartDetails = "cartDetails";
}

class NotificationsString{
  static const String pendingItems = "Pending items";
  static const String sentItems = "Sent items";
  static const String serverRequest = "server_request";
  static const String tableActive = "table_active";
  static const String discountApproval = "discount_approval";
  static const String bills = "bills";
  static const String viewTable = "VIEW TABLE";
  static const String viewOrder = "VIEW ORDER";
  static const String cancel = "CANCEL";
  static const String ok = "OK";
  static const String enableNotificationsTitle = "Enable Notifications";
  static const String enableNotificationsBody = "To receive notifications, please enable permissions in settings.";
  static const String enableIOSNotificationsBody = "To receive notifications, please enable permissions from app settings.\n\nSettings  > Notifications > Allow notification > Select app > Enable";
  static const String openSettings = "OPEN SETTINGS";
  static const String errorDialogTitle = "FCM Error";
  static const String errorDialogBody = "Please set your business and try again.";
  static const String approve = "Approve";
  static const String voidItem = "Void item";
  static const String deny = "Deny";
  static const String order = "Order";
  static const String items = "items";
  static const String customDiscount = "custom";
  static const String customDiscountType = "custom";
  static const String presetDiscountType = "preset";
  static const String noItems = "No Items.";
  static const String notifyAlertScreen = "notify_alert_screen";
  static const String notifyNoteHintText = "E.g Reason for cancelling the order, such as item was out of stock";
}