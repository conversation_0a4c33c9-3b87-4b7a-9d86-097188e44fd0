import 'package:flutter/material.dart';

class ColorConstant {
  static const Color colorThemeWhite = Colors.white;
  static const Color colorThemeGray = Color(0xFFF2F2F2);
  static const Color colorBlueDark = Color(0xFF000055);
  static const Color colorBlueLight_8 = Color(0x14000055);
  static const Color colorBlueLight_16 = Color(0x29000055);
  static const Color colorBlueLight_32 = Color(0x52000055);
  static const Color colorBlueLight_50 = Color(0x80000055);
  static const Color colorRedDark = Color(0xFFF63E4D);
  static const Color transparent = Color(0x00000000);
  static const Color blackColor = Colors.black;

  static const Color colorGreenLight =  Color(0xFFE6F7E6);
  static const Color colorRedLight = Color(0x2AF63E4D);
  static const Color colorDarkGreen_600 =  Color(0xFF01AB19);
  static const Color colorGreenLight_3 =  Color(0xFF6FCF97);
  static const Color buttonBgColor = Color(0xFF2BA66B);
  static const Color colorRedDark_5 =  Color(0xFFF63E4D);
  static const Color colorGrayLight = Color(0xFF7C8898);
  static const Color colorGrayDark = Color(0xFF23242A);
  static const Color colorOrangeLight =  Color(0xffFFCBBA);
  static const Color colorLightGray_5x =  Color(0xffE0E0E0);
  static const Color rectangleStash = Color(0xFFE7A644);
  static const Color errorColor= Color(0xFFFF9E9E);
  static const Color colorLightRed_20 = const Color(0x33DC3545);
  static const Color colorWhiteLight = Color(0xE0EEEEEE);
  static const Color colorDarkYellow = const Color(0xFFEF9D21);
  static const Color refundColor= Color(0xFFFF9E9E);

  static const Color successColor= Color(0xFF87F572);
  static const Color errorColorLight= Color(0x1AE7A644);
  static const Color errorColorDark= Color(0xFFE7A644);
  static const Color redColor = Color(0xFFF53333);

  static const Color dropShadowColor= Color(0xFFFFFFFF);
  static const Color fillColor2x = Color(0x542BA66B);
  static const Color locationTextBgColor = Color(0xFF2B2C32);

  static const Color elapsedBlueColor = Color(0xFFF1F4FF);
  static const Color elapsedBlueFontColor = Color(0xFF000055);
  static const Color elapsedYellow = Color(0xFFFFFBEB);
  static const Color elapsedYellowFontColor = Color(0xFFD97706);
  static const Color elapsedRedPaidColor = Color(0xFFFEF2F2);
  static const Color elapsedRedFontColor = Color(0xFFDC2626);
  static const Color clearAllColor = Color(0xFF6366F1);

  // Notification
  static const Color white = Color(0xFFFFFFFF);
  static const Color customerBackground = Color(0xFFF3F4F6);
  static const Color bluePriceFontColor = Color(0xFFF0D05D2);
  static const Color warningOrangeFontColor = Color(0xFFD97706);
  static const Color notifDetailText = Color(0xFF374151);
  static const Color notifIconColor = Color(0xFF374151);
  static const Color notifItemColor = Color(0xFF6B7280);
  static const Color notifQuantityColor = Color(0xFF111827);
  static const Color notifTextColor = Color(0xFF111827);
  static const Color notifLineThroughPriceColor = Color(0xFF9CA3AF);
  static const Color notifBorderColor = Color(0xFFD1D5DB);
  static const Color notifDiscountBorderColor = Color(0xFF312E81);
  static const Color notifPendingBackground = Color(0xFFFEF3C7);
  static const Color notifPendingTextColor = Color(0xFFB45309);
  static const Color notifCheckedColor = Color(0xFF4F46E5);
  static const Color notifUnCheckedColor = Color(0XFFEEF2FF);
  static const Color notifItemDeleteBgColor = Color(0xFFF3F4F6);
  static const Color notifItemNoteColor = Color(0xFF4B5563);

}
