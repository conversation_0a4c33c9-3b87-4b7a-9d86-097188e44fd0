import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/models/notification/activeview_provider.dart';
import 'package:gr8tables_server_manager/providers/cartview/receipt_provider.dart';
import 'package:gr8tables_server_manager/providers/customer/customer_order_history_provider.dart';
import 'package:gr8tables_server_manager/providers/discount/discount_provider.dart';
import 'package:gr8tables_server_manager/providers/historyview/historyview_provider.dart';
import 'package:gr8tables_server_manager/providers/homescreen/bottom_sheet_provider.dart';
import 'package:gr8tables_server_manager/providers/homescreen/home_screen_provider.dart';
import 'package:gr8tables_server_manager/providers/notification/notification_provider.dart';
import 'package:gr8tables_server_manager/providers/payment/payment_provider.dart';
import 'package:gr8tables_server_manager/screens/alerts/alerts_view.dart';
import 'package:gr8tables_server_manager/screens/cart/cart_view.dart';
import 'package:gr8tables_server_manager/screens/history/history_view.dart';
import 'package:gr8tables_server_manager/screens/orders/dine_in_takeout_view.dart';
import 'package:gr8tables_server_manager/screens/orders/dine_in_view.dart';
import 'package:provider/provider.dart';

import 'constants/app_color.dart';
import 'constants/app_string.dart';
import 'helpers/flavor_config.dart';
import 'providers/alerts/alerts_provider.dart';
import 'providers/auth/auth_provider.dart';
import 'providers/cartview/cart_view_provider.dart';
import 'providers/common/shareable_provider.dart';
import 'providers/homescreen/bottomview_provider.dart';
import 'providers/dine_in_takeout_view/dinein_takeout_provider.dart';
import 'providers/menuview/menu_view_provider.dart';
import 'providers/package_info/package_info_provider.dart';
import 'providers/pubnub/pubnub_provider.dart';
import 'screens/auth/forgot_password.dart';
import 'screens/auth/logIn_screen.dart';
import 'screens/home/<USER>';
import 'utils/app_routes.dart';
import 'utils/pref_utils.dart';

final navigatorKey = GlobalKey<NavigatorState>();
final scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => AuthProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => PackageInfoProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ShareableProvider(),
        ),
        ChangeNotifierProvider.value(
          value: AlertsProvider(
              navigatorKey: navigatorKey,
              scaffoldMessengerKey: scaffoldMessengerKey),
        ),
        ChangeNotifierProvider(
          create: (_) => BottomViewProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => BottomSheetProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => DineInTakeoutProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CartViewProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => MenuViewProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => PubNubProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => PaymentProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => HomeScreenProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CustomerOrderHistoryProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => HistoryViewProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ReceiptProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => DiscountProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => NotificationProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ActiveViewProvider(),
        ),
      ],
      child: MaterialApp(
        scaffoldMessengerKey: scaffoldMessengerKey,
        navigatorKey: navigatorKey,
        debugShowCheckedModeBanner: false,
        themeMode: ThemeMode.light,
        title: ApiConfig.name,
        theme: ThemeData(
          useMaterial3: true,
          fontFamily: AppFonts.roboto,
          checkboxTheme: CheckboxThemeData(
            checkColor: WidgetStateProperty.all<Color>(ColorConstant.notifCheckedColor),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity(horizontal: -4, vertical: -4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4.0),
            ),
              fillColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {

                    if(states.contains(WidgetState.selected)) {
                      return ColorConstant.notifUnCheckedColor;
                    }

                    return ColorConstant.transparent;
                  }
              ),
              side:   AlwaysKeepBorder(),
          ),
          dialogTheme: const DialogThemeData(
            shape: RoundedRectangleBorder(),
            backgroundColor: ColorConstant.colorThemeWhite,
            surfaceTintColor: ColorConstant.colorThemeWhite,
          ),
          appBarTheme:  AppBarTheme(
            backgroundColor: ColorConstant.colorThemeWhite,
            surfaceTintColor: ColorConstant.colorThemeWhite,
            iconTheme: const IconThemeData(color: ColorConstant.colorBlueDark,),
            elevation: 4,
            shadowColor: ColorConstant.colorBlueDark.withOpacity(.5),
            centerTitle: true,
          ),
          floatingActionButtonTheme: const FloatingActionButtonThemeData(
            backgroundColor: ColorConstant.colorBlueDark,
            elevation: 2,
          ),
          bottomAppBarTheme:  BottomAppBarTheme(
             color: ColorConstant.colorBlueDark,
            elevation: 4,
            shadowColor: ColorConstant.colorBlueDark.withOpacity(.5),
          ),
          colorScheme: ColorScheme.fromSeed(
            seedColor: ColorConstant.colorBlueDark,
            background: ColorConstant.colorThemeWhite,
            error: Colors.red,
            primary: ColorConstant.colorBlueDark,
            onTertiary: ColorConstant.colorBlueDark,
          ),
        ),
        initialRoute: _isLoggedIn() ? AppRoutes.HOME_SCREEN: AppRoutes.LOGIN_ROUTE,
        routes: {
          AppRoutes.LOGIN_ROUTE: (context) => const LoginScreen(),
          AppRoutes.HOME_SCREEN: (context) => const HomeScreen(),
          AppRoutes.FORGOT_PASSWORD_ROUTE: (context) => const ForgotPassword(),
          AppRoutes.DINE_IN_TAKEOUT_SCREEN: (context) => DineInTakeoutView(),
          AppRoutes.DINE_IN_SCREEN: (context) => DineInView(),
          AppRoutes.HISTORY_SCREEN: (context) => HistoryView(),
          AppRoutes.ALERTS_SCREEN: (context) => AlertsView(),
        },
      ),
    );
  }

  /// using auth token check app is already login or not
  bool _isLoggedIn() {
   String authToken = PrefsUtils.getString(PrefKeys.authToken) ?? '';
    return (authToken.isNotEmpty) ? true : false;
  }
}

class AlwaysKeepBorder extends WidgetStateBorderSide {
  @override
  BorderSide? resolve(_) => const BorderSide(color: ColorConstant.colorBlueDark, width: 0.7,
  strokeAlign: 5);

}