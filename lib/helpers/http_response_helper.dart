import 'dart:convert';
import "package:http/http.dart" show Response;

class HttpResponseHelper {
  var _errorMsg = "Something doesn't seem right. Please try again later.";

  parseHttpResponse(Response response, [String requestBody = ""]) {

    dynamic responseJson = _parseToJson(response.body.toString());

    if (responseJson['message'] != null) {
      if (responseJson['message'] is String) {
        _errorMsg = responseJson['message'];
      } else if (responseJson['message'] is List) {
        _errorMsg = '';
        for (var i = 0; i < (responseJson['message'] as List).length; i++) {
          _errorMsg += responseJson['message'][i] + "\n";
        }
      } else {
        _errorMsg = "Something doesn't seem right. Please try again later.";
      }
    }

    switch (response.statusCode) {
      case 200:
        return responseJson;
      case 201:
        return responseJson;
      case 400:
        throw BadRequestException(_errorMsg);
      case 401:
      case 403:
        throw UnauthorisedException(_errorMsg);
      case 404:
      case 409:
      case 418:
      case 500:
        throw FetchDataException(_errorMsg);
      default:
        throw FetchDataException(
            'Error occurred while Communication with Server with StatusCode : ${response.statusCode}');
    }
  }

  dynamic _parseToJson(String responseBody) {
    return json.decode(responseBody);
  }

}

class AppException implements Exception {
  final _message;
  final _prefix;

  AppException([this._message, this._prefix]);

  @override
  String toString() {
    return "$_prefix$_message";
  }
}

class FetchDataException extends AppException {
  FetchDataException([String message = 'error fetching data'])
      : super(message, '');
}

class BadRequestException extends AppException {
  BadRequestException([message]) : super(message, '');
}

class UnauthorisedException extends AppException {
  UnauthorisedException([message]) : super(message, '');
}

class InvalidInputException extends AppException {
  InvalidInputException([String message = 'invalid input'])
      : super(message, '');
}

class InternetException extends AppException {
  InternetException(
      [String message =
          'It looks like you are not connected to the internet. Please check your connection and try again.'])
      : super(message, '');
}