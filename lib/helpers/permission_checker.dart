

import '../models/auth/user_register_model.dart';
import '../utils/pref_utils.dart';

class PermissionChecker {

  static bool isPermissionAllowed(String permissionName) {
    String _userType = UserData.fromJson(PrefsUtils.getObject(PrefKeys.user)).userType!;
    if (_userType.toLowerCase() == 'super') {
      return true;
    }
    // print("Permission Allowed : ${_checkPermission(permissionName)}");
    return _checkPermission(permissionName);

  }

  static bool _checkPermission(String permissionName) {
    List<String>? _permissions =
        UserData.fromJson(PrefsUtils.getObject(PrefKeys.user)).permissions!;
    if (_permissions.isEmpty) {
      return false;
    }
    return _permissions.where((element) => element.toLowerCase() == permissionName.toLowerCase()).isNotEmpty;
  }
}
