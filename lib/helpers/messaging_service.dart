
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../constants/api_constant.dart';
import '../constants/app_string.dart';
import '../providers/homescreen/bottom_sheet_provider.dart';
import '../providers/homescreen/home_screen_provider.dart';
import '../providers/notification/notification_provider.dart';
import '../utils/app_routes.dart';
import '../utils/app_utils.dart';
import '../utils/notification_center/notification_center.dart';
import '../utils/pref_utils.dart';
import 'notification_permission.dart';

class MessagingService {
  static final MessagingService _instance = MessagingService._internal();

  factory MessagingService() => _instance;

  MessagingService._internal();

  final FirebaseMessaging _fcm = FirebaseMessaging.instance;

  Future<void> init(BuildContext _context) async {
    NotificationSettings settings = await _fcm.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('User granted notifications permission: ${settings.authorizationStatus}');

    if (settings.authorizationStatus == AuthorizationStatus.denied) {
       hideLoaderDialog(_context);
      _showPermissionDialog(_context);
    }

    String? fcmToken = PrefsUtils.getString(PrefKeys.fcmToken);
    debugPrint('fcmToken from preference: $fcmToken');
    if(fcmToken == null ){
      await _context.read<NotificationProvider>().initFirebase();
    }

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Message data: ${message.notification!.title.toString()}');
      if (message.notification != null) {
        String? topicKey = message.data[ApiConstant.TOPIC_KEY];
        String? cartId = message.data[ApiConstant.CART_ID_NOTIFICATION];
        String? tableId = message.data[ApiConstant.TABLE_ID_NOTIFICATION];
        _saveFlagPreferences();
        PrefsUtils.setString(PrefKeys.fcmTopicKey, topicKey!);
        PrefsUtils.setString(PrefKeys.fcmTableId, tableId!);
        PrefsUtils.setString(PrefKeys.fcmCartId, cartId!);
        NotificationCenter().notify(NotificationsString.notifyAlertScreen,data: true);
      }
    });

    FirebaseMessaging.instance.getInitialMessage().then((message) async{
      if (message != null) {
        debugPrint("Message clicked and app opened (Killed app): ${message.messageId}");
        _saveFlagPreferences();
        await _context.read<NotificationProvider>().getUnReadCount();
        _context.read<BottomSheetProvider>().setDialogForHomePage(false);
        _context.read<HomeScreenProvider>().updateSelectedIndex(3);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async{
      debugPrint("Message clicked and app opened (Background app): ${message.messageId}");
      try {
        _saveFlagPreferences();
        _context.read<HomeScreenProvider>().updateSelectedIndex(3);
        _context.read<NotificationProvider>().getUnReadCount();
        _context.read<BottomSheetProvider>().setDialogForHomePage(false);
      }catch(e){
        debugPrint("Error ${e.toString()}");
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
      debugPrint("FCM TokenRefresh: $newToken");
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      if (fcmToken != null) {
        PrefsUtils.setString(PrefKeys.fcmToken, fcmToken);
        await _context.read<NotificationProvider>().getUnReadCount();
      } else {
        debugPrint("FCM token null");
      }
    }).onError((err) {
      debugPrint("Error while refreshing FCM token: $err");
    });
  }

  void _showPermissionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${NotificationsString.enableNotificationsTitle}'),
        content: Text(
          Platform.isIOS?'${NotificationsString.enableIOSNotificationsBody}':'${NotificationsString.enableNotificationsBody}'
        ),
        actions: [
          if (Platform.isAndroid)...[
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                NotificationPermission().requestPermission();
              },
              child: Text('${NotificationsString.openSettings}'),
            ),
          ],
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(Platform.isIOS?'${NotificationsString.ok}':'${NotificationsString.cancel}'),
          ),
        ],
      ),
    );
  }
}


// Handler for background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  // Handle background message
  debugPrint("Handling a background message:(App killed)");
  debugPrint('Message ID: ${message.messageId}');
  debugPrint('Notification Title: ${message.notification?.title}');
  debugPrint('Notification Body: ${message.notification?.body}');
  debugPrint('Data: ${message.data}');
  debugPrint('From: ${message.from}');
  _saveFlagPreferences();
}

_saveFlagPreferences() async{
  try {
    // Directly access SharedPreferences instance
    // final prefs = await SharedPreferences.getInstance();
    // await prefs.setString(NotificationsString.fcmMessageReceive, "true");
    // debugPrint("Preference value saved successfully in background handler ${prefs.getString(NotificationsString.fcmMessageReceive)}");
    PrefsUtils.setString(PrefKeys.fcmReceived, "true");
    debugPrint("Preference value saved successfully in background handler ${PrefsUtils.getString(PrefKeys.fcmReceived)}");
  }catch(e){
    debugPrint("Error in PrefsUtils.setString(PrefKeys.fcmMessageReceive");
  }
}