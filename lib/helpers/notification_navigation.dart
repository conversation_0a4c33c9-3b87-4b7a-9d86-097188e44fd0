import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../constants/app_string.dart';
import '../providers/homescreen/bottom_sheet_provider.dart';
import '../providers/homescreen/home_screen_provider.dart';
import '../utils/app_routes.dart';
import '../utils/pref_utils.dart';
/// This class only use for foreground notifications
class NotificationNavigation {
  static const MethodChannel _navigationChannel = MethodChannel(MethodChannelString.notificationChannel);

  navigateToPage({required BuildContext context}) {
    // Listen for navigation requests from Android
    _navigationChannel.setMethodCallHandler((call) async {
      _saveFlagPreferences();
      context.read<BottomSheetProvider>().setDialogForHomePage(false);
      context.read<HomeScreenProvider>().updateSelectedIndex(3);
      // if (call.method == MethodChannelString.navigateToCart) {
      //   // String tab = call.arguments;
      //   // if (tab == MethodChannelString.cartDetails) {}
      //   debugPrint('load table - cart details page.');
      //   _screenNavigation(buildContext: context,);
      // }
    });
  }
  _saveFlagPreferences() async{
    try {
      // Directly access SharedPreferences instance
      // final prefs = await SharedPreferences.getInstance();
      // await prefs.setString(NotificationsString.fcmMessageReceive, "true");
      // debugPrint("Preference value saved successfully in background handler");
      await PrefsUtils.init();
      PrefsUtils.setString(PrefKeys.fcmReceived, "true");
      debugPrint("Preference value saved successfully in background handler ${PrefsUtils.getString(PrefKeys.fcmReceived)}");
    }catch(e){
      debugPrint("Error in PrefsUtils.setString(PrefKeys.fcmMessageReceive");
    }
  }

}
