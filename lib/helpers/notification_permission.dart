import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import '../constants/app_string.dart';

class NotificationPermission {
  static const platform = MethodChannel(MethodChannelString.permissionChannel);

  Future<void> requestPermission() async {
    try {
      final String result = await platform.invokeMethod(MethodChannelString.requestNotificationPermission);
      debugPrint("permission:$result");  // This will print 'Requested' if the call was successful
    } on PlatformException catch (e) {
      debugPrint("Failed to request notification permission: '${e.message}'");
    }
  }
}
