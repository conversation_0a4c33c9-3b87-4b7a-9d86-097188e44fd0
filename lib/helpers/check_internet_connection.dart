import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

typedef StateCallback = void Function(bool);

class CheckInternetConnection {

  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  static CheckInternetConnection? _checkInternetConnection;

  static CheckInternetConnection newInstance() {
    if (_checkInternetConnection == null) {
      _checkInternetConnection = CheckInternetConnection();
    }
    return _checkInternetConnection!;
  }

  initConnection() async {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((status) {

      if (status.contains(ConnectivityResult.mobile)) {
        debugPrint("Connected to the Mobile Internet");
        _connectionStatus = [ConnectivityResult.mobile];
      } else if (status.contains(ConnectivityResult.wifi)) {
        debugPrint("Connected to the Wifi Internet");
        _connectionStatus = [ConnectivityResult.wifi];
      } else if (status.contains(ConnectivityResult.ethernet)) {
        debugPrint("Connected to the Wifi Ethernet");
        _connectionStatus = [ConnectivityResult.ethernet];
      } else if (status.contains(ConnectivityResult.vpn)) {

      } else if (status.contains(ConnectivityResult.bluetooth)) {

      } else if (status.contains(ConnectivityResult.other)) {
        // Connected to a network which is not in the above mentioned networks.
      } else if (status.contains(ConnectivityResult.none)) {
        debugPrint("You are disconnected to the Internet.");
        _connectionStatus = [ConnectivityResult.none];
      }

    });

    await Future<void>.delayed(const Duration(seconds: 10));
    await _connectivitySubscription.cancel();
  }

  Future<bool> checkConnection() async {
    _connectionStatus = await _connectivity.checkConnectivity();
    if (_connectionStatus.contains(ConnectivityResult.none)) {
      return false;
    } else {
      return true;
    }
  }

}
