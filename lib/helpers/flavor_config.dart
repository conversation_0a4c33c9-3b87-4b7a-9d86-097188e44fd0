enum Environment {
  DEV,
  BETA,
  UAT,
  LIVE,
  PRODUCTION,
}

class ApiConfig {
  static late Map<String, dynamic> _config;

  static void setEnvironment(Environment env) {
    switch (env) {
      case Environment.DEV:
        _config = _Config.developConstants;
        break;
      case Environment.BETA:
        _config = _Config.betaConstants;
        break;
      case Environment.UAT:
        _config = _Config.uatConstants;
        break;
      case Environment.LIVE:
        _config = _Config.liveConstants;
        break;
      case Environment.PRODUCTION:
        _config = _Config.productionConstants;
        break;
    }
  }

  static get authSERVERURL {
    return _config[_Config.AUTH_SERVER_URL];
  }

  static get authSERVERTOKEN {
    return _config[_Config.AUTH_SERVER_API_TOKEN];
  }

  static get cartSERVERURL {
    return _config[_Config.CART_SERVER_URL];
  }


  static get name {
    return _config[_Config.APP_NAME];
  }

  static get environment {
    return _config[_Config.ENVIRONMENT];
  }

  static bool get isDebug {
    return _config[_Config.IS_DEBUG];
  }

  static bool get isCrashlyticsEnabled {
    return _config[_Config.IS_CRASHLYTICS_ENABLED];
  }

  static get pubnubSubscribeKey {
    return _config[_Config.PUBNUB_SUBSCRIBE_KEY];
  }
  static get pubnubPublishKey {
    return _config[_Config.PUBNUB_PUBLISH_KEY];
  }

  static get graphqlUrl {
    return _config[_Config.GRAPHQL_URL];
  }

  static get googlePlaceApiKey{
    return _config[_Config.GOOGLE_PLACE_API_KEY];
  }
  static get googlePlaceUrl{
    return _config[_Config.GOOGLE_PLACE_URL];
  }
}

class _Config {
  static const AUTH_SERVER_URL = "AUTH_SERVER_URL";
  static const CART_SERVER_URL = "CART_SERVER_URL";
  static const AUTH_SERVER_API_TOKEN = "AUTH_SERVER_API_TOKEN";
  static const IS_DEBUG = "IS_DEBUG";
  static const IS_CRASHLYTICS_ENABLED = "IS_CRASHLYTICS_ENABLED";
  static const APP_NAME = "APP_NAME";
  static const ENVIRONMENT = "ENVIRONMENT";
  static const PUBNUB_SUBSCRIBE_KEY = "PUBNUB_SUBSCRIBE_KEY";
  static const PUBNUB_PUBLISH_KEY = "PUBNUB_PUBLISH_KEY";
  static const GRAPHQL_API_TOKEN = "GRAPHQL_API_TOKEN";
  static const GRAPHQL_URL = "GRAPHQL_URL";
  static const GOOGLE_PLACE_API_KEY = "GOOGLE_PLACE_API_KEY";
  static const GOOGLE_PLACE_URL = "GOOGLE_PLACE_URL";

  static Map<String, dynamic> developConstants = {
    AUTH_SERVER_URL: "amora-auth.dev.api.amorapos.net",
    AUTH_SERVER_API_TOKEN: "1YpaUQyGQ8Iye1u02zJyaCLaL3a34biW",
    CART_SERVER_URL: "order-v2.dev.api.unoapp.io",
    IS_DEBUG: true,
    IS_CRASHLYTICS_ENABLED: false,
    PUBNUB_PUBLISH_KEY:'******************************************',
    PUBNUB_SUBSCRIBE_KEY: '******************************************',
    APP_NAME: 'Amora Dev',
    ENVIRONMENT : 'Dev',
    GRAPHQL_URL : 'https://menu-v2.dev.api.amorapos.net/graphql',
    GOOGLE_PLACE_API_KEY : 'AIzaSyCytx_kr2QzqYCZqZTLkr2AuBuc307QB9Y',
    GOOGLE_PLACE_URL : 'maps.googleapis.com',
  };

  static Map<String, dynamic> betaConstants = {
    AUTH_SERVER_URL: "amora-auth.beta.api.amorapos.net",
    AUTH_SERVER_API_TOKEN: "taoEAHQOx65erxVQQQhmXrbY0xufsk8p",
    CART_SERVER_URL: "order-v2.beta.api.unoapp.io",
    IS_DEBUG: true,
    IS_CRASHLYTICS_ENABLED: false,
    PUBNUB_PUBLISH_KEY:'******************************************',
    PUBNUB_SUBSCRIBE_KEY: '******************************************',
    APP_NAME: 'Amora Beta',
    ENVIRONMENT : 'Beta',
    GRAPHQL_URL : 'https://menu-v2.beta.api.amorapos.net/graphql',
    GOOGLE_PLACE_API_KEY : 'AIzaSyCytx_kr2QzqYCZqZTLkr2AuBuc307QB9Y',
    GOOGLE_PLACE_URL : 'maps.googleapis.com',
  };

  static Map<String, dynamic> uatConstants = {
    AUTH_SERVER_URL: "amora-auth.uat.api.amorapos.net",
    AUTH_SERVER_API_TOKEN: "I891HlrKg6AFwo1ZH2KPgamDXsMoHOEt",
    CART_SERVER_URL: "order-v2.uat.api.amorapos.net",
    IS_DEBUG: true,
    IS_CRASHLYTICS_ENABLED: false,
    PUBNUB_PUBLISH_KEY:'******************************************',
    PUBNUB_SUBSCRIBE_KEY: '******************************************',
    APP_NAME: 'Amora Uat',
    ENVIRONMENT: 'Uat',
    GRAPHQL_URL : 'https://menu-v2.uat.api.amorapos.net/graphql',
    GOOGLE_PLACE_API_KEY : 'AIzaSyCytx_kr2QzqYCZqZTLkr2AuBuc307QB9Y',
    GOOGLE_PLACE_URL : 'maps.googleapis.com',
  };

  static Map<String, dynamic> productionConstants = {
    AUTH_SERVER_URL: "gr8tables-auth.api.unoapp.io",
    AUTH_SERVER_API_TOKEN: "vJgCLXHZF0l7FIPlkYw4N4HJGsvjsuUd",
    CART_SERVER_URL: "order-v2.api.unoapp.io",
    IS_DEBUG: false,
    IS_CRASHLYTICS_ENABLED: false,
    PUBNUB_PUBLISH_KEY:'******************************************',
    PUBNUB_SUBSCRIBE_KEY: '******************************************',
    APP_NAME: 'Amora',
    ENVIRONMENT: 'Production',
    GRAPHQL_URL : 'https://menu-v2.api.unoapp.io/graphql',
    GOOGLE_PLACE_API_KEY : 'AIzaSyCytx_kr2QzqYCZqZTLkr2AuBuc307QB9Y',
    GOOGLE_PLACE_URL : 'maps.googleapis.com',
  };

  static Map<String, dynamic> liveConstants = {
    AUTH_SERVER_URL: "gr8tables-auth.api.unoapp.io",
    AUTH_SERVER_API_TOKEN: "vJgCLXHZF0l7FIPlkYw4N4HJGsvjsuUd",
    CART_SERVER_URL: "order-v2.api.unoapp.io",
    IS_DEBUG: false,
    IS_CRASHLYTICS_ENABLED: false,
    PUBNUB_PUBLISH_KEY:'******************************************',
    PUBNUB_SUBSCRIBE_KEY: '******************************************',
    APP_NAME: 'Amora Live',
    ENVIRONMENT: 'Live',
    GRAPHQL_URL : 'https://menu-v2.api.unoapp.io/graphql',
    GOOGLE_PLACE_API_KEY : 'AIzaSyCytx_kr2QzqYCZqZTLkr2AuBuc307QB9Y',
    GOOGLE_PLACE_URL : 'maps.googleapis.com',
  };
}
