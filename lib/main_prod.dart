import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'utils/pref_utils.dart';

import 'helpers/flavor_config.dart';
import 'my_app.dart';

void main() async{
  ApiConfig.setEnvironment(Environment.PRODUCTION);

  WidgetsFlutterBinding.ensureInitialized();
  // initialize shared preferences here to ensure that it is initialized and can then be accessed
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light.copyWith(
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.dark,
    statusBarColor: Colors.white,
    systemNavigationBarColor: Colors.white,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));
  await PrefsUtils.init();
  await Firebase.initializeApp();
  runApp(const MyApp());
}