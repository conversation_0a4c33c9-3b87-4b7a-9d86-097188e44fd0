
import 'dart:convert';

import 'package:flutter/cupertino.dart';

import '../../constants/api_constant.dart';
import '../../constants/app_string.dart';
import '../../models/cart/cart_custom_discount_response.dart';
import '../../models/cart/cart_data.dart';
import '../../models/cart/cart_items/cart_item_promotions.dart';
import '../../models/cart/cart_items/cart_items.dart';
import '../../models/cart/cart_promotions/remove_promotions.dart';
import '../../models/discount/cart_discount_response.dart';
import '../../models/discount/discount_response.dart';
import '../../models/discount/promotion_apply_on.dart';
import '../../models/location/location_user_model.dart';
import '../../models/loyalty/remove_loyalty_response.dart';
import '../../models/loyalty/reward_types.dart';
import '../../models/orders/cart_promotions/cart_promocode_response.dart';
import '../../models/orders/order_items_response.dart';
import '../../services/cart/cart_service.dart';
import '../../services/graph_ql/graph_ql_service.dart';
import '../../services/location/location_service.dart';
import '../../utils/app_utils.dart';
import '../../utils/pref_utils.dart';

class DiscountProvider with ChangeNotifier {


  final LocationService _location = LocationService();
  final CartService _cartService = CartService();


  bool _hasDiscount = false;
  bool get hasDiscount => _hasDiscount;

  bool _isApplied = false;
  bool get applied => _isApplied;

  num _discountId = 0;
  num get discountId => _discountId;

  List<DiscountsData> _discountList = [];
  List<DiscountsData> get discountList => _discountList;

  List<CartItems> _itemList = [];
  List<CartItems> get itemList => _itemList;

  ///Employees
  int _selectedEmployeeIndex = -1;
  int get selectedEmployeeIndex => _selectedEmployeeIndex;

  List<LocationUserModel> employeeList = [];

  bool _hasDiscountInputError =false;
  bool get hasDiscountInputError => _hasDiscountInputError;


  bool _customDiscountAuthorized = false;
  bool get customDiscountAuthorized => _customDiscountAuthorized;

  bool _regularDiscountAuthorized = false;
  bool get regularDiscountAuthorized => _regularDiscountAuthorized;

  String _discountType='\%'; //'\$';
  String get discountType =>_discountType;

  String _selectedEmpEmail ="";
  String get selectedEmpEmail =>_selectedEmpEmail;

  bool _hasPromoCodeInputError =false;
  bool get hasPromoCodeInputError => _hasPromoCodeInputError;

  String _promoCodeErrorMsg="";
  String get promoCodeErrorMsg => _promoCodeErrorMsg;

  String _successMsg="";
  String get successMsg => _successMsg;

  String _discountErrorMsg="";
  String get discountErrorMsg => _discountErrorMsg;

  String _customDiscountError="";
  String get customDiscountError => _customDiscountError;

  bool _allItemsSelected = false;
  bool get allItemsSelected => _allItemsSelected;

  /// for discount type switch
  Alignment _knobAlignment = Alignment.centerLeft;
  Alignment get knobAlignment => _knobAlignment;

  /// employee pin
  String _pin = "";
  String get pin => _pin;

  // custom discount
  bool _customDiscountOnCartSelected =true;
  bool get customDiscountOnCartSelected =>_customDiscountOnCartSelected;

  bool _customDiscountOnItemSelected =false;
  bool get customDiscountOnItemSelected =>_customDiscountOnItemSelected;

  bool _hasCustomNextPressed =false;
  bool get hasCustomNextPressed =>_hasCustomNextPressed;

  num _newSubTotal=0;
  num get newSubTotal => _newSubTotal;

  num _subTotal=0;
  num get subTotal => _subTotal;

  num _cartTotal=0;
  num get cartTotal => _cartTotal;

  num _customCartDiscount = 0;
  num get customCartDiscount => roundDouble(_customCartDiscount);


  CartData? _cartData;
  CartData? get cartData =>_cartData;

  num _totalSavings=0;
  num get totalSavings => _totalSavings;

  num _totalItemDiscount=0;
  num get totalItemDiscount => roundDouble(_totalItemDiscount);

  bool _notesExpanded = false;
  bool get notesExpanded => _notesExpanded;

  String _customDiscountNote="";
  String get customDiscountNote => _customDiscountNote;

  num _customDiscountValue=0;
  num get customDiscountValue => roundDouble(_customDiscountValue);


  String _preSetDiscountErrorMsg="";
  String get preSetDiscountErrorMsg => _preSetDiscountErrorMsg;

  /// only for getting preset discounts
  List<CartItems> _cartItems = [];
  List<CartItems> get cartItems => _cartItems;

  /// only for getting preset discounts
  setCartItems(List<CartItems> items) {
    _cartItems.clear();
     _cartItems.addAll(items);
  }

  setNotesExpandCollapse(){
    this._notesExpanded = !this._notesExpanded;
    notifyListeners();
  }



  /// check custom discount is on cart or not
  bool checkCustomDiscountOnCart() {
    if(this._cartData!=null) {
      if(this._cartData!.cartCustomDiscount!.isNotEmpty) {
        return this._cartData!.cartCustomDiscount!.where((customDiscount) =>customDiscount.applyOn ==PromotionApplyOn.cart.name)
            .isNotEmpty;
      }
    }
    return false;
  }

  /// check item has already custom discount
  bool customDiscountAlreadyAppliedOnItem(List<CartPromotionItem> orderCustomDiscountItem) {
    // check weather item has product level or cart level custom discount applied or not
    num _total = orderCustomDiscountItem.
    where((discountItem) => (discountItem.applyOn  == PromotionApplyOn.product.name) || (discountItem.applyOn  == PromotionApplyOn.cart.name)  )
        .fold(0, (i, item){
      return i + item.appliedValue!;
    });
    return (_total > 0);
  }

  /// check if item is gift
  /// or part of rewardType = item
  bool isItemFree(CartItems item){
    if(null != _cartData!.cartReward){
      if( _cartData!.cartReward!.rewardType!.toLowerCase() == RewardType.item.name){
        /// applied on item and that item is free
        if(null != item.cartRewardItem && item.freeItem!)
          return true;
      }
    }
    return false;
  }

  /// find total applied custom discount for item
  num totalCustomAppliedDiscountValue(List<OrderPromotionItem> orderCustomDiscountItem,{bool forCart=false}) {
    num _total = orderCustomDiscountItem.
    where((discountItem) => discountItem.applyOn  == (forCart ? PromotionApplyOn.cart.name : PromotionApplyOn.product.name ))
        .fold(0, (i, item){
       return i + item.appliedValue!;
    });
    return _total;
  }

  /// set custom discount value
  setCustomDiscountValue(num value){
    this._customDiscountValue = value;
    notifyListeners();
  }
  /// set custom discount note
  setCustomDiscountNote(String note){
    this._customDiscountNote = note;
    notifyListeners();
  }

  /// set custom discount error
   setCustomDiscountError(String errorMsg){
    this._customDiscountError =errorMsg;
    notifyListeners();
  }

  /// reset custom dis
  resetCustomDiscount(){
    this._customCartDiscount =0;
    _resetItemDiscount();
    this._totalSavings =0;
    this._totalItemDiscount =0;
    this._customCartDiscount =0;
    this._customDiscountError ="";
    notifyListeners();
  }

   setOrder(CartData? cartData)  async{
     debugPrint(" inside set order");
    this._cartData = cartData;
    debugPrint(" inside set order ${this._cartData}");
    manageOrderCalculation();
    if(_cartData!=null && _cartData?.id !=0) {
      this._setItemList(_cartData?.cartItems ?? []);
      setCartItems(_cartData?.cartItems ?? []);
    }
    notifyListeners();
  }

  /// recalculate order data with promotions
  /// along with any total changes
  manageOrderCalculation(){
    if(this._cartData!=null) {
      this._totalSavings = 0;
      this._subTotal  = this._cartData!.subtotal!;
      this._newSubTotal =  this._subTotal;


      /// has promo code
      if(_cartData?.cartPromoCode!=null) {
        _newSubTotal = roundDouble(this._newSubTotal - _cartData!.cartPromoCode!.appliedValue!);
        this._totalSavings = roundDouble(this._totalSavings + _cartData!.cartPromoCode!.appliedValue!);
      }

      /// has discount
      if(_cartData?.cartDiscount!=null) {
        _newSubTotal = roundDouble(this._newSubTotal - _cartData!.cartDiscount!.appliedValue!);
        this._totalSavings = roundDouble(this._totalSavings + _cartData!.cartDiscount!.appliedValue!);
      }

      /// has membership points redeemed
      /// excluding as custom discount need to ignore that points applied

      /// has tier reward used
      if(_cartData?.cartReward !=null){
        _newSubTotal = roundDouble(this._newSubTotal - _cartData!.cartReward!.rewardAmount!);
        // if(this._order?.orderReward!.rewardType!.toLowerCase() !=  RewardType.item.name) {
        this._totalSavings = roundDouble(this._totalSavings + _cartData!.cartReward!.rewardAmount!);
        // }
      }

      /// has custom discount
      if(this._cartData!.cartCustomDiscount!.isNotEmpty) {

        num _customDiscountTotal = this._cartData!.cartCustomDiscount!.fold(0, (i, item){
          return i + item.appliedValue!;
        });

        //this._totalItemDiscount = _customDiscountTotal;

        _newSubTotal = roundDouble(this._newSubTotal - _customDiscountTotal);
        this._totalSavings = roundDouble(this._totalSavings + _customDiscountTotal);
      }

      this._cartTotal = this._newSubTotal + this._cartData!.tax!;

      if(this._newSubTotal == this._subTotal) {
        this._newSubTotal = 0;
      }

    }
    notifyListeners();
  }

  /// remove custom discount from item
  void removeCustomDiscountFromItem(num id) {
    if(this._itemList.isNotEmpty) {
      this._itemList.where((item) => item.id! ==id).
      map((item) {
         if(item.afterPromotionValue != item.customDiscount) {
           item.afterPromotionValue =
               item.afterPromotionValue! + item.customDiscount!;
         }
        item.customDiscount =0;
      }).toList();
      _handleCartDiscountChanges(forItemRemove: true);
      notifyListeners();
    }
  }

  /// remove preset discount
  void removePresetDiscount(int id) {
    if(this._itemList.isNotEmpty) {
      this._itemList.where((item) => item.id! ==id).
      map((item) {
        if(item.afterPromotionValue != item.customDiscount) {
          item.afterPromotionValue =
              item.afterPromotionValue! + item.customDiscount!;
        }
        item.customDiscount =0;
      }).toList();
      _handleCartDiscountChanges(forItemRemove: true);
      notifyListeners();
    }
  }

  void hasApplyCustomDiscountError(num value,{bool forCart=true}) {
    if(forCart) {
      /// on cart
      if (value != 0) {
        if(this.totalSavings == this._subTotal ) {
          setCustomDiscountValue(0);
          debugPrint('value : $value $newSubTotal');
          this._customDiscountError = "";
          this._customCartDiscount = 0;
          return;
        }
        setCustomDiscountValue(value);
        debugPrint('value : $value $newSubTotal');
        if (this._discountType == '\$') {
          if (value >this._subTotal
              /*(this._newSubTotal == 0 ? this._subTotal : this._newSubTotal)*/) {
            this._customCartDiscount = 0;
            this._customDiscountError =
                PromotionsString.discountCannotGreaterThanTotal;
          } else {
            this._customDiscountError = "";
            if(value >(this._newSubTotal == 0 ? this._subTotal : this._newSubTotal) ){
              value = (this._newSubTotal == 0 ? this._subTotal : this._newSubTotal);
            }
            this._newSubTotal =  ((this._newSubTotal == 0 ? this._subTotal : this._newSubTotal) - value) ;
            this._customCartDiscount = value;
            this._cartTotal = this._newSubTotal + _cartData!.tax!;
          }
        } else {
          if (value > 100) {
            this._customCartDiscount = 0;
            this._customDiscountError =
                PromotionsString.discountCannotGreaterThanPercentage;
          } else {
            this._customDiscountError = "";
            var _tmpAmount = (value / 100) * (this._newSubTotal == 0 ? this._subTotal : this._newSubTotal);
            if(_tmpAmount >(this._newSubTotal == 0 ? this._subTotal : this._newSubTotal) ){
              _tmpAmount = (this._newSubTotal == 0 ? this._subTotal : this._newSubTotal).toDouble();
            }
            this._newSubTotal = ((this._newSubTotal == 0 ? this._subTotal : this._newSubTotal) - _tmpAmount);
            this._customCartDiscount = _tmpAmount;
            this._cartTotal = this._newSubTotal + _cartData!.tax!;
          }
        }
        this._totalSavings = this._totalSavings + this._customCartDiscount;
      }
      else {
        setCustomDiscountValue(0);
        debugPrint('value : $value $newSubTotal');
        this._customDiscountError = "";
        this._customCartDiscount = 0;
        this._totalSavings = 0;

        /// reset the values
        manageOrderCalculation();
      }
    }
    else {
      /// on Item

      if (value != 0){
        if(this.totalSavings == this._subTotal ) {
          setCustomDiscountValue(0);
          debugPrint('value item :: : $value $newSubTotal');
          this._customDiscountError = "";
          this._customCartDiscount = 0;
          this._itemList/*.where((item) => item.selected ==true)*/.map((item) {
            if(item.afterPromotionValue! != item.customDiscount!) {
              // item.afterPromotionValue = item.afterPromotionValue! - item.customDiscount!;
              item.afterPromotionValue = roundDouble(item.afterPromotionValue! + item.customDiscount!);
            }
            item.customDiscount =0;
          }).toList();
          //_resetItemDiscount();
          this._totalSavings = 0;
          this._totalItemDiscount =0;
          /// reset the values
          manageOrderCalculation();
          return;
        }

        setCustomDiscountValue(value);
        debugPrint(' item value ::: $value $newSubTotal');
        /// traverse first for check selected items
        num _total = this._itemList.where((item) => item.selected == true,).fold(0, (i, item){
          return i + item.afterPromotionValue!;
         // return i + item.lineTotal!;
        });

        if (this._discountType == '\$') {

          debugPrint('line total ::: $_total ::: of selected ');

          if(value > _total){

            /// reset discount for select item to 0
            this._itemList.where((item) => item.selected == true,).
            map((item) {
              if(item.afterPromotionValue! == item.customDiscount!) {
                item.afterPromotionValue = 0;
              }else {
                // item.afterPromotionValue = item.afterPromotionValue! - item.customDiscount!;
                item.afterPromotionValue = item.afterPromotionValue! + item.customDiscount!;
              }
              item.customDiscount = 0;
            }).toList();

            this._customDiscountError =
                PromotionsString.discountCannotGreaterThanItemTotal;
          }else {
            this._customDiscountError = "";
             /// apply custom discount amount to selected item
            this._itemList.where((item) => item.selected == true,).
            map((item) {
              debugPrint('ready for apply ${item.id}');
              debugPrint('::: line total ${item.lineTotal!} subtotal ${this._subTotal} :::  entered value $value');
              num _tmpValue =0;
              if(this._itemList.where((item) => item.selected == true,).length >1) {
                 _tmpValue = roundDouble(getItemWeightageOnCart(
                   // item.lineTotal!,_total /*this._subTotal*/) * value);
                    item.afterPromotionValue!,_total /*this._subTotal*/) * value);
              }else {
                _tmpValue =  value;
              }
              debugPrint('::: after divide equal value flat ${item.name} ::: $_tmpValue');


              debugPrint('custom discount flat ${(_tmpValue > item.afterPromotionValue!) ? 0.00 :_tmpValue}');

              debugPrint(':::: after promotion flat ${item.afterPromotionValue} :::::');
              if(item.afterPromotionValue == 0 ){
                item.afterPromotionValue = _tmpValue;
              }else if(item.afterPromotionValue == _tmpValue) {
                item.afterPromotionValue = 0;
                //  item.afterPromotionValue = _tmpValue;
              }else {
                item.afterPromotionValue =  (_tmpValue > item.afterPromotionValue!) ? 0.00 :   item.afterPromotionValue! - _tmpValue;
              }

              debugPrint(':::: after promotion flat == ${item.afterPromotionValue} :::::');
             // item.customDiscount =(_tmpValue > item.afterPromotionValue!) ? item.afterPromotionValue!  :_tmpValue;
              item.customDiscount = roundDouble(_tmpValue);/* (_tmpValue > item.afterPromotionValue!) ? 0.00 :roundDouble(_tmpValue);*/

            }).toList();
            _handleCartDiscountChanges(forItemRemove: false);
          }

        }
        else {
          if (value > 100) {
            /// reset discount for select item to 0
            this._itemList.where((item) => item.selected == true,).
            map((item)
            {
              if(item.afterPromotionValue! == item.customDiscount!) {
                item.afterPromotionValue = 0;
              }else {
                // item.afterPromotionValue = item.afterPromotionValue! - item.customDiscount!;
                item.afterPromotionValue = item.afterPromotionValue! + item.customDiscount!;
              }
              item.customDiscount = 0;
            }).toList();

            this._customDiscountError =
                PromotionsString.discountCannotGreaterThanPercentage;
          }else {
            this._customDiscountError = "";

            /// apply custom percentage discount amount to selected item
            this._itemList.where((item) => item.selected == true,).
            map((item) {
              var _tmpAmount = roundDouble ((value / 100) * _total)/*this._subTotal*/; /*item.afterPromotionValue!*/;
              debugPrint('percentage ::: selected item ${item.name}::: $_tmpAmount');

              num _tmpValue =0;
              if(this._itemList.where((item) => item.selected == true,).length >1) {
                 _tmpValue = roundDouble(getItemWeightageOnCart(
                    /*item.lineTotal!*/ item.afterPromotionValue!, _total/*this._subTotal*/) * _tmpAmount);
              }else {
                _tmpValue = _tmpAmount;
              }
              debugPrint('::: after divide equal value ::: $_tmpValue');

              debugPrint('custom discount ${(_tmpValue > item.afterPromotionValue!) ? 0.00 :_tmpValue}');

              debugPrint(':::: after promotion ${item.afterPromotionValue} :::::');
              if(item.afterPromotionValue == 0 ){
                item.afterPromotionValue = _tmpValue;
              }else if(item.afterPromotionValue == _tmpValue) {
                item.afterPromotionValue = 0;
                //item.afterPromotionValue = _tmpValue;
              }else {
                item.afterPromotionValue =  (_tmpValue > item.afterPromotionValue!) ? 0.00 :   item.afterPromotionValue! - _tmpValue;
              }

              debugPrint(':::: after promotion == ${item.afterPromotionValue} :::::');
              //item.afterPromotionValue = (_tmpValue > item.afterPromotionValue!) ? 0.00 :  item.afterPromotionValue! - _tmpValue;
             // item.customDiscount = item.customDiscount! + _tmpValue;
              item.customDiscount =  roundDouble(_tmpValue);//(_tmpValue > item.afterPromotionValue!) ? item.afterPromotionValue!  :_tmpValue;
            }).toList();
            _handleCartDiscountChanges(forItemRemove: false);
          }
        }
      }else {
        setCustomDiscountValue(0);
        debugPrint('in else value :: : $value $newSubTotal');
        this._customDiscountError = "";
        this._customCartDiscount = 0;
        this._itemList/*.where((item) => item.selected ==true)*/.map((item) {
          if(item.afterPromotionValue! != item.customDiscount!) {
           // item.afterPromotionValue = item.afterPromotionValue! - item.customDiscount!;
            item.afterPromotionValue = roundDouble(item.afterPromotionValue! + item.customDiscount!);
          }
          item.customDiscount =0;
        }).toList();
        //_resetItemDiscount();
        this._totalSavings = 0;
        this._totalItemDiscount =0;
        /// reset the values
        manageOrderCalculation();
        _resetItemDiscount();
        notifyListeners();
      }
    }
    notifyListeners();

  }

  // weightage of item based on sub total
  // if user selects 2 items then sum of 2 items
  ///Proportions logic
  /// item A : 10$ item B : 20$  item C : 22$
  /// first get their weightage on cart
  /// item A : 10/(10+20+22) = value
  /// multiply the amount that you want to divide with value
  /// for e.g 5$ = 5*value = assigned discount
  num getItemWeightageOnCart(num lineTotal, num subTotal){
    //  Proportions of each item's price
    debugPrint(':::: weightage individual :::: ${(lineTotal  /  subTotal)}' );
    return (lineTotal /  subTotal);
  }

  /// handle cart discount changes
  /// when apply on item
  void _handleCartDiscountChanges({required bool forItemRemove}){
    if(this._itemList.isNotEmpty){
      num _totalCustomDiscount = this._itemList/*.where((item) => item.selected == true,)*/.fold(0, (i, item){
        debugPrint('custom discount for total ${item.customDiscount}');
        return i + item.customDiscount!;
      });

      num _removedItemDiscount = 0;
      debugPrint('::: total custom discount ::: $_totalCustomDiscount');
      if(this._totalItemDiscount!=0) {
        debugPrint('before ::: Removed item discount ::: $_removedItemDiscount');
        _removedItemDiscount =  this._totalItemDiscount  - _totalCustomDiscount;
        debugPrint('after ::: Removed item discount ::: $_removedItemDiscount');
      }
        this._totalItemDiscount = _totalCustomDiscount;


      if(this._newSubTotal == 0){
        this._newSubTotal = this._subTotal;
      }
      debugPrint('before ::: new sub total ::: $_newSubTotal');
      debugPrint('before ::: total savings ::: $_totalSavings');
      if(!forItemRemove){
        _newSubTotal =roundDouble(_newSubTotal - _totalCustomDiscount) ;
        this._totalSavings = roundDouble(this._totalSavings + _totalCustomDiscount);
      }else {
        _newSubTotal = roundDouble(this._newSubTotal + _removedItemDiscount);
        this._totalSavings = roundDouble(this._totalSavings -  _removedItemDiscount);
      }
      debugPrint('after ::: new sub total ::: $_newSubTotal');
      debugPrint('after ::: total savings ::: $_totalSavings');
    }

    this._cartTotal = this._newSubTotal + this._cartData!.tax!;

    if(this._newSubTotal == this._subTotal) {
      this._newSubTotal = 0;
    }
  }

  /// reset item discount
  void _resetItemDiscount(){
    if(this._itemList.isNotEmpty){
    _applyPresetPromotionToItem();
   }
  }

  void setCustomNextPressed(bool value) {
    this._hasCustomNextPressed = value;
    notifyListeners();
  }

  void setCustomDiscountIOnCartSelected() {
    this._customDiscountNote = "";
    this._customDiscountValue = 0;
    this._customDiscountOnCartSelected = true;
    this._customDiscountOnItemSelected = false;
    notifyListeners();
  }

  void setCustomDiscountIOnItemSelected() {
    this._customDiscountNote = "";
    this._customDiscountValue = 0;
    this._customDiscountOnItemSelected = true;
    this._customDiscountOnCartSelected = false;
    notifyListeners();
  }

  void resetDiscountOptions() {
    this._customDiscountNote = "";
    this._customDiscountValue = 0;
    this._customDiscountOnCartSelected = true;
    this._customDiscountOnItemSelected = false;
  }

  /// set employee pin
  void setEmployeePin(String pin){
    this._pin =pin;
    notifyListeners();
  }

  ///Get discounts for business
  Future<void> retrieveDiscounts() async {
    List<DiscountsData> _discountList = [];


    List<int> _itemIds = _cartItems.where((e)=> e.userDefinedCustomItem == false)
        .map((items) => items.itemId!).toList();

     debugPrint('ids : ${_itemIds}');
    try {
      _discountList =  await GraphQLService().retrieveDiscounts(itemIds: _itemIds);
    } catch (error) {
      print(error.toString());
      throw error;
    }
    print("discounts  length ${_discountList.length}",);

    _setDiscountList(_discountList);


  }


  /// make item selection for custom discount
   setItemSelection(int id,bool selected){
     this._itemList.where((item) => item.id == id).map((e) => e.selected = selected).toList();
     this._allItemsSelected =  (this._itemList.length == (this._itemList.where((item) => item.selected ==true).length));
     notifyListeners();
   }

   /// check all items selected or not
   setAllItemSelected(bool selected) {
    this._allItemsSelected = selected;
    this._itemList.map((e) => e.selected = selected).toList();
    notifyListeners();
   }

   bool checkAnyItemSelected(){
     return this._itemList.where((item) => item.selected == true).length > 0;
   }

  ///location employee list
  Future<List<LocationUserModel>> retrieveEmployees() async {
    List<LocationUserModel> _employees = [];

    try {
      _employees = await _location.getEmployees();
    } catch (err) {
      throw err;
    }
    this.employeeUserList(_employees);

    return _employees;
  }



  ///apply promo code on cart
  Future<CartPromoCodeResponse> applyPromoCode({required String promoCode}) async {
    CartPromoCodeResponse _cartPromoCodeResponse;
    try {
      _cartPromoCodeResponse = await _cartService.applyPromoCode(promoCode: promoCode,
      cartId: _cartData!.id!, user: _cartData!.user);
    } catch (error) {
      throw error;
    }
    return _cartPromoCodeResponse;
  }

  ///remove promo code  from cart
  Future<RemovePromotionsResponse> removePromoCode({required num id}) async {
    RemovePromotionsResponse _remoPromotionsResponse;
    try {
      _remoPromotionsResponse = await _cartService.removePromoCode(id: id,);
    } catch (error) {
      throw error;
    }
    return _remoPromotionsResponse;
  }

  ///remove custom discount  from cart or item
  Future<RemovePromotionsResponse> removeCustomDiscount({required num customDiscountId}) async {
    RemovePromotionsResponse _remoPromotionsResponse;
    try {
      _remoPromotionsResponse = await _cartService.removeCustomDiscount(customDiscountId: customDiscountId,);
    } catch (error) {
      throw error;
    }
    return _remoPromotionsResponse;
  }

  void setDiscountType(String type, Alignment knobAlignment) {
    this._customDiscountValue = 0;
    this._discountType = type;
    this._knobAlignment =knobAlignment;
    /// in case of custom cart discount applied
    /*if(this._customCartDiscount!=0) {
      this._customDisountError ="";
      this._customCartDiscount =0;
      this._totalSavings = 0;
     // manageOrderCalculation();
    }*/
    /// in case of custom item discount applied
   /* if(this._itemList.isNotEmpty) {
      this._itemList.where((item) => item.selected == true,).
      map((item) {
        item.afterPromotionValue = item.afterPromotionValue! + item.customDiscount!;
        item.customDiscount =0;
      }).toList();
      this._totalItemDiscount =0;
    }*/
    hasApplyCustomDiscountError(0,forCart: false);
    //manageOrderCalculation();
    notifyListeners();
  }
  void setDiscountTypeNotification() {
    if (this._cartData?.cartCustomDiscount != null && this._cartData!.cartCustomDiscount!.isNotEmpty) {
      this._knobAlignment = this._cartData!.cartCustomDiscount!.first.valueType == "Percentage" 
          ? Alignment.centerLeft 
          : Alignment.centerRight;
    }
    //manageOrderCalculation();
    notifyListeners();
  }

  void _setItemList(List<CartItems> items){
    this._itemList.clear();
    this._itemList.addAll(items);
    debugPrint('::::: items size ::::: ${this._itemList.length}');
    _applyPresetPromotionToItem();
    notifyListeners();
  }

  /// apply preset promotion to item
  _applyPresetPromotionToItem(){
    if(this._itemList.isNotEmpty) {
      if(null!= this._cartData){
        debugPrint('::::: has order ::::: ${this._cartData}');
        this._itemList.map((item) {
          item.afterPromotionValue  = item.lineTotal;
          item.customDiscount=0;

          ///order custom discount
          if (item.cartCustomDiscountItem!.isNotEmpty) {
            debugPrint('::::: has custom discount :::::');
            num _customDiscountTotal = item.cartCustomDiscountItem!.
            where((discountItem) => (discountItem.applyOn == PromotionApplyOn.product.name)
                || (discountItem.applyOn == PromotionApplyOn.multiple_product_qty.name) ).
           /* where((discountItem) => discountItem.applyOn == PromotionApplyOn.product.name ).*/
                fold(0, (i, item){
              return i + item.appliedValue!;
            });

            debugPrint('::::: custom discount ::::: on item $_customDiscountTotal');
            item.afterPromotionValue = item.afterPromotionValue! - roundDouble(_customDiscountTotal);
            debugPrint('::::: after custom discount ::: ${item.afterPromotionValue}');

          }

          ///order discount
          if (null != _cartData!.cartDiscount) {
            debugPrint('::::: has discount :::::');
            // discount applied on product or item
            if ( (_cartData!.cartDiscount!.applyOn == PromotionApplyOn.product.name) ||
                (_cartData!.cartDiscount!.applyOn == PromotionApplyOn.multiple_product_qty.name)) {
              debugPrint('::::: discount ::::: on item');
              if (null != item.cartDiscountItem) {
                debugPrint('::::: discount ::::: on item ${item.cartDiscountItem!.discount}');
                item.afterPromotionValue = item.afterPromotionValue! - roundDouble(item.cartDiscountItem!.discount!);
                debugPrint('::::: after discount ::: ${item.afterPromotionValue}');
              }
            }
          }
          /// order promo
          if(null!= _cartData!.cartPromoCode){
            debugPrint('::::: has promo :::::');
            // promo code applied on product or item
            if( (_cartData!.cartPromoCode!.applyOn == PromotionApplyOn.product.name) ||
                (_cartData!.cartPromoCode!.applyOn == PromotionApplyOn.multiple_product_qty.name)) {
              debugPrint('::::: promo ::::: on item');
              if(null != item.cartPromocodeItem) {
                debugPrint('::::: promo ::::: on item ${item.cartPromocodeItem!.discount}');
                item.afterPromotionValue = item.afterPromotionValue! - roundDouble(item.cartPromocodeItem!.discount!);
                debugPrint('::::: after promo ::: ${item.afterPromotionValue}');
              }
            }
          }

          /// order reward
          if(null!= _cartData!.cartReward){
            debugPrint('::::: has reward :::::');
            // reward applied on product or item
            if(_cartData!.cartReward!.rewardType!.toLowerCase() == RewardType.item.name) {
              debugPrint('::::: reward ::::: on item');
              if(null != item.cartRewardItem) {
                debugPrint('::::: reward ::::: on item ${item.cartRewardItem!.discount}');
                item.afterPromotionValue = item.afterPromotionValue! - roundDouble(item.cartRewardItem!.discount!);
                debugPrint('::::: after reward ::: ${item.afterPromotionValue}');
              }
            }
          }

          debugPrint('::::: after promotional ::: ${item.afterPromotionValue}');
          /* if(item.afterPromotionValue  == item.lineTotal){
            item.afterPromotionValue = 0;
          }*/
        }).toList();

      }
    }
  }

  void employeeUserList(List<LocationUserModel> employees) async {
    this.employeeList = employees;
    notifyListeners();
  }

  void setSelectedEmployeeIndex(int selectedEmployeeIndex) {
    this._selectedEmployeeIndex = selectedEmployeeIndex;
    //if(selectedEmployeeIndex!=-1)
    //this._selectedEmpEmail = this.employeeList[selectedEmployeeIndex].email ?? '';
    notifyListeners();
  }

  setCustomDiscountAuthorized(bool customAuthorized) {
    _customDiscountAuthorized = customAuthorized;
    notifyListeners();
  }

  setRegularDiscountAuthorized(bool regularAuthorized) {
    _regularDiscountAuthorized = regularAuthorized;
    notifyListeners();
  }

  setSuccessMsg(String msg) {
    this._successMsg = msg;
   // notifyListeners();
  }
  setPromoCodeInputError(bool hasError,String errorMsg) {
    _hasPromoCodeInputError = hasError;
    _promoCodeErrorMsg = errorMsg;
    notifyListeners();
  }

  setDiscountInputError(bool hasError,String errorMsg) {
    _hasDiscountInputError = hasError;
    _discountErrorMsg = errorMsg;
    notifyListeners();
  }

  setPreSetDiscountErrorMsg(String errorMsg) {
    _preSetDiscountErrorMsg = errorMsg;
    notifyListeners();
  }

  markDiscountAsUnApplied(){
    if(this._discountList.isNotEmpty){
      notifyListeners();
    }
  }


  void _setDiscountList(List<DiscountsData> discountList){
    this._discountList.clear();
    this._discountList.addAll(discountList);
    _hasDiscount =  this._discountList.isNotEmpty;
    notifyListeners();
  }

  setApplied(bool apply, num discountId) {
    _isApplied = apply;
    _discountId = discountId;
    notifyListeners();
  }

  String discountForApprove(int discountId) {
    if (_cartData != null) {
      if (_cartData!.cartCustomDiscount != null && _cartData!.cartCustomDiscount!.isNotEmpty) {
        final customDiscount = _cartData!.cartCustomDiscount!.first;
        if (customDiscount.id == discountId) {
          return customDiscount.valueType ?? '';
        }
      } else if (_cartData!.cartDiscount != null && _cartData!.cartDiscount!.discountId == discountId) {
        return _cartData!.cartDiscount!.valueType ?? '';
      }
    }
    return '';
  }

  reset(){
    this._discountList = [];
    this._hasDiscount  = false;
    this._isApplied = false;
    this._discountId = 0;
    this.employeeList =[];
    this._selectedEmployeeIndex = -1;
    this._hasDiscountInputError = false;
    this._customDiscountAuthorized =false;
    this._regularDiscountAuthorized =false;
    this._allItemsSelected =false;
    this._itemList = [];
    this._discountType ="\%";
    this._selectedEmpEmail = "";
    this._hasPromoCodeInputError =false;
    this._promoCodeErrorMsg = "";
    this._discountErrorMsg ="";
    this._successMsg ="";
    this._knobAlignment = Alignment.centerLeft;
    this._pin ="";
    this._customDiscountOnCartSelected =true;
    this._customDiscountOnItemSelected =false;
    this._hasCustomNextPressed =false;
    this._customCartDiscount =0;
    this._totalItemDiscount =0;
    this._newSubTotal =0;
    this._cartTotal =0;
   // this._order =null;
    this._subTotal=0;
    this._customDiscountError ="";
    this._customDiscountNote = "";
    this._customDiscountValue = 0;
    this._preSetDiscountErrorMsg = "";
    notifyListeners();
  }

  /// check if present from the list
  checkIfPresetAlreadyAppliedFromTheList(num discountId) {
    if(null != this._cartData) {
      if(null != this._cartData!.cartDiscount) {
        if(this._cartData!.cartDiscount!.discountId == discountId) return true;
      }
    }
    return false;
  }

  ///apply discount  on cart
  Future<CartDiscountResponse> applyDiscount({required num discountId}) async {
    CartDiscountResponse _orderDiscountResponse;
    try {
      _orderDiscountResponse = await _cartService.applyDiscount(discountId: discountId,
          cartId: _cartData!.id!,user: _cartData!.user);
    } catch (error) {
      throw error;
    }
    return _orderDiscountResponse;
  }

  ///remove discount  from cart
  Future<RemovePromotionsResponse> removeDiscount({required num id}) async {
    RemovePromotionsResponse _remoPromotionsResponse;
    try {
      _remoPromotionsResponse = await _cartService.removeDiscount(id: id,);
    } catch (error) {
      throw error;
    }
    return _remoPromotionsResponse;
  }


  ///apply custom discount on cart
  Future<CartCustomDiscountResponse> applyCustomDiscount() async {

    var dataObject = {};

    dataObject[ApiConstant.CART_ID] = this._cartData!.id!;
    dataObject[ApiConstant.USER_ID] = this._cartData!.user == null ? null : this._cartData!.user!.id!;
    dataObject[ApiConstant.USER_TYPE] = this._cartData!.user == null ? ApiConstant.USERTYPE_GUEST : ApiConstant.USERTYPE_REGISTERED;
    dataObject[ApiConstant.NOTE] = this._customDiscountNote;

    dataObject[ApiConstant.VALUE] = this._customDiscountValue;
    dataObject[ApiConstant.VALUE_TYPE] = this._discountType =='\$' ? ApiConstant.VALUE_TYPE_FLAT : ApiConstant.VALUE_TYPE_PERCENTAGE;
    dataObject[ApiConstant.APPLIED_VALUE] = this._customDiscountOnItemSelected ? this._totalItemDiscount  : this._customCartDiscount;
    dataObject[ApiConstant.APPLY_ON] = this._customDiscountOnCartSelected ? ApiConstant.APPLY_ON_CART : ApiConstant.APPLY_ON_PRODUCT;

    if(this._customDiscountOnItemSelected) {
      var _discounts =[];

      if(this.itemList.isNotEmpty){
        this.itemList.forEach((item) {

          if(item.customDiscount!!=0) {
            var _innerObj = {};
            _innerObj[ApiConstant.CART_ITEM_ID] = item.id!;
            _innerObj[ApiConstant.QTY] = item.itemQty!;
            _innerObj[ApiConstant.APPLIED_VALUE] = item.customDiscount!;
            _discounts.add(_innerObj);
          }
        });
      }
      dataObject[ApiConstant.DISCOUNTS] =_discounts;
    }
    debugPrint('::: object for send  ${jsonEncode(dataObject)} :::');

    CartCustomDiscountResponse _cartCustomDiscountResponse;
    try {
      _cartCustomDiscountResponse = await _cartService.applyCustomDiscount(data: dataObject);
    } catch (error) {
      throw error;
    }
    // debugPrint('::: applied ::: ${_cartCustomDiscountResponse.message}');
    return _cartCustomDiscountResponse;
  }


  ///Approve custom discount on cart
  Future<CartCustomDiscountResponse> approveCustomDiscount({required num? id,
    bool isApprove = false,
    required String? discountType,
    String? note}) async {
    var dataObject = <String, dynamic>{};

    dataObject[ApiConstant.DISCOUNT_ID] = id?.toInt(); // Convert num? to int if necessary
    dataObject[ApiConstant.APPROVED] = isApprove;
    dataObject[ApiConstant.DISCOUNT_TYPE] = discountType;
    if(note != null) {
      dataObject[ApiConstant.DISCOUNT_DECLINE_NOTE] = note;
    }

    late CartCustomDiscountResponse _cartCustomDiscountResponse;

    try {
      _cartCustomDiscountResponse = await _cartService.approveCustomDiscount(
        cartId: _cartData!.id!,
        data: dataObject,
      );
      return _cartCustomDiscountResponse;
    } on Exception catch (error) {
      // Handle the error as needed, e.g., log it or notify the user
      throw error;
    }
  }

}
