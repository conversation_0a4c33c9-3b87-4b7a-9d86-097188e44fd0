import 'package:flutter/cupertino.dart';
import '../../constants/app_string.dart';
import '../../models/cart/cart_data.dart';
import '../../models/cart/get_cart_data_response.dart';
import '../../models/payment/cart_transaction_response.dart';
import '../../models/payment_options/other_payment_options.dart';
import '../../services/cart/cart_service.dart';

class PaymentProvider with ChangeNotifier{
  final CartService _cartService = CartService();
  CartData? _cartData;

  CartData? get cartData => _cartData;

  int _selectedOtherPaymentIndex = -1;
  int get selectedOtherPaymentIndex => _selectedOtherPaymentIndex;

  bool _cashPayment = false;
  bool get cashPayment => _cashPayment;

  bool _otherPaymentFail = false;
  bool get otherPaymentFail => _otherPaymentFail;

  bool _receivePayment = false;
  bool get receivePayment => _receivePayment;

  bool _isOverPayment = false;
  bool get isOverPayment => _isOverPayment;

  String _cardType = '';
  String get cardType => _cardType;

  bool _cardPayment = false;
  bool get cardPayment => _cardPayment;

  bool _isPaymentSuccessFull = false;
  bool get isPaymentSuccessFull => _isPaymentSuccessFull;

  bool _isWaitingForCardPayment = true;
  bool get isWaitingForCardPayment => _isWaitingForCardPayment;

  bool _isCardPaymentAccepted = false;
  bool get isCardPaymentAccepted => _isCardPaymentAccepted;

  String _paymentTerminalNotFoundErrorMessage = "";
  String get paymentTerminalNotFoundErrorMessage => _paymentTerminalNotFoundErrorMessage;

  CartTransaction? _transaction;
  CartTransaction? get transaction => _transaction;


  void updateCartData(CartData? newCartData) {
    if (newCartData == null) {
      debugPrint("updateCartData called with null data");
      return;
    }

    debugPrint("updateCartData ${newCartData.dueAmount}");
    _cartData = newCartData;
    notifyListeners();
  }

  setReceivePayment(bool value){
    _receivePayment = value;
    _cashPayment = false;
    notifyListeners();
  }

  resetOtherPaymentSelection() {
    _selectedOtherPaymentIndex = -1;
    _cardType = '';
    _isOverPayment = false;
    _paymentTerminalNotFoundErrorMessage = '';
    notifyListeners();
  }

  setSelectedOtherPayment(String otherPaymentName, int index) {
    debugPrint(':: payment index $index $otherPaymentName::');
    switch(index){
      case 0: //VISA
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.visa.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 1: //MC
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.master.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 2: //AMEX
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.amex.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 3: //Debit
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.debit.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 4: //Uber
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.uber.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 5: //Gift Card
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.gift_card.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 6: //Other
        _selectedOtherPaymentIndex = index;
        _cardType = CardType.other.name;
        _paymentTerminalNotFoundErrorMessage = '';
        break;
      case 7: //Clover
        _selectedOtherPaymentIndex = index;
        _cardType = '';
        break;
    }
    debugPrint('Selected Other payment index is $_selectedOtherPaymentIndex');
    notifyListeners();
  }

  ///Get payment types
  String getPaymentType() {
    String _paymentType = '';
    if(selectedOtherPaymentIndex == 7) {
      _paymentType = PaymentType.credit;
    } else {
      _paymentType = PaymentType.nonIntegrated;
    }
    return _paymentType;
  }

  setCardPaymentSelected(bool value) {
    _cardPayment = value;
    _cashPayment = false;
    notifyListeners();
  }

  /// Cart payment
  Future<void> cartPayment(
      {required String paymentType, required num cartAmount, String cardType = 'other'}) async {
    CartTransactionResponse _transactionResponse;
    try {
      _transactionResponse = await _cartService.cartPayment(
          paymentType: paymentType, cartAmount: cartAmount, cardType: cardType, cartId: _cartData!.id!);
      await getCart(id: _cartData!.id!);
      if (_transactionResponse.payload != null) {
        updateTransaction(_transactionResponse.payload!);
      }
    } catch (error) {
      throw error;
    }
  }

  /// get cart
  Future<void> getCart({required int id}) async {
    GetCartDataResponse cartDataResponse;
    try {
      cartDataResponse = await  _cartService.getCart(
        id: id.toString(),
      );
      updateCartData(cartDataResponse.payload!);
    } catch (error, stacktrace) {
      debugPrint('$stacktrace');
      throw error;
    }
  }


  ///Update transaction value of order
  void updateTransaction(CartTransaction transaction) {
    _transaction = transaction;
    transaction.status == 'success'
        ? _isPaymentSuccessFull = true
        : _isPaymentSuccessFull = false;
    notifyListeners();
  }

  setCashPaymentSelected(bool value){
    _cashPayment = value;
    notifyListeners();
  }

  setOtherPaymentFail(bool value){
    _otherPaymentFail = value;
    notifyListeners();
  }

  setPaymentSuccessFull(bool isPaymentSuccessFull) {
    _isPaymentSuccessFull = isPaymentSuccessFull;
    notifyListeners();
  }

  setWaitingForCardPayment(
      bool isWaitingForCardPayment, bool isCardPaymentAccepted) {
    _isWaitingForCardPayment = isWaitingForCardPayment;
    _isCardPaymentAccepted = isCardPaymentAccepted;
    notifyListeners();
  }

  ///Set error message when payment terminal not assigned to pos station and try to place order using oher payment method
  setPaymentTerminalNotFoundErrorMessage(String errorMessage) {
    _paymentTerminalNotFoundErrorMessage = errorMessage;
    notifyListeners();
  }

  resetCashCardPaymentFlag() {
    _receivePayment = false;
    _cashPayment = false;
    _cardPayment = false;
    _isPaymentSuccessFull = false;
    _isWaitingForCardPayment = true;
    _isCardPaymentAccepted = false;
    notifyListeners();
  }

  reset() {
    _selectedOtherPaymentIndex = -1;
    _isOverPayment = false;
    _cardType = '';
    _cashPayment = false;
    _receivePayment = false;
    _paymentTerminalNotFoundErrorMessage = "";
    _cardPayment = false;
    _transaction = null;
    _otherPaymentFail = false;
    _isCardPaymentAccepted = false;
    _isWaitingForCardPayment = true;
    notifyListeners();
  }
}