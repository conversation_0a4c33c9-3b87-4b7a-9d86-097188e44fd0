import 'package:flutter/material.dart';

import '../../models/cart/cart_data.dart';
import '../../models/cart/cart_response.dart';
import '../../services/history/history_service.dart';

class HistoryViewProvider with ChangeNotifier{

  HistoryService _historyService = HistoryService();

  String _selectedSortName = '';
  String get selectedSortName => _selectedSortName;

  String _selectedSortOrder = '';
  String get selectedSortOrder => _selectedSortOrder;

  int _currentPage = 1;
  int get currentPage => _currentPage;

  int _totalPages = 1;
  int get totalPages => _totalPages;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String _selectedSortBy = '';
  String get selectedSortBy => _selectedSortBy;

  List<CartData> _cartList = [];
  List<CartData> get cartList => _cartList;

  setCurrentPage(String search){
    _currentPage++;
      /// if current page is last page then no need to call
     if(_currentPage >_totalPages) {
       _currentPage = _totalPages;
       return;
     }
    getHistoryCartOrder(search: search,sort:selectedSortOrder ,sortBy: _selectedSortBy);
  }

  resetCurrentPage(){
    _currentPage = 1;
    _totalPages = 1;
    _cartList.clear();
    notifyListeners();
  }

  setLoading(bool value){
    _isLoading = value;
    notifyListeners();
  }

  setSelectedOrderSort(String name,String sortOrder,String sortBy){
    _selectedSortName = name;
    _selectedSortOrder = sortOrder;
    _selectedSortBy = sortBy;
    _currentPage = 1;
    _totalPages = 1;
    _isLoading = false;
    _cartList.clear();
    notifyListeners();
  }

  setCartSelected(CartData cartData) {
    if (!cartData.isSelected!) {
      cartData.isSelected = true;

      _cartList
          .where((e) => e.id != cartData.id)
          .map((e) => e.isSelected = false)
          .toList();

      notifyListeners();
    }
  }

  ///Update cart list
  updateCartList(List<CartData> cartList) {
    _cartList.addAll(cartList);
    notifyListeners();
  }

  reset(){
    this._currentPage =1;
    this._totalPages = 1;
    this._isLoading =false;
    _cartList = [];
    resetSorting();
    notifyListeners();
  }

  resetSorting() {
    _selectedSortName = '';
    _selectedSortOrder = '';
    _selectedSortBy ='';
    notifyListeners();
  }



  ///Get Cart list for history
  Future<void> getHistoryCartOrder({String search = '',String sort = '',String sortBy = '',}) async {
    List<CartData> tmpCartList = [];
    CartPayloadData? _cartPayload;

    try {
      setLoading(true);
      _cartPayload = await _historyService.getHistoryCartOrder(
          search: search,
          sort: sort,sortBy: sortBy,
          page: '$_currentPage');
      setLoading(false);

      if (_cartPayload != null) {
        _totalPages = _cartPayload.totalPages ?? 1;
        tmpCartList.addAll((_cartPayload.carts ?? []));

        if (tmpCartList.isNotEmpty) {
          updateCartList(tmpCartList.cast<CartData>());
        }
      }
    } catch (error, stacktrace) {
      _cartList = [];
      debugPrint('$stacktrace');
      notifyListeners();
      throw error;
    }

  }

}