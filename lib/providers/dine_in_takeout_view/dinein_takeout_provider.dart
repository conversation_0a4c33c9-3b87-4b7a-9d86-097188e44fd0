import 'package:flutter/cupertino.dart';

import '../../constants/app_string.dart';
import '../../models/cart/cart_data.dart';
import '../../models/cart/cart_response.dart';
import '../../models/cart/cart_user.dart';
import '../../models/dinein_takeout/floors_response.dart';
import '../../services/cart/cart_service.dart';
import '../../utils/app_utils.dart';

class DineInTakeoutProvider with ChangeNotifier{

  final CartService _cartService = CartService();

  List<FloorData> _floorList = [];
  List<FloorData> get floorList => _floorList;

  List<CartData> _cartList = [];
  List<CartData> get cartList => _cartList;

  int _currentPage = 1;
  int get currentPage => _currentPage;

  int _totalPages = 1;
  int get totalPages => _totalPages;

  bool _isLoading = false;
  bool get isLoading => _isLoading;



  /// mark floor selected
  markFloorSelected(FloorData floor) {
    if (!floor.isSelected!) {
      floor.isSelected = true;

      _floorList
          .where((e) => e.id != floor.id)
          .map((e) => e.isSelected = false)
          .toList();

      notifyListeners();
    }
  }

  /// get tables of selected
  /// floor
  getFloorTables() {
    if(_floorList.isEmpty) return [];

    return floorList.firstWhere((floor) => floor.isSelected!).tables ??[];
  }

  ///get floors with tables
  Future<void> getFloorWithTables() async {

    List<FloorData> floors = [];
    try {
      floors = await _cartService.getFloors();

      _floorList.clear();
      _floorList = List.of(floors);
       if(_floorList.isNotEmpty) {
         markFloorSelected(_floorList.first);
       }
       notifyListeners();

    } catch (error) {

      _floorList.clear();
      notifyListeners();
      throw error;
    }
  }

  ///Get Cart  list
  Future<void> getCartList({String search = '',}) async {
    List<CartData> tmpCartList = [];
    CartPayloadData? cartPayloadData;

    try {
      setLoading(true);
      cartPayloadData = await _cartService.getOrders(
          search: search,
          page: '$_currentPage');

      setLoading(false);
      if (cartPayloadData != null) {
        _totalPages = cartPayloadData.totalPages ?? 1;
        tmpCartList.addAll(cartPayloadData.carts ?? []);

        if (tmpCartList.isNotEmpty) {
          updateCartList(tmpCartList);
        }
      }

    } catch (error, stacktrace) {
      _cartList = [];
      setLoading(false);
      debugPrint('$stacktrace');
      notifyListeners();
      //throw error;
    }

  }

  setLoading(bool value){
    _isLoading = value;
    notifyListeners();
  }

  ///Update cart list
  updateCartList(List<CartData> cartList) {
    if(_currentPage == 1 ){
      /// intentionally done as ux becomes bad
      /// if this one not written here then
      _cartList.clear();
    }
    _cartList.addAll(cartList);
    notifyListeners();
  }

  /// get formatted customer name
  String getCustomerName(CartUser? user, {bool isRemoveNextLine = true}) {
    if (user == null) return isRemoveNextLine ? 'Guest' : 'Guest\n';

    return user.firstName!.isNotEmpty
        ? '${user.firstName!} ${user.lastName!}'.length > 23
        ? '${user.firstName!} ${user.lastName!}'
        : isRemoveNextLine
        ? '${user.firstName!} ${user.lastName!}'
        : '${user.firstName!} ${user.lastName!}\n'
        : user.email!.isNotEmpty
        ? user.email!.length > 23
        ? user.email!
        : isRemoveNextLine
        ?  user.email!
        : '${user.email!}\n'
        : isRemoveNextLine
        ? getFormattedPhoneNumber(user.phone!)
        : '${getFormattedPhoneNumber(user.phone!)}\n';
  }

  setCurrentPage(String search) {
    _currentPage++;

    /// if current page is last page then no need to call
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
      return;
    }
    getCartList(search: search);
  }

  resetCurrentPage() {
    _currentPage = 1;
    _totalPages = 1;
  }

  reset(){
     _floorList = [];
     _cartList = [];
     _currentPage = 1;
     _totalPages = 1;
     _isLoading = false;
    notifyListeners();
  }
}