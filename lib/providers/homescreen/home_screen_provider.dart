
import 'package:flutter/cupertino.dart';

import '../../constants/app_string.dart';
import '../../screens/alerts/alerts_view.dart';
import '../../screens/history/history_view.dart';
import '../../screens/home/<USER>';
import '../../screens/orders/dine_in_takeout_view.dart';

class HomeScreenProvider extends ChangeNotifier {

  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  List<int> _navigationStack = []; // Tracks the tab history
  List<int> get navigationHistory => _navigationStack;

  String _appBarTitle = 'Home';
  String get appBarTitle => _appBarTitle;

  Widget _pageWidget = HomeScreenContentView();  // Default page
  Widget get pageWidget => _pageWidget;

  String _activeMenuName = NavigationString.homeView;  // Default page
  String get activeMenuName => _activeMenuName;


  // Update the selected page based on the navigation string
  void updateSelectedPage(String selectedPage) {
    switch (selectedPage) {
      case BottomBarNavigationString.home:
        _currentIndex = 0;
        _appBarTitle = NavigationString.homeView;
        _pageWidget = DineInTakeoutView();
        break;

      case BottomBarNavigationString.order:
        _currentIndex = 1;
        _appBarTitle = NavigationString.orderView;
        _pageWidget = DineInTakeoutView();
        break;

      case BottomBarNavigationString.history:
        _currentIndex = 2;
        _appBarTitle = BottomBarNavigationString.history;
        _pageWidget = HistoryView();
        break;

      case BottomBarNavigationString.alerts:
        _currentIndex = 3;
        _appBarTitle = BottomBarNavigationString.alerts;
        _pageWidget = AlertsView();
        break;

      // default:
      //   _appBarTitle = NavigationString.homeView; // Default values
      //   _pageWidget = HomeScreenContentView();
    }
    _navigationStack.add(currentIndex);
    // Notify listeners about the change
    notifyListeners();
  }

  setCurrentIndex(currentIndex){
    _currentIndex = currentIndex;
    notifyListeners();
  }

  manageNavigationStack(){
    _navigationStack.removeLast(); // Remove the last page
    _currentIndex = _navigationStack.last; // Go to the previous page
    updateSelectedIndex(_currentIndex);
    notifyListeners();
  }

  void updateSelectedIndex(int index) {
    switch (index) {
      case 0:
        _activeMenuName =  BottomBarNavigationString.home;
        _appBarTitle = NavigationString.homeView;
        _pageWidget = DineInTakeoutView();
        notifyListeners();
        break;

      case 1:
        _activeMenuName =  BottomBarNavigationString.order;
        _appBarTitle = NavigationString.orderView;
        _pageWidget = DineInTakeoutView();
        notifyListeners();
        break;

      case 2:
        _activeMenuName =  BottomBarNavigationString.history;
        _appBarTitle = BottomBarNavigationString.history;
        _pageWidget = HistoryView();
        notifyListeners();
        break;

      case 3:
        _activeMenuName =  BottomBarNavigationString.alerts;
        _appBarTitle = BottomBarNavigationString.alerts;
        _pageWidget = AlertsView();
        notifyListeners();
        break;

    }
  }

  setActiveMenuName(String menuName){
    _activeMenuName = menuName;
    notifyListeners();
  }


  // Reset method to go back to the default home page
  void reset() {
    _appBarTitle = NavigationString.homeView;
    _pageWidget = HomeScreenContentView();
    _activeMenuName =  BottomBarNavigationString.home;
    _currentIndex = 0;

    notifyListeners();
  }
}
