import 'package:flutter/cupertino.dart';

class BottomSheetProvider with ChangeNotifier {

  bool  _showDialogHome = false;
  bool get showDialogHome => _showDialogHome;

  bool  _showDialogCart = false;
  bool get showDialogCart => _showDialogCart;

  bool  _showDialogDine = false;
  bool get showDialogDine => _showDialogDine;

  bool  _showDialogHistory = false;
  bool get showDialogDineHistory => _showDialogHistory;

  bool  _showDialogAlerts = false;
  bool get showDialogAlerts => _showDialogAlerts;

  setDialogForHomePage(bool value){
    if(value){
      _showDialogHome = true;
      _showDialogCart = false;
      _showDialogDine = false;
      _showDialogHistory = false;
      _showDialogAlerts = false;
    }else{
      _showDialogHome = false;
      _showDialogCart = false;
      _showDialogDine = false;
      _showDialogHistory = false;
      _showDialogAlerts = false;
    }
    notifyListeners();
  }

  setDialogForCartPage(bool value){
     if(value){
       _showDialogCart = true;
       _showDialogDine = false;
       _showDialogHome = false;
       _showDialogHistory = false;
       _showDialogAlerts = false;
     }else{
       _showDialogCart = false;
       _showDialogDine = false;
       _showDialogHome = false;
       _showDialogHistory = false;
       _showDialogAlerts = false;
     }
     notifyListeners();
  }

  setDialogForDinePage(bool value){
    if(value){
      _showDialogDine = true;
      _showDialogCart = false;
      _showDialogHome = false;
      _showDialogHistory = false;
      _showDialogAlerts = false;
    }else{
      _showDialogCart = false;
      _showDialogDine = false;
      _showDialogHome = false;
      _showDialogHistory = false;
      _showDialogAlerts = false;
    }
    notifyListeners();
  }

  reset(){
    _showDialogHome = false;
    _showDialogCart = false;
    _showDialogDine = false;
    _showDialogHistory = false;
    _showDialogAlerts = false;
    notifyListeners();
  }
}