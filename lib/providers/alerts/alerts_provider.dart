import 'package:flutter/material.dart';
import '../../constants/app_string.dart';
import '../../custom_widgets/alerts_widget.dart';
import '../../custom_widgets/app_text_style.dart';


enum AlertType { snackBar, alertDialog, confirmationDialog }

class AlertModel {
  final AlertType alertType;
  final String title;
  final String message;
  final VoidCallback? alertOnPressed;
  final String? alertButtonTitle;
  final VoidCallback? leftButtonOnPressed;
  final String? leftButtonTitle;
  final VoidCallback? rightButtonOnPressed;
  final String? rightButtonTitle;
  final int? duration;
  final bool? isMilliseconds;

  AlertModel(
      {required this.alertType,
      required this.title,
      required this.message,
      this.alertOnPressed,
      this.alertButtonTitle = ConstantString.ok,
      this.leftButtonOnPressed,
      this.leftButtonTitle = ConstantString.yes,
      this.rightButtonOnPressed,
      this.rightButtonTitle = ConstantString.cancel,this.duration,this.isMilliseconds});
}

class AlertsProvider with ChangeNotifier {
  final GlobalKey<NavigatorState> _navigatorKey;
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey;

  AlertsProvider({
    required GlobalKey<NavigatorState> navigatorKey,
    required GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey,
  })  : _navigatorKey = navigatorKey,
        _scaffoldMessengerKey = scaffoldMessengerKey;

  AlertModel _alert = AlertModel(alertType: AlertType.alertDialog, title: 'default', message: 'default',duration: 1 , isMilliseconds : false,);

  AlertModel get alert => _alert;
  bool _isDialogShown = false;

  void setNewAlert({required AlertModel alertData}) {
    if (_isDialogShown) {
      //dismiss already shown dialog
      dismissDialog();
    }

    _alert = alertData;
    switch (_alert.alertType) {
      case AlertType.snackBar:
        _showSnackBar();
        break;
      case AlertType.alertDialog:
        _showAlertDialog();
        _isDialogShown = true;
        break;
      case AlertType.confirmationDialog:
        _showConfirmationDialog();
        _isDialogShown = true;
        break;
    }
  }

  void _showAlertDialog() {
    showDialog(
            context: _navigatorKey.currentState!.overlay!.context,
            builder: (_) => getAlertDialog(alert, dismissDialog, _navigatorKey.currentState!.overlay!.context),
            barrierDismissible: false)
        .then((value) => _isDialogShown = false);
  }

  void _showConfirmationDialog() {
    showDialog(
            context: _navigatorKey.currentState!.overlay!.context,
            builder: (_) => getConfirmationDialog(alert, onYes: (){},onNo: (){},),
            barrierDismissible: false)
        .then((value) => _isDialogShown = false);
  }

  void _showSnackBar() {
    _scaffoldMessengerKey.currentState!.showSnackBar(
          SnackBar(
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(left: MediaQuery.of(_navigatorKey.currentState!.overlay!.context).size.width/2, right: 20.0,bottom: MediaQuery.of(_navigatorKey.currentState!.overlay!.context).size.height - 100),
            duration: alert.isMilliseconds ?? false  ? Duration(milliseconds: alert.duration ?? 500) : Duration(seconds: alert.duration ?? 1),
            content: Text(
              alert.message,
              style: AppTextStyle.mediumTextStyle.copyWith(
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
            backgroundColor: Colors.white,
          ),
        );
  }

  void dismissDialog() {
    try {
      if (Navigator.canPop(_navigatorKey.currentState!.overlay!.context)) {
        Navigator.of(_navigatorKey.currentState!.overlay!.context).pop();
      }
    } catch (err) {
      debugPrint(err.toString());
    } finally {
      _isDialogShown = false;
    }
  }
}
