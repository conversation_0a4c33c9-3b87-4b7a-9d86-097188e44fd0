import 'dart:collection';
import 'dart:convert';

import 'package:flutter/material.dart';

import '../../constants/api_constant.dart';
import '../../constants/app_color.dart';
import '../../constants/app_string.dart';
import '../../models/auth/user_model.dart';
import '../../models/cart/cart_users/cart_user_data.dart';
import '../../models/cart/create_cart_item_object.dart';
import '../../models/cart/create_cart_item_response.dart';
import '../../models/menu/menu_data.dart';
import '../../services/cart/cart_service.dart';
import '../../utils/app_utils.dart';
import '../../utils/pref_utils.dart';

class ItemModifierViewProvider with ChangeNotifier{


   final CartService _cartService = CartService();

   Items? _itemData;
   Items? get itemData => _itemData;

   List<ItemModifiers> _itemModifiers=[];
   List<ItemModifiers> get itemModifiers => _itemModifiers;

   List<ModifierItems> _modifierItems=[];
   List<ModifierItems> get modifierItems => _modifierItems;

   Items? _itemWithDefaultData;
   Items? get itemWithDefaultData => _itemWithDefaultData;

   num _actualPrice = 0;
   num get actualPrice => _actualPrice;

   Items? _itemsObject;
   Items? get itemsObject => _itemsObject;

   int _itemCount = 1;
   int get itemCount => _itemCount;

   ///
   Map<int, int> validationDataList = {};
   List<ItemModifiers> tempImList = [];
   int includedCount = 0;

   ///list of all modifier item, which is later used to create api request object
   List<ModifierItems> modifierItemListForRef = [];
   ///list of all modifier, which is later used to create api request object
   List<ItemModifiers> modifierListForRef = [];

   int _selectedCourseId = 0;
   int get selectedCourseId => _selectedCourseId;

   List<ItemCourse> _itemCourseList=[];
   List<ItemCourse> get itemCourseList => _itemCourseList;

   List<ItemTags> _itemTags=[];
   List<ItemTags> get itemTags => _itemTags;


   String? _notes = '';
   String? get notes => _notes;

   CartUserData? _selectedUserData;
   CartUserData? get selectedUserData => _selectedUserData;

   String? _categoryName = '';
   String? get categoryName => _categoryName;

   setItemData(Items data,String categoryName) async{
     _itemData = data;
     _itemsObject = Items.deepCopy(data);
     _itemWithDefaultData = Items.deepCopy(data);
     _itemWithDefaultData?.itemModifiers = data.itemModifiers!.map((e) =>
         ItemModifiers.deepCopy(e)).toList();

     _itemsObject?.itemModifiers = <ItemModifiers>[];
     _itemsObject?.categoryId = data.categoryId!;

     tempImList.clear();
     includedCount = 0;
     _itemCount = 1;
     _notes ='';
     _categoryName = categoryName;
     _selectedCourseId =0;
     _itemModifiers.clear();
     _itemTags.clear();
     validationDataList.clear();
     _itemModifiers.addAll(data.itemModifiers!.map((e) =>
         ItemModifiers.deepCopy(e)).toList());
     _itemTags.addAll(data.itemTags!.map((e) => ItemTags.deepCopy(e)).toList());


     modifierListForRef.clear();
     modifierItemListForRef.clear();



     for (var i = 0; i < _itemModifiers.length; ++i) {
       ItemModifiers im = ItemModifiers.deepCopy(_itemModifiers[i]);
       im.modifierItems?.clear();

       modifierListForRef.add(im);
       for (var j = 0; j < _itemModifiers[i].modifierItems!.length; ++j) {
         //if status is true then only consider it
         if (_itemModifiers[i].modifierItems![j].status!) {

           modifierItemListForRef.add(ModifierItems.deepCopy(_itemModifiers[i].modifierItems![j]));
         }
       }
     }

     modifierItemListForRef.forEach((element) {
       element.qty = element.min ?? 0;
       element.included = element.included ?? 0;
     });

     await makeQuantitySameWithMinCount();

     _actualPrice = calculateActualPriceOfItem(_itemData!);
     checkItemValidation();

     notifyListeners();
   }

   ///
   /// Item validation note string and validation list creation
   /// Ex. When item modifier has min = 2 and max = 2 => 'Select at-least "min" up to "max"'
   /// Ex. When item modifier has min = 0 and max > 0 => 'Select at-least "max"'
   /// other conditions are covered below in if else.
   ///
   String makeItemLimitNote(ItemModifiers modifierCategory) {
     List<bool> isAllItemStatusIsFalse = [];
     for (var i = 0; i < modifierCategory.modifierItems!.length; ++i) {
       isAllItemStatusIsFalse
           .add(modifierCategory.modifierItems![i].status!);
     }
     if (!isAllItemStatusIsFalse.contains(true)) {
       debugPrint('all false');
       return '';
     }
     int min = modifierCategory.min!, max = modifierCategory.max!;
     if (min == 0 && max == 0) {
       return '';
     } else if (min == 0 && max > 0) {
       return ItemModifierViewString.selectUpToX.replaceAll('X', '$max');
     } else if (min >= 1 && max > 1) {
       if (validationDataList.isEmpty) validationDataList = {};

       validationDataList.putIfAbsent(
           modifierCategory.modifierId!, () => modifierCategory.min!);

       return ItemModifierViewString.selectAtLeastXupToY
           .replaceAll('X', '$min')
           .replaceAll('Y', '$max');
     } else if (min == max) {
       if (validationDataList.isEmpty) validationDataList = {};

       validationDataList.putIfAbsent(
           modifierCategory.modifierId!, () => modifierCategory.min!);
       return ItemModifierViewString.selectAtLeastX.replaceAll('X', '$max');
     } else {
       debugPrint('Alert: if-else by passed. Review data & conditions!!!!!');
       return '';
     }
   }

   getColorAfterValidation(ItemModifiers modifierCategory) {
     if (modifierCategory.min! >= 1) {
       int itemModifierIndex = tempImList.indexWhere((element) =>
       '${element.modifierId}' == '${modifierCategory.modifierId}');
       if (itemModifierIndex >= 0 &&
           getAllModifierItemQuantity(tempImList
               .elementAt(itemModifierIndex)
               .modifierItems!) >=
               modifierCategory.min!) {
         return ColorConstant.colorGrayLight;
       } else {
         return ColorConstant.colorRedDark;
       }
     } else {
       return ColorConstant.colorGrayLight;
     }
   }

   /// This method will check that requested [itemId] is already selected or not.
   /// [tempImList] collect selected data. So, if [tempImList] has requested [itemId] in it, then it's already selected
   /// [tempImList.indexWhere] return -1 if there are no matching data in list.
   ///
   bool isSelected(int parentIndex,int childIndex, int categoryId, int itemId) {

     debugPrint(':: is selected ${tempImList.length}  ::');
     int indexOfIm = tempImList
         .indexWhere((element) => element.modifierId == categoryId);
     if (tempImList.isNotEmpty && indexOfIm >= 0) {
       var im = tempImList.elementAt(indexOfIm);
       return im.modifierItems != null &&
           im.modifierItems!.indexWhere(
                   (element) => element.modifierItemId == itemId) >=
               0;
     } else {
       return false;
     }
   }

   int getAllModifierItemQuantity(List<ModifierItems> modifierItems) {
     int totalQuantity = 0;
     for (var i = 0; i < modifierItems.length; ++i) {
       if (modifierItems[i].status!) {
         totalQuantity += (modifierItems[i].qty ?? 0) as int;
       }
     }
     return totalQuantity;
   }

   void updateSelection(
       ItemModifiers modifierCategory, ModifierItems modifierItem) {
     int group = modifierCategory.modifierId!;
     int child = modifierItem.modifierItemId!;

     debugPrint(':: group $group  child $child');

     try{
       ItemModifiers im = ItemModifiers.deepCopy(modifierListForRef.
       elementAt(modifierListForRef.
       indexWhere((element) => element.modifierId! == group)));

       ModifierItems mi = ModifierItems.deepCopy(modifierItemListForRef.
       elementAt(modifierItemListForRef.
       indexWhere((element) => element.modifierItemId == child)));

       debugPrint(':: im $im  mi $mi');

       debugPrint(':: tempImList ${tempImList.length}  ::');

       if (tempImList.isEmpty ||
           tempImList.indexWhere(
                   (element) => element.modifierId == im.modifierId) ==
               -1) {
         debugPrint(':: tempImList ${tempImList.length}  if ::');
         tempImList.add(im);
       } else {
         debugPrint(':: tempImList ${tempImList.length} else  ::');
         im = ItemModifiers.deepCopy(tempImList
             .elementAt(tempImList.indexWhere(
                 (element) => element.modifierId == im.modifierId)));

         if (im.modifierItems!.indexWhere((element) =>
         element.modifierItemId == mi.modifierItemId) !=
             -1) {
           mi =  ModifierItems.deepCopy(im.modifierItems!
               .elementAt(im.modifierItems!.indexWhere((element) =>
           element.modifierItemId == mi.modifierItemId)));
         }
       }
       includedCount = im.included ??= 0;

       if (im.modifierItems!.indexWhere((element) =>
       element.modifierItemId == mi.modifierItemId) >=
           0) {
         im.modifierItems!.removeWhere(
                 (element) => element.modifierItemId == mi.modifierItemId);

         int index = tempImList.indexWhere(
                 (element) => element.modifierId == im.modifierId);
         tempImList.removeAt(index);
         tempImList.insert(index, im);

         if (im.modifierItems!.isEmpty) {
           tempImList.removeWhere(
                   (element) => element.modifierId == im.modifierId);
         }

         /*=========THIS IS CODE TO DISTRIBUTE INCLUDING COUNT IF ANY PLUS MINUS FOUND==========*/
         if (tempImList.isNotEmpty) {
           int alreadyIncluded = 0;
           for (var i = 0; i < im.modifierItems!.length; ++i) {
             debugPrint('::log ====== =======  ${im.modifierItems![i].included}:');
             alreadyIncluded += im.modifierItems![i].included as int;
           }
           int remainingIncluded = im.included! - alreadyIncluded;
           for (var j = 0; j < im.modifierItems!.length; ++j) {
             if (im.modifierItems![j].qty! >
                 (im.modifierItems![j].included ?? 0) &&
                 remainingIncluded > 0) {
               int difference = im.modifierItems![j].qty! -
                   (im.modifierItems![j].included ?? 0) as int;
               if (difference <= remainingIncluded) {
                 im.modifierItems![j].included =
                     (im.modifierItems![j].included ?? 0) + difference;
                 remainingIncluded -= difference;
               } else if (difference > remainingIncluded) {
                 im.modifierItems![j].included =
                     (im.modifierItems![j].included ?? 0) +
                         remainingIncluded;
                 remainingIncluded = 0;
                 break;
               }
             }
           }
         }
         /*=========ABOVE CODE IS TO DISTRIBUTE INCLUDING COUNT IF ANY PLUS MINUS FOUND==========*/
       } else if ((im.max == 1 ||
           getAllModifierItemQuantity(im.modifierItems!) < im.max!) ||
           (im.min == 0 && im.max == 0)) {
         if (im.max == 1) {
           ///here we have cleared [im.modifierItems] cause max value is 1 so it will only make one item selected at a time and old will be removed
           ///works similar to radio button
           im.modifierItems!.clear();
         }
         mi.qty = (mi.qty ??= 0) + 1;

         mi.included = updateIncludedCount(im, mi);

         im.modifierItems!.add(mi);

         int index = tempImList.indexWhere(
                 (element) => element.modifierId == im.modifierId);
         tempImList.removeAt(index);
         tempImList.insert(index, im);
         ////notifyListeners();

       } else {
         //notifyListeners();
         return;
       }
     }catch(e, stacktrace) {
       debugPrint('$stacktrace');
     }


     debugPrint('tempImList  ${tempImList.length}');
     _itemData?.itemModifiers = tempImList.isEmpty ? _itemModifiers : tempImList;
     _actualPrice = calculateActualPriceOfItem(_itemData!);
     notifyListeners();
   }

   /*
  *
  * modifierItem qty is calculated with min and max limit
  * [tempImList] has all data while selecting item and from below count will be returned after calculation
  *
  * */
   String getModifierItemQuantity(
       ItemModifiers modifierCategory, ModifierItems modifierItem) {
     int itemModifierIndex = tempImList.indexWhere((element) =>
     element.modifierId == modifierCategory.modifierId);
     if (itemModifierIndex == -1) {
       return '1';
     } else {
       ItemModifiers im = ItemModifiers.deepCopy(tempImList.elementAt(itemModifierIndex));
       int modifierIndex = im.modifierItems!.indexWhere((element) =>
       element.modifierItemId == modifierItem.modifierItemId);
       if (modifierIndex >= 0) {
         ModifierItems mi =ModifierItems.deepCopy(
             im.modifierItems!.elementAt(modifierIndex));
         return '${mi.qty}';
       } else {
         return '1';
       }
     }
   }

   /*
  *
  * here we will check qty against included count.
  * - qty and included count difference(qty - included) will be returned here
  * this returned value is used to show and count modifierItem price
  *
  * Ex. qty=3, included=3 => difference=0, modifierItem price is not calculated
  *     qty=2, included=1 => difference=1, 1 times modifierItem price is calculated(price*difference)
  *     qty=2, included=0 => difference=2, 2 times modifierItem price is calculated(price*difference)
  *
  * */
   int getNonIncludedCount(
       ItemModifiers modifierCategory, ModifierItems modifierItem) {
     int nonIncludedItemCount = 0;
     int categoryIndex = tempImList.indexWhere((element) =>
     element.modifierId == modifierCategory.modifierId);
     if (categoryIndex >= 0) {
       ItemModifiers im = ItemModifiers.deepCopy(tempImList.elementAt(categoryIndex));
       int indexOfMi = im.modifierItems!.indexWhere((element) =>
       element.modifierItemId == modifierItem.modifierItemId);
       if (indexOfMi >= 0) {
         ModifierItems mi =
         ModifierItems.deepCopy(im.modifierItems!.elementAt(indexOfMi));
         nonIncludedItemCount = (mi.qty ?? 0) - mi.included as int;
       }
     }
     return nonIncludedItemCount;
   }
   /*
  *
  * included count is distributed in first come, first serve basis.
  * Ex. we have few item named A, B and C
  * if i select B, A, B, C respectively and we have 3 included count then included will be assigned as below
  * Total included count is 3.
  * B = 1 (remaining included count = 2)
  * A = 1 (remaining included count = 1)
  * B = 1+1 (remaining included count = 0)
  * C = 0 (remaining included count = 0)
  * */
   updateIncludedCount(ItemModifiers im, ModifierItems mi) {
     int alreadyIncluded = 0;
     for (var i = 0; i < im.modifierItems!.length; ++i) {
       alreadyIncluded += (im.modifierItems![i].included) as int;
     }
     int remainingIncluded = (im.included ?? 0) - alreadyIncluded;

     if (remainingIncluded == 0) {
       mi.included = mi.included ?? 0;
     } else if (remainingIncluded > 0) {
       mi.included = (mi.included ?? 0) + 1;
       remainingIncluded = remainingIncluded - 1;
     }
     //print('included count set to MI is: ${mi.included}');
     return mi.included;
   }

   /*
  * Here, 'qty' is checked against 'min' value,
  * 'qty' > 'min' = minus button is enabled.
  * 'qty' <= 'min' = minus button is disabled.
  * */
   bool checkForMinimum(categoryId, itemId) {
     int indexOfCategory = tempImList
         .indexWhere((element) => element.modifierId == categoryId);
     if (tempImList.isNotEmpty && indexOfCategory >= 0) {
       var im = tempImList.elementAt(indexOfCategory);
       int indexOfModifierItem = im.modifierItems!
           .indexWhere((element) => element.modifierItemId == itemId);
       if (im.modifierItems != null && indexOfModifierItem >= 0) {
         ModifierItems mi =ModifierItems.deepCopy(
             im.modifierItems!.elementAt(indexOfModifierItem));
         return (mi.qty ?? 0) > (mi.min ?? 0);
       } else {
         return false;
       }
     } else {
       return false;
     }
   }


   void updatePlusMinusSelection( int parentIndex, int childIndex, int value, ItemModifiers modifierCategory, ModifierItems modifierItem) {
     int group = modifierCategory.modifierId!;
     int child = modifierItem.modifierItemId!;

     /*  ============================CODE FOR SELECTION TIME LIST UPDATE=============================== */
     ItemModifiers im = ItemModifiers.deepCopy(tempImList.elementAt(tempImList.indexWhere((element) => element.modifierId == group)));
     ModifierItems mi = ModifierItems.deepCopy(im.modifierItems!.elementAt(im.modifierItems!.indexWhere((element) => element.modifierItemId == child)));
     includedCount = im.included ??= 0;
     if (value == -1) {
       if (mi.qty > mi.min && mi.qty > 1) {
         mi.qty -= 1;
         if (mi.included! > 0) {
           mi.included = mi.included! - 1;
         }
       } else {
         return;
       }
     } else {
       if (mi.max == 0 ? getAllModifierItemQuantity(im.modifierItems!) < im.max! : getAllModifierItemQuantity(im.modifierItems!) < im.max!  && mi.qty < mi.max ) {
         mi.qty += 1;
         mi.included = updateIncludedCount(im, mi);
       } else {
         return;
       }
     }

     ///to replace new mi with old in mi list of im
     int indexOfMi = im.modifierItems!.indexWhere((element) => element.modifierItemId == mi.modifierItemId);
     im.modifierItems!.removeAt(indexOfMi);
     im.modifierItems!.insert(indexOfMi, mi);

     /* =========THIS CODE DISTRIBUTE INCLUDING COUNT IF ANY PLUS MINUS FOUND========== */
     int alreadyIncluded = 0;
     for (var i = 0; i < im.modifierItems!.length; ++i) {
       alreadyIncluded += im.modifierItems![i].included as int;
     }
     int remainingIncluded = im.included! - alreadyIncluded;
     for (var j = 0; j < im.modifierItems!.length; ++j) {
       if (im.modifierItems![j].qty > (im.modifierItems![j].included ?? 0) && remainingIncluded > 0) {
         int difference = im.modifierItems![j].qty - im.modifierItems![j].included;
         if (difference <= remainingIncluded) {
           im.modifierItems![j].included = (im.modifierItems![j].included ?? 0) + difference;
           remainingIncluded -= difference;
         } else if (difference > remainingIncluded) {
           im.modifierItems![j].included = (im.modifierItems![j].included ?? 0) + remainingIncluded;
           remainingIncluded = 0;
           break;
         }
       }
     }
     /*   =========END OF CODE TO DISTRIBUTE INCLUDING COUNT IF ANY PLUS MINUS FOUND========== */

     ///to replace new im with old in list of im
     int index = tempImList.indexWhere((element) => element.modifierId == im.modifierId);
     tempImList.removeAt(index);
     tempImList.insert(index, im);

     debugPrint('tempImList  ${tempImList.length}');
     _itemData?.itemModifiers = tempImList.isEmpty ? _itemModifiers : tempImList;
     _actualPrice = calculateActualPriceOfItem(_itemData!);
     notifyListeners();
     /*  ==========================END CODE FOR SELECTION TIME LIST UPDATE============================= */
   }

   /*
  *
  * check for max limit of itemModifier and modifierItem
  * if within limit then return true, if reached max limit or exceed then return false
  *
  * */
   bool isQuantityLimitExceeded(int imId, int miId) {
     int itemModifierIndex =
     tempImList.indexWhere((element) => element.modifierId == imId);
     if (itemModifierIndex >= 0) {
       ItemModifiers itemModifier =
       ItemModifiers.deepCopy(tempImList.elementAt(itemModifierIndex));

       if (getAllModifierItemQuantity(itemModifier.modifierItems!) <
           itemModifier.max!) {
         int modifierItemIndex = itemModifier.modifierItems!
             .indexWhere((element) => element.modifierItemId == miId);
         if (modifierItemIndex >= 0) {
           ModifierItems modifierItem = ModifierItems.deepCopy(itemModifier
               .modifierItems!
               .elementAt(modifierItemIndex));
           return modifierItem.max == 0
               ? getAllModifierItemQuantity(
               itemModifier.modifierItems!) <
               itemModifier.max!
               : (modifierItem.qty ??= 0) < modifierItem.max!;
         } else {
           return false;
         }
       } else {
         return false;
       }
     } else {
       return false;
     }
   }

   /// Calculate item price
   num calculateActualPriceOfItem(Items itemsObject) {
     ///Actual base price item = (item base price + modifier item price if exceed include)*qty  || formula suggested by rakesh in skype
     num actualPrice = 0.0;
     num basePrice = itemsObject.itemPriceInventory?.price ??0;
     debugPrint('base: ${itemsObject.itemPriceInventory?.price ??0}');


     num miTotalActualPrice = 0.0;
     for (var i = 0; i < itemsObject.itemModifiers!.length; ++i) {
       num imBasePrice = itemsObject.itemModifiers![i].basePrice ??0;
       num miActualPrice = 0.0;
       for (var j = 0; j < itemsObject.itemModifiers![i].modifierItems!.length; ++j) {
         var miBasePrice = itemsObject.itemModifiers![i].modifierItems![j].price ?? 0;
         int miQty = itemsObject.itemModifiers![i].modifierItems![j].qty ?? 0;
         int miIncluded = itemsObject.itemModifiers![i].modifierItems![j].included ?? 0;

         debugPrint(':: miQty $miQty miIncluded $miIncluded  miQty $miQty: :');
         if (miQty > 0 && (miIncluded <= miQty)) {
           int itemNotIncludedCount = miQty - miIncluded;
           if (itemNotIncludedCount != 0) miActualPrice = miActualPrice + imBasePrice+ (miBasePrice ??= 0.0) * itemNotIncludedCount;
         }
       }
       debugPrint(':: miTotalActualPrice $miTotalActualPrice imBasePrice $imBasePrice  $miActualPrice miActualPrice::');
       miTotalActualPrice = miTotalActualPrice /*+ imBasePrice*/ + miActualPrice;
     }
     actualPrice = roundDouble(basePrice + miTotalActualPrice);
     return actualPrice;
   }

   /*
  * This method will set all 'qty' same as 'min' count, this is cause by default 'qty' will be null and qty will always
  * remain at-least same as 'min' value
  * */
   Future<void> makeQuantitySameWithMinCount() async {
     await manageIncludedCountForInitialData();
   }

   /*
  * Included count is distributed to modifier items based on their 'min' value while add item.
  * Cause while add item included count is null/zero for all,
  * and if any item has 'min' > 0 then included count is assigned to them depending on included count availability
  * */
   manageIncludedCountForInitialData() {

     for (var i = 0; i < _itemModifiers.length; ++i) {
       if (_itemModifiers[i].modifierItems!.isNotEmpty) {
         int alreadyIncluded = 0;
         for (var k = 0; k < _itemModifiers[i].modifierItems!.length; ++k) {
           //if status is true then only consider it
           if (_itemModifiers[i].modifierItems![k].status!) {
             alreadyIncluded += _itemModifiers[i].modifierItems![k].included ?? 0;
           }
         }
         int remainingIncluded = (_itemModifiers[i].included ?? 0) - alreadyIncluded;
         for (var j = 0; j < _itemModifiers[i].modifierItems!.length; ++j) {
           //if status is true then only consider it
           if (_itemModifiers[i].modifierItems![j].status!) {
             _itemModifiers[i].modifierItems![j].qty = _itemModifiers[i].modifierItems![j].min ?? 0;

             if (_itemModifiers[i].modifierItems![j].qty == 0) {
               _itemModifiers[i].modifierItems![j].included = 0;
             } else {
               if (remainingIncluded == 0) {
                 _itemModifiers[i].modifierItems![j].included = 0;
               } else {
                 if (remainingIncluded <= _itemModifiers[i].modifierItems![j].qty) {
                   _itemModifiers[i].modifierItems![j].included = remainingIncluded;
                   remainingIncluded = 0;
                 } else if (remainingIncluded > _itemModifiers[i].modifierItems![j].qty) {
                   _itemModifiers[i].modifierItems![j].included = _itemModifiers[i].modifierItems![j].qty;
                   remainingIncluded = remainingIncluded - _itemModifiers[i].modifierItems![j].qty as int;
                 }
               }
             }
             debugPrint(
                 'FINAL Assigned included to ${_itemModifiers[i].modifierItems![j].name} is: ${_itemModifiers[i].modifierItems![j].included}');
           }
         }
       }
     }
     getAllPreSelectedData();
   }

   /*
  * This method is used to collect selected data in [tempImList]
  * */

   void getAllPreSelectedData() {
     debugPrint(':: preselection check in::');
     try {
       //itemWithDefaultData = Items.deepCopy(_itemsObjectCopied!);
       debugPrint(':: preselection check in default size ${itemWithDefaultData?.itemModifiers?.length}::');
       for (var i = 0; i < (itemWithDefaultData?.itemModifiers?.length ?? 0); ++i) {
         //if status is true then only consider it -> [!element.status]
         itemWithDefaultData?.itemModifiers![i].modifierItems!.removeWhere((element) => ((element.min ?? 0) == 0 || !element.status!));
         itemWithDefaultData?.itemModifiers![i].modifierItems!.forEach((element) {
           element.qty = element.min ?? 0;
         });
       }
       debugPrint(' :: cart item modifiers ${itemWithDefaultData?.itemModifiers?.length}::');
       for (var i = 0; i < (itemWithDefaultData?.itemModifiers?.length ?? 0); ++i) {
         debugPrint(':: modifier items ${itemWithDefaultData?.itemModifiers?[i].modifierItems?.length }::');
         itemWithDefaultData?.itemModifiers?.removeWhere((element) => (element.modifierItems?.length ?? 0) == 0);
       }
       //debugPrint('itemWithDefaultData $itemWithDefaultData');
     } catch (exception) {
       debugPrint('Exception while remove data $exception');
     }

     debugPrint(' :: cart item modifiers ${itemWithDefaultData?.itemModifiers?.length}::');

     for (var i = 0; i < (itemWithDefaultData!.itemModifiers!.length); ++i) {
       ItemModifiers itemModifiers = itemWithDefaultData!.itemModifiers![i];

       debugPrint('default modifier list ${itemModifiers.name}' );
       _itemsObject?.itemModifiers ??= [];
       _itemsObject?.itemModifiers?.add( ItemModifiers.deepCopy(itemModifiers));
     }
     tempImList = [];
     tempImList = _itemsObject?.itemModifiers ??[];

     tempImList.forEach((e) {
       debugPrint(':: ${e.name}  tmpI::');
     });
     debugPrint(':: preselection check out::');
     notifyListeners();

   }

   setItemCourseDropDownData(){
     _itemCourseList = [
       ItemCourse(id: 0, name: ItemCourseString.now),
       ItemCourse(id: 1, name: ItemCourseString.first),
       ItemCourse(id: 2, name: ItemCourseString.second),
       ItemCourse(id: 3, name: ItemCourseString.third),
       ItemCourse(id: 4, name: ItemCourseString.takeout),
     ];
     notifyListeners();
   }

   setCourseId(int courseId){
     _selectedCourseId = courseId;
     notifyListeners();
   }

    bool checkItemValidation() {

     debugPrint(':: validationlist ${validationDataList.length}::');
     for (var i = 0; i < validationDataList.length; ++i) {
       int itemModifierId = validationDataList.entries.elementAt(i).key;
       int itemMinLimit = validationDataList.entries.elementAt(i).value;
       if (tempImList.indexWhere((element) => element.modifierId! == itemModifierId) >= 0) {
         int totalModifierQuantity = getAllModifierItemQuantity(tempImList.elementAt(tempImList.indexWhere((element) =>
         element.modifierId! == itemModifierId)).modifierItems!);
         if (totalModifierQuantity < itemMinLimit) {
           return  false;
         }
         //debugPrint('Length of $itemModifierName is $valueLength');
       } else {
         return false;
       }
     }
     return true;
   }

   decreaseQty(){
     if (_itemCount > 1) {
       _itemCount-- ;
     }
     notifyListeners();
   }

   increaseQty(){

     // if(_itemData!.itemPriceInventory != null) {
     //   if(_itemCount <_itemData!.itemPriceInventory!.currentInventory! ) {
     //     _itemCount++ ;
     //   }
     // }
     if(_itemData!.itemPriceInventory != null) {
       if(_itemData!.itemPriceInventory!.enableInventoryCountdown!) {
         if (_itemCount <
             _itemData!.itemPriceInventory!.currentInventory!) {
           _itemCount++;
         }
       }else {
         _itemCount++;
       }
     }
     notifyListeners();
   }

   ///create cart item
   Future<CreateCartItemResponse> createCartItem(int cartId) async {
     CreateCartItemResponse? createCartItemResponse;
     try {
       createCartItemResponse = await _cartService.createCartItem(
           body:jsonEncode(buildCartItemObject(cartId)));

     } catch (error, stacktrace) {
       debugPrint('$stacktrace');
       throw error;
     }

     return createCartItemResponse;
   }

   /// build  cart item object to send
   CreateCartItemObject buildCartItemObject(int cartId){
     CreateCartItemObject createCartItemObject = CreateCartItemObject();


     createCartItemObject.cartId = cartId;
     createCartItemObject.itemQty = _itemCount;
     createCartItemObject.itemSku = _itemData?.itemSku!;
     createCartItemObject.name = _itemData?.name!;
     createCartItemObject.imageUrl = _itemData?.imageUrl!;
     createCartItemObject.category = _categoryName;
     createCartItemObject.categoryId = _itemData?.categoryId!;
     createCartItemObject.itemId = _itemData?.itemId!;
     createCartItemObject.basePrice = _itemData?.itemPriceInventory?.price ??0;
     createCartItemObject.cost = _itemData?.itemPriceInventory?.cost ??0;
     createCartItemObject.actualPrice = _actualPrice;
     createCartItemObject.note = _notes;
     createCartItemObject.sentToKitchen= false; // default
     createCartItemObject.course= _selectedCourseId;
     createCartItemObject.userDefinedCustomItem= false;
     createCartItemObject.userId=  '';
     createCartItemObject.userName= '';
     createCartItemObject.addedById = UserModel.fromJson(PrefsUtils.getObject(PrefKeys.user)).id;
     createCartItemObject.addedBy  = ApiConstant.ADDED_BY_VALUE;


     List<ItemModifiers>? cartItemModifiersList =[];

     tempImList.forEach((mo) {

       List<ModifierItems>?  modifierItems = [];

       mo.modifierItems?.forEach((e) {

         ModifierItems modifierItem =ModifierItems(
           modifierItemId: e.modifierItemId,
           price: e.price,
           included: e.included,
           max: e.max,
           min : e.min,
           name: e.name,
           order: e.order,
           sku:e.sku,
           qty: e.qty,
         );
         modifierItems.add(modifierItem);
       });


       ItemModifiers cartItemModifier = ItemModifiers(
           modifierId: mo.modifierId,
           basePrice: mo.basePrice,
           included: mo.included,
           max: mo.max,
           min : mo.min,
           name: mo.name,
           order:  mo.order,
           sku: mo.sku,
           modifierItems: modifierItems
       );

       cartItemModifiersList.add(cartItemModifier);

     });

     createCartItemObject.cartItemModifiers = cartItemModifiersList;

     if(_selectedUserData != null) {
       if(_selectedUserData!.id != 0) {
         createCartItemObject.cartUserId = _selectedUserData?.id;
       }
       if(_selectedUserData!.userId!.isNotEmpty) {
         CartUser cartUser;
         if(_selectedUserData?.userType!.toLowerCase() =='guest'){
           cartUser = CartUser(
             userId: _selectedUserData?.userId,
             name: _selectedUserData?.name,
           );
         }else {
           cartUser = CartUser(
               userId: _selectedUserData?.userId,
               name: _selectedUserData?.name,
               email: _selectedUserData?.email,
               phone: _selectedUserData?.phone
           );
         }
         createCartItemObject.cartUser = cartUser;
       }


     }


     debugPrint(':: item to send ${jsonEncode(createCartItemObject)}::');

     return createCartItemObject;
   }

   setNotes(String notes) {
     _notes = notes;
   }

   setItemCourse(int courseId){
     _selectedCourseId = courseId;
     notifyListeners();
   }

   setCartUserData(CartUserData? userData){
     _selectedUserData = userData;
   }

  reset(){
    _itemData = null;
    _itemWithDefaultData = null;
    _itemModifiers =[];
    _modifierItems =[];
     includedCount = 0;
     _actualPrice = 0;
    _itemsObject = null;
    _itemCount = 1;
    _selectedCourseId =0;
    _itemTags = [];
    validationDataList.clear();
    _notes ='';
    _selectedUserData = null;
    _categoryName='';
    notifyListeners();
  }
}