import 'dart:async';

import 'package:flutter/material.dart';

import '../../models/menu/menu_data.dart';
import '../../models/menu/tag_response.dart';
import '../../services/graph_ql/graph_ql_service.dart';

class MenuViewProvider with ChangeNotifier{

  List<MenuCategories> _categories = [];
  List<MenuCategories> get  categories => _categories;

  List<Items> _items = [];
  List<Items> get items => _items;

  List<TagData> _tags = [];
  List<TagData> get tags => _tags;

  List<int> _selectedTagIds =[];
  List<int> get  selectedTagIds => _selectedTagIds;

  StreamController<List<MenuCategories>> _menuStreamController = StreamController<List<MenuCategories>>.broadcast();
  Stream<List<MenuCategories>> get menuStream =>
      _menuStreamController.stream.asBroadcastStream();

  Future<void> fetchMenus() async {
    List<MenuData> menuList;
    try {
      menuList =  await GraphQLService().fetchMenus();
      if (menuList.isNotEmpty) {
        _categories.clear();
        _categories.addAll(menuList.first.menuCategories ?? []);

        setCategoriesData();
      }else {
        _menuStreamController.add([]);
      }
    } catch (error) {
      throw error;
    }
    notifyListeners();
  }

  /// set category data
  void setCategoriesData() {
    _menuStreamController.add(_categories);
    debugPrint(':: categories ${_categories.length} ::');
    if (_categories.isNotEmpty) {
      setItems(_categories.first.items!);
    }
  }

  setItems(List<Items> items) {
    _items = items;
    notifyListeners();
  }

  Future<void> fetchTags() async {
    try {
      _tags.clear();
      _tags =  await GraphQLService().fetchTags();
    } catch (error) {
      throw error;
    }
    notifyListeners();
  }

  /// search items
  Future<void> searchItems() async {
    try {
       _items.clear();
       _items =  await GraphQLService().searchItems(tagIds:_selectedTagIds,);

      Map<int, MenuCategories> categoryIdMap = {};
      if(_items.isNotEmpty){
        _items.forEach((element) {
          categoryIdMap.update(element.categoryId!, (value) => element.category!, ifAbsent: () => element.category!,);
        });

        _categories.clear();
        categoryIdMap.forEach((key, value) {
          value.items =  _items.where((element) => element.categoryId == key).toList();
          _categories.add(value);
        });
        setCategoriesData();
      }
    } catch (error) {
      throw error;
    }
    notifyListeners();
  }

  /// mark tag selected
  markTagSelected(TagData tagData){
     tagData.selected = !tagData.selected!;
    _selectedTagIds.clear();
     _selectedTagIds = _tags.where((tag) => tag.selected! == true).
    map((e) => e.id!).toList();
    debugPrint(':: Tag ids :: $_selectedTagIds');
    // notifyListeners();
  }

  String getCategoryName(int categoryId){
    return categories.firstWhere((c) => c.id == categoryId).categoryName??'';
  }

  checkItemInStock(Items item) {
    if(item.itemPriceInventory != null) {
      if(item.itemPriceInventory!.enableInventoryCountdown! &&
          item.itemPriceInventory!.currentInventory == 0) {
        return false;
      }
      return true;
    }
    return false;
  }

  void reset(){
    _categories = [];
    _items = [];
    _tags = [];
    _selectedTagIds =[];
    notifyListeners();
  }

  @override
  void dispose() {
    _menuStreamController.close();
    super.dispose();
  }
}