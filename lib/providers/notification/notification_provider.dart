import 'dart:convert';
import 'dart:ffi';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/services/notification/notification_service.dart';
import '../../models/notification/device_register.dart';
import '../../utils/pref_utils.dart';

class NotificationProvider with ChangeNotifier {
  FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  NotificationService _notificationService = NotificationService();

  int _totalNotifications = 0;
  get totalNotifications => _totalNotifications;

  bool _showApproveActionButton = true;
  bool get showApproveActionButton => _showApproveActionButton;

  void setShowApproveActionButton(bool value) {
    _showApproveActionButton = value;
    notifyListeners();
  }

  setNotificationsCount(int count){
    _totalNotifications = count;
    notifyListeners();
  }

  resetNotifications(){
    _totalNotifications = 0;
    notifyListeners();
  }

  getTotalNotifications(){
    String label = "";
    if(_totalNotifications > 99){
      label = "99+";
    }else{
      label = _totalNotifications.toString();
    }
    return label;
  }

  Future<void> initFirebase() async {
    String? fcmToken = PrefsUtils.getString(PrefKeys.fcmToken);
    if (fcmToken == null || fcmToken.isEmpty) {
      // Get the token for the device
      await _getToken();
      await registerDevice();
    }
  }


  // Request permission on iOS
  Future<void> requestPermissions() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('User granted permission: ${settings.authorizationStatus}');
  }


  // Get the token for the device and store it
  Future<void> _getToken() async {
    if(Platform.isIOS) {
      debugPrint(':: platform ios::');
      await FirebaseMessaging.instance.getAPNSToken().then((value) {
         debugPrint(':: get apns token $value::');
      });
    }
    String? fcmToken = await _firebaseMessaging.getToken();
    debugPrint("FCM Token generated: $fcmToken");
    setFcmToken(fcmToken!);
    // Notify listeners (if token is required elsewhere)
    notifyListeners();
  }

  void setFcmToken(String fcmToken) async {
    await PrefsUtils.setString(PrefKeys.fcmToken, fcmToken);
  }


  /// register device for notification
   registerDevice() async {
    try {
        String? fcmToken = PrefsUtils.getString(PrefKeys.fcmToken);
        await _notificationService.registerDevice(fcmToken: fcmToken!);
        debugPrint('Register device to notification server.');
    } catch (error) {
     // throw error;
    }
  }

  /// remove device for notification service
  Future<void> removeDevice() async {
    try {
       // remove device token from notification server
       String? token = PrefsUtils.getString(PrefKeys.fcmToken);
       if(token != null) {
         await _notificationService.removeDevice(fcmToken: token);
         PrefsUtils.setString(PrefKeys.fcmToken, '');
         debugPrint('Remove device from notification service.');
       }else {
         debugPrint('Remove device fcm token null.');
       }

    } catch (error) {
      debugPrint('Remove device from notification error.');
    }
  }

  /// get unread notifications count
  Future<void> getUnReadCount() async {
    try {
      NotificationData deviceRegister = await _notificationService.unReadCount();
      debugPrint('unread count : ${deviceRegister.unreadCount}');
      setNotificationsCount(deviceRegister.unreadCount!);
    } catch (error) {
      throw error;
    }
  }

  reset(){
    // _totalNotifications = 0;
    _showApproveActionButton = false;
    _firebaseMessaging.deleteToken();
  }

}
