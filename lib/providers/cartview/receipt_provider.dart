import 'package:flutter/material.dart';
import 'package:gr8tables_server_manager/services/cart/cart_service.dart';

import '../../models/orders/order_receipt/order_receipt_response.dart';

class ReceiptProvider with ChangeNotifier {
  final CartService _cartService = CartService();

    String? _email;
    String? get email => _email;

    String? _phone;
    String? get phone => _phone;

    bool? _emailOpted =false;
    bool? get emailOpted => _emailOpted;

    bool? _phoneOpted =false;
    bool? get phoneOpted => _phoneOpted;

    String? _receiptSuccessMsg;
    String? get receiptSuccessMsg => _receiptSuccessMsg;



    /// set success message
    setSuccessMsg(String msg) {
     this._receiptSuccessMsg = msg;
       notifyListeners();
    }


  ///send email receipt
  Future<OrderReceiptResponse> sendEmailReceipt({ required String email, String cartId = ""}) async {
    OrderReceiptResponse _orderReceiptResponse;
    try {
      _orderReceiptResponse = await _cartService.sendEmailReceipt(email : email,cartId: cartId);
    } catch (error) {
      throw error;
    }
    return _orderReceiptResponse;
  }

  /// reset data
   reset(){
      this._email ="";
      this._phone ="";
      this._emailOpted =false;
      this._phoneOpted =false;
      this._receiptSuccessMsg = "";
      notifyListeners();
  }
}