import 'dart:async';

import 'package:flutter/material.dart';

import '../../constants/api_constant.dart';
import '../../constants/app_string.dart';
import '../../models/cart/cart_data.dart';
import '../../models/cart/cart_items/cart_items.dart';
import '../../models/cart/cart_items/delete_cart_item_response.dart';
import '../../models/cart/cart_promotions/cart_custom_discount.dart';
import '../../models/cart/cart_promotions/cart_discount.dart';
import '../../models/cart/cart_promotions/cart_reward.dart';
import '../../models/cart/cart_promotions/remove_promotions.dart';
import '../../models/cart/cart_user.dart';
import '../../models/cart/cart_users/add_cart_user_response.dart';
import '../../models/cart/cart_users/cart_user_data.dart';
import '../../models/cart/create_cart_response.dart';
import '../../models/cart/get_cart_data_response.dart';
import '../../models/customer/search_customer.dart';
import '../../models/loyalty/remove_loyalty_response.dart';
import '../../services/cart/cart_service.dart';
import 'package:collection/collection.dart';

import '../../utils/app_utils.dart';


class CartViewProvider with ChangeNotifier{

   final CartService _cartService = CartService();

   CartData? _cartData;
   CartData? get cartData => _cartData;

   List<CartUserData> _cartUserList =[];
   List<CartUserData> get cartUserList => _cartUserList;

   int _selectedCartUser= 0;
   int get selectedCartUser=> _selectedCartUser;

   String _guestUserName='';
   String get guestUserName=> _guestUserName;

   Map<CartUserData, List<CartItems>> _pendingCartItemMap ={};
   Map<CartUserData, List<CartItems>> get pendingCartItemMap =>_pendingCartItemMap;

   Map<CartUserData, List<CartItems>> _submittedCartItemMap ={};
   Map<CartUserData, List<CartItems>> get submittedCartItemMap =>_submittedCartItemMap;

   /// Get total number of submitted items
   int getSubmittedItemsCount() {
     int totalCount = 0;
     _submittedCartItemMap.values.forEach((items) {
       totalCount += items.fold(0, (sum, item) => sum + (item.itemQty ?? 1));
     });
     return totalCount;
   }

   /// Get total number of pending items
   int getPendingItemsCount() {
     int totalCount = 0;
     _pendingCartItemMap.values.forEach((items) {
       // Count each item individually by quantity instead of just counting the list length
       totalCount += items.fold(0, (sum, item) => sum + (item.itemQty ?? 1));
     });
     return totalCount;
   }

   bool _actionVoidItem = false;
   bool get actionVoidItem => _actionVoidItem;

   Map<int,bool> _itemsAvailableForVoid ={};
   Map<int,bool> get itemsAvailableForVoid => _itemsAvailableForVoid;

   num _subtotal = 0;
   num get subtotal => _subtotal;


   CartDiscount? _cartDiscount;
   CartDiscount? get cartDiscount => _cartDiscount;

   List<CartCustomDiscount>? _cartCustomDiscount = [];
   List<CartCustomDiscount>? get cartCustomDiscount => _cartCustomDiscount;

   CartReward? _cartReward;
   CartReward? get cartReward => _cartReward;

   num _subtotalAfterPromotions = 0;
   num get subtotalAfterPromotions => _subtotalAfterPromotions;

   num _subTotal = 0;
   num get subTotal =>_subTotal;

   num _serviceChargeValue = 0;
   num get serviceChargeValue => _serviceChargeValue;

   String _serviceChargeType='\%'; //'\$';
   String get serviceChargeType =>_serviceChargeType;

   String _serviceChargeError='';
   String get serviceChargeError => _serviceChargeError;

   /// for service charge type switch
   Alignment _knobAlignment = Alignment.centerLeft;
   Alignment get knobAlignment => _knobAlignment;

   String _tipType='\%';
   String get tipType =>_tipType;

   num _tipValue = 0;
   num get tipValue => _tipValue;

   String _tipError='';
   String get tipError => _tipError;

   /// for tip  switch
   Alignment _knobAlignmentTip = Alignment.centerLeft;
   Alignment get knobAlignmentTip => _knobAlignmentTip;

   Customer? _customer;
   Customer? get customer => _customer;

   String _customerName = '';
   String get customerName => _customerName;

   bool _isCustomerSelected = false;
   bool get isCustomerSelected => _isCustomerSelected;

   StreamController<String> _customerStreamController = StreamController<String>.broadcast();
   Stream<String> get customerStream => _customerStreamController.stream.asBroadcastStream();


   /// get cart
   Future<void> getCart({required int id}) async {
     GetCartDataResponse cartDataResponse;
     try {
       cartDataResponse = await  _cartService.getCart(
         id: id.toString(),
       );
      // debugPrint("Cart: test data: " + cartDataResponse.payload!.toString());
       setCartData(cartDataResponse.payload!);
     } catch (error, stacktrace) {
       debugPrint("stacktrace caught: $stacktrace");
       _generateCartUsersMap();
       debugPrint('$stacktrace');
       throw error;
     }
   }

   setCartData(CartData? data) {
   _cartData = data;
   _cartCustomDiscount = data?.cartCustomDiscount;
   _cartDiscount = data?.cartDiscount;
    _actionVoidItem = false;
    _generateCartUsersMap();
    checkForPendingCartItems();
    checkForSubmittedCartItems();
   _subTotal = cartData?.subtotal ??0;
    _calculatePromotions();
    notifyListeners();
   }

   resetCartData(){
     _cartData = null;
   }

   getLabelForBottomMenuLeftOption() {
     bool isSentToKitchenAll = false;
     String label = "Option";
     if(_cartData != null){
       if(_cartData?.status!.toLowerCase() != OrderStatus.pending){
         return label;
       }
       if(_cartData!.cartItems!.isEmpty){
         return label;
       }

       isSentToKitchenAll =_cartData!.cartItems!.every((items) => (items.sentToKitchen! == true));

       if(!isSentToKitchenAll) {
         label = BottomMenuString.send_to_kitchen;
       }
     }
     return label;
   }

   isAddItemsMenuButtonEnable() {
     bool isAddItem = false;
     if(_cartData != null) {
       String cartStatus  = _cartData?.status!.toLowerCase() ??'';
       if(_cartData!.platform!.toLowerCase() == ConstantString.platformWeb){
         if(cartStatus == OrderStatus.pending){
           isAddItem = true;
         }
       }else{
         if(cartStatus == OrderStatus.pending){
           isAddItem = true;
         }
       }
     }
     return isAddItem;
   }

   isSendToKitchenMenuButtonEnable() {
     bool isSentToKitchenAll = false;

     if(_cartData != null){
       isSentToKitchenAll =_cartData!.cartItems!.every((items) => (items.sentToKitchen! == true));
     }
     return !isSentToKitchenAll;
   }

   getLabelForBottomMenuRightOption() {
      if(_actionVoidItem) {
        return BottomSheetString.voidItem;
      }
      if(_cartData != null) {
          String cartStatus  = _cartData?.status!.toLowerCase() ??'';
          if(_cartData!.platform!.toLowerCase() == ConstantString.platformWeb){
            if(cartStatus == OrderStatus.placed ||
                cartStatus == OrderStatus.ready ||
                cartStatus == OrderStatus.outForDelivery) {
                 return BottomMenuString.reprint;
              }
           /*if(cartStatus == OrderStatus.pending) {
             return BottomMenuString.cancel;
           }*/
          }else {
            if(cartStatus == OrderStatus.pending){
              return BottomMenuString.addItem;
            }
          }
          // if(cartStatus == OrderStatus.placed ||
          //   cartStatus == OrderStatus.ready ||
          //   cartStatus == OrderStatus.outForDelivery
          // ){
          //     return BottomMenuString.bill;
          //
          // }
          // if(_cartData?.paymentReceived! == true) {
          //   return BottomMenuString.bill;
          // }
          // if(_cartData?.paymentReceived! == false){
          //   return BottomMenuString.addItem;
          // }
      }
      return BottomMenuString.addItem;
   }

   bool showVoidItemsButton() {
     if (cartData == null) return false;

     String cartStatus = cartData!.status!.toLowerCase();
     bool isPendingOrPlaced = cartStatus == OrderStatus.pending || cartStatus == OrderStatus.placed;
     bool isPlatformWeb = cartData!.platform!.toLowerCase() == ConstantString.platformWeb;
     bool paymentNotReceived = cartData!.paymentReceived == false;

     if (isPendingOrPlaced) {
       // For web, void button should appear if order is pending or payment is not received
       if (isPlatformWeb) {
         return cartStatus == OrderStatus.pending || paymentNotReceived;
       }
       // For other platforms, show button for pending/placed status
       return paymentNotReceived;
     }

     return false;
   }


   _generateCartUsersMap(){
     _cartUserList = [];
      if(_cartData != null) {
          if(_cartData?.id != 0){
            _cartUserList = List.of(_cartData?.cartUsers ??[]);
          }
      }
   }

   selectCartUser(int usrId){
     debugPrint(':: $usrId::');
     _selectedCartUser = usrId;
     notifyListeners();
   }

   setSelectedCustomer(Customer? customer, {String name = ''}) {
     _customer = customer;
     _customerName = name;
     _isCustomerSelected = true;
     _customerStreamController.sink.add(_customerName);
     notifyListeners();
   }

   /// get formatted customer name
   String getCustomerName(CartUser? user, {bool isRemoveNextLine = true}) {
     if (user == null) return isRemoveNextLine ? 'Guest' : 'Guest\n';

     return user.firstName!.isNotEmpty
         ? '${user.firstName!} ${user.lastName!}'.length > 23
         ? '${user.firstName!} ${user.lastName!}'
         : isRemoveNextLine
         ? '${user.firstName!} ${user.lastName!}'
         : '${user.firstName!} ${user.lastName!}\n'
         : user.email!.isNotEmpty
         ? '${user.email!}'.length > 23
         ? '${user.email!}'
         : isRemoveNextLine
         ? '${user.email!}'
         : '${user.email!}\n'
         : isRemoveNextLine
         ? '${getFormattedPhoneNumber(user.phone!)}'
         : '${getFormattedPhoneNumber(user.phone!)}\n';
   }

   /// add user to cart
   Future<void> addUserToCart() async {
     try {
       AddCartUserResponse addCartUserResponse;
       addCartUserResponse = await _cartService.addUserToCart(_cartData!.id!,
           _guestUserName);
       _guestUserName = '';

       if(addCartUserResponse.payload != null) {
         _selectedCartUser = addCartUserResponse.payload!.id!;
       }
      getCart(id: _cartData!.id!);
     } catch (error) {
       throw error;
     }
   }

   resetCartUserSelection(){
    _selectedCartUser =0;
    _guestUserName ='';
    // _cartUserList = [];
    notifyListeners();
   }

   bool checkAnyCartUserSelected(){
     return _selectedCartUser != 0;
   }

   setGuestUser(String name){
     _guestUserName = name;
   }

   checkForPendingCartItems(){
     _pendingCartItemMap ={};
     if(_cartData != null) {
       if(_cartData?.id != 0){

         /// check first if there are cart users or not
         if(_cartData!.cartUsers!.isNotEmpty) {
           _cartData!.cartUsers!.forEach((cu) {
             _pendingCartItemMap.update(cu, (value) => [], ifAbsent: ()=> []);
           });
         }

         /// find for send to kitchen false items
         List<CartItems> _unSendItems =
          List.from(_cartData!.cartItems!.where((ci) => ci.sentToKitchen == false));

         /// check if any unassigned item ( who has no cart user )
         List<CartItems> _unUnassignedItems = List.from(_unSendItems.
           where((ci) => ci.cartUserId! == 0));

         if(_unUnassignedItems.isNotEmpty) {
           CartUserData? userUnassigned = CartUserData(
             userId: '',
             id: 0,
             name: 'Unassigned',
           );
           _pendingCartItemMap.update(userUnassigned, (value) => [], ifAbsent: ()=> []);
         }

         /// divide  items user-wise
         _unSendItems.forEach((usi) {
           var map = _pendingCartItemMap.entries.firstWhereOrNull((e) => e.key.id! ==
               usi.cartUserId!);
           if(map != null) {
             map.value..add(usi);
           }
         });

         /// add items to un-assigned
         if(_unUnassignedItems.isNotEmpty) {
           var map = _pendingCartItemMap.entries.firstWhereOrNull((e) => e.key.userId!.isEmpty);
           if(map != null) {
             map.value.clear();
             map.value..addAll(_unUnassignedItems);
           }
         }

       }
     }
   }

   checkForSubmittedCartItems(){
     _submittedCartItemMap ={};
     _itemsAvailableForVoid = {};
     if(_cartData != null) {
       if(_cartData?.id != 0){

         /// check first if there are cart users or not
         if(_cartData!.cartUsers!.isNotEmpty) {
           _cartData!.cartUsers!.forEach((cu) {
             _submittedCartItemMap.update(cu, (value) => [], ifAbsent: ()=> []);
           });
         }

         /// find for send to kitchen true items
         List<CartItems> _sentItems =
         List.from(_cartData!.cartItems!.where((ci) => ci.sentToKitchen == true));

         /// check if any unassigned item ( who has no cart user )
         List<CartItems> _unUnassignedItems = List.from(_sentItems.
         where((ci) => ci.cartUserId ==0));

         debugPrint(':: un assigned items submitted ${_unUnassignedItems.length} ::');

         if(_unUnassignedItems.isNotEmpty) {
           CartUserData? userUnassigned = CartUserData(
             userId: '',
             id: 0,
             name: 'Unassigned',
           );
           _submittedCartItemMap.update(userUnassigned, (value) => [], ifAbsent: ()=> []);
         }

         /// divide  items user-wise
         _sentItems.forEach((si) {
           var map = _submittedCartItemMap.entries.firstWhereOrNull((e) => e.key.id! ==
               si.cartUserId!);
           if(map != null) {
             map.value..add(si);
           }
           _itemsAvailableForVoid.update(si.id!, (value) => false, ifAbsent: ()=> false);
         });

         /// add items to un-assigned
         if(_unUnassignedItems.isNotEmpty) {
           var map = _submittedCartItemMap.entries.firstWhereOrNull((e) => e.key.userId!.isEmpty);
           if(map != null) {
             map.value.clear();
             map.value..addAll(_unUnassignedItems);
           }
           _unUnassignedItems.forEach((ui) {
             _itemsAvailableForVoid.update(ui.id!, (value) => false, ifAbsent: ()=> false);
           });
         }


       }
     }
   }


   CartUserData? getSelectedCartUser(){
     return _cartUserList.firstWhereOrNull((e) => e.id == _selectedCartUser );
   }

   ///Create cart
   Future<CreateCartResponse> createCart({String orderReceiveMethod = '',
     int tableId =-1,
     /*Customer? customer, UserAddress? userAddress*/}) async{

     CreateCartResponse createCartResponse;
     try{
       /// user address variable
       createCartResponse = await _cartService.createCart(
         orderReceiveMethod : orderReceiveMethod,
         tableId: tableId,
       );
      } catch (error) {
       throw error;
     }

     return createCartResponse;
   }

   /// send to kitchen
   Future<void> itemSendToKitchen() async {
     try {
       await _cartService.itemSendToKitchen(cartItemIds: _cartData!.cartItems!.where((e)=>e.sentToKitchen == false ).map((e)=>e.id!).toList(),
           cartId: _cartData!.id!);
       await getCart(id: _cartData!.id!);

     } catch (error) {
       throw error;
     }
   }

   num subTotalCalculate() {
     num total = 0;
     submittedCartItemMap.entries.forEach((entry) {
       total += entry.value.fold(0.0, (sum, cartItem) {
         return sum + (cartItem.lineTotal ?? 0);
       });
     });
     return total;
   }

   ///delete cart item
   Future<DeleteCartItemResponse> deleteCartItem({required int cartItemId}) async {
     DeleteCartItemResponse deleteCartItemResponse;
     try {
       deleteCartItemResponse = await _cartService.deleteCartItem(cartItemId: cartItemId);
       if(_cartData != null) {
         getCart(id: _cartData!.id!);
       }
     } catch (error) {
       throw error;
     }
     return deleteCartItemResponse;
   }

   startStopVoidItem(){
     _actionVoidItem = !_actionVoidItem;

     if(_actionVoidItem == false){
       // if false then mark all as false
       _itemsAvailableForVoid.updateAll((key, value) => false);
     }
     notifyListeners();
   }

   /// select/de-select item for void
   selectDeselectItemForVoid(int id , bool newValue) {
     _itemsAvailableForVoid.update(id, (value) => newValue, ifAbsent: ()=>newValue,);
     notifyListeners();
   }


   checkItemSelectedForVoid(int id){
      if(_itemsAvailableForVoid.containsKey(id)){
        return _itemsAvailableForVoid[id];
      }
    return false;
   }

   allowToVoidItem() {
      return _itemsAvailableForVoid.containsValue(true);
   }

   /// void items
   Future<void> voidCartItems(String reason) async {
     try {

        List<int> cartItemIds = _itemsAvailableForVoid.entries.where((e) => e.value
            == true).map((e) => e.key).toList();

        debugPrint(':: items to be void  ${cartItemIds}:: reason $reason');

       await _cartService.voidCartItem(cartItemIds:cartItemIds,
         reason: reason,);

        if(_cartData != null) {
          getCart(id: _cartData!.id!);
        }
     } catch (error) {
       throw error;
     }
   }

   /// Update cart status
   Future<void> updateCartStatus({required String status}) async {
     try {
       await _cartService.updateCartStatus(cartId: _cartData!.id.toString(), status: status,);
       if(status == OrderStatus.processed){
         if(_cartData != null) {
           if(_cartData?.id !=0) {
             debugPrint(':: print receipt ::');
           }
         }
       }
     } catch (error) {
       throw error;
     }
   }

   /// calculate promotions if any
   void _calculatePromotions() {
     debugPrint('_calculatePromotions ');
     ///calculate with promotions if any
     _subtotalAfterPromotions = _subTotal;

     if(_cartData != null) {
       /// order promo code
       if (null != _cartData?.cartPromoCode) {
         _subtotalAfterPromotions =
             _subtotalAfterPromotions - _cartData!.cartPromoCode!.appliedValue!;

       }

       /// cart regular discount
       if (null != _cartData?.cartDiscount) {
         _subtotalAfterPromotions =
             _subtotalAfterPromotions - _cartData!.cartDiscount!.appliedValue!;

       }

       /// cart custom discount
       if (_cartData!.cartCustomDiscount!.isNotEmpty) {
         num _customDiscountTotal =
         _cartData!.cartCustomDiscount!.fold(0, (i, item) {
           return i + item.appliedValue!;
         });
         _subtotalAfterPromotions =
             _subtotalAfterPromotions - _customDiscountTotal;

       }

       /// cart loyalty
       if(null != _cartData!.cartLoyalty) {
         _subtotalAfterPromotions = _subtotalAfterPromotions - _cartData!.cartLoyalty!.rewardAmount!;
       }

       /// cart rewards
       if (null != _cartData!.cartReward) {
         _subtotalAfterPromotions =
             _subtotalAfterPromotions - _cartData!.cartReward!.rewardAmount!;
       }

     }else {
       _subtotalAfterPromotions = 0;
     }

     if(_subtotalAfterPromotions <= 0) {
       _subtotalAfterPromotions = 0;
     }

   }

   /// calculate new subtotal
   num newSubTotalCalculation(){
     return subtotalAfterPromotions - pendingSubTotal();
   }

   num pendingSubTotal() {
     num total = 0;
     pendingCartItemMap.entries.forEach((entry) {
       total += entry.value.fold(0.0, (sum, cartItem) {
         return sum + (cartItem.lineTotal ?? 0);
       });
     });
     return total;
   }

   /// set service charge value
   setServiceCharge(num value){
     if (value > 100) {
       _serviceChargeValue = 0;
       setServiceChargeError(AddServiceChargeViewString.serviceChargeNotGreaterThan100Percent);
     }else {
       _serviceChargeValue = value;
       setServiceChargeError("");
     }
     notifyListeners();
   }

   /// set service charge type
   void setServiceChargeType(String type, Alignment knobAlignment) {

     _serviceChargeType = type;
     _knobAlignment =knobAlignment;
     _serviceChargeValue = 0;
     notifyListeners();
   }

   /// set service charge error
   setServiceChargeError(String errorMsg){
     _serviceChargeError =errorMsg;
     notifyListeners();
   }
   /// set tip value
   setTipValue(num value){
     if (value > 100) {
       _tipValue = 0;
       setTipError(AddTipViewString.tipNotGreaterThan100Percent);
     }else {
       _tipValue = value;
       setTipError("");
     }
     notifyListeners();
   }

   /// set tip type
   void setTipType(String type, Alignment knobAlignment) {

     _tipType = type;
     _knobAlignmentTip =knobAlignment;
     _tipValue = 0;
     notifyListeners();
   }

   /// set tip error
   setTipError(String errorMsg){
     _tipError =errorMsg;
     notifyListeners();
   }

   /// check if email already exist
   String checkIfHasEmail() {
     if(0 == _cartData!.id!) return '';
     if(null == _cartData!.user) return '';
     if(_cartData!.user!.email!.isNotEmpty) {
       return _cartData!.user!.email!;
     }
     return '';
   }

   /// add service charge
   Future<void> addServiceCharge({required int cartId}) async {
     try {

       await _cartService.addServiceCharge(cartId: cartId,
           type: _serviceChargeType =='\$' ? ApiConstant.VALUE_TYPE_FLAT :
           ApiConstant.VALUE_TYPE_PERCENTAGE, value: _serviceChargeValue);

     } catch (error) {
       throw error;
     }
   }

   /// add tip
   Future<void> addTip({required int cartId}) async {
     try {

       await _cartService.addTip(cartId: cartId,
           type: _tipType =='\$' ? ApiConstant.VALUE_TYPE_FLAT :
           ApiConstant.VALUE_TYPE_PERCENTAGE, value: _tipValue);

     } catch (error) {
       throw error;
     }
   }

   /// remove service charge
   Future<void> removeServiceCharge( {required int serviceChargeId, }) async {
     try {
       if(serviceChargeId > 0) {
         await _cartService.removeServiceCharge(serviceChargeId:serviceChargeId);
       }
     } catch (error) {
       throw error;
     }
   }

   /// remove tip
   Future<void> removeTip({required int tipId, }) async {
     try {
       if(tipId > 0) {
         await _cartService.removeTip(tipId:tipId);
       }
     } catch (error) {
       throw error;
     }
   }

   ///remove discount  from cart
   Future<RemovePromotionsResponse> removeDiscount({required num id}) async {
     RemovePromotionsResponse _remoPromotionsResponse;
     try {
       _remoPromotionsResponse = await _cartService.removeDiscount(id: id,);
     } catch (error) {
       throw error;
     }
     return _remoPromotionsResponse;
   }

   ///Remove loyalty from order
   Future<RemoveLoyaltyResponse> removeLoyaltyFromOrder({required num loyaltyId}) async {
     RemoveLoyaltyResponse _removeLoyaltyResponse;
     try {
       _removeLoyaltyResponse = await _cartService.removeLoyaltyFromOrder(loyaltyId: loyaltyId);
     } catch (error) {
       throw error;
     }
     return _removeLoyaltyResponse;
   }

   bool showServiceChargesRemoveIcon() {
     bool flag = false;
     bool allPendingEmpty = _pendingCartItemMap.values.every((e) => e.isEmpty);
     if (allPendingEmpty){
       if(cartData!.cartServiceCharge != null){
         if(cartData!.paymentReceived! == false){
           flag = true;
         }
       }
     }
     return flag;
   }

   bool showTipsRemoveIcon() {
     bool flag = false;
     bool allPendingEmpty = _pendingCartItemMap.values.every((e) => e.isEmpty);
     if (allPendingEmpty){
       if(cartData!.cartTip != null){
         if(cartData!.paymentReceived! == false){
           flag = true;
         }
       }
     }
     return flag;
   }

   /// Check if cart discount exists
   bool checkCartDiscountExists(int discountId) {
     if (cartData!.cartDiscount  != null && cartData!.cartDiscount !.id == discountId) {
       return cartData!.cartDiscount !.approved ?? false;
     }
     return false;
   }

   resetAddServiceChargeDialog(){
     _serviceChargeType ='\%';
     _serviceChargeError ='';
     _serviceChargeValue =0;
     notifyListeners();
   }

   resetAddTipDialog(){
     _tipType ='\%';
     _tipError ='';
     _knobAlignmentTip = Alignment.centerLeft;
     _tipValue =0;
     notifyListeners();
   }

   resetPromoDialog(){
     notifyListeners();
   }

   reset() {
    _cartData = null;
    _cartUserList =[];
    _selectedCartUser =0;
    _guestUserName ='';
    _pendingCartItemMap ={};
    _submittedCartItemMap ={};
    _actionVoidItem = false;
    _itemsAvailableForVoid = {};
    _subtotal = 0;
    _cartDiscount = null;
    _cartCustomDiscount = null;
    _cartReward = null;
    _subtotalAfterPromotions = 0;
    _customer = null;
    _customerName = '';
    _customerStreamController.sink.add(_customerName);
    notifyListeners();
  }
}