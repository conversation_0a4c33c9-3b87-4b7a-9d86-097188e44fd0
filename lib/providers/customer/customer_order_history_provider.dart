

import 'package:flutter/foundation.dart';

import '../../models/cart/cart_data.dart';
import '../../models/customer/customer_order/customer_frequent_orders.dart';
import '../../models/customer/customer_order/customer_past_order.dart';
import '../../models/menu/menu_data.dart';
import '../../services/graph_ql/graph_ql_service.dart';
import '../../services/orders/order_service.dart';

class CustomerOrderHistoryProvider with ChangeNotifier{

  final OrderService _orderService = OrderService();

  String _selectedCustomerId='';
  String get selectedCustomerId => _selectedCustomerId;

  List<CustomerFrequentOrderData> _customerFrequentOrders =[];
  List<CustomerFrequentOrderData> get customerFrequentOrders => _customerFrequentOrders;

  List<CartData> _pastOrders =[];
  List<CartData> get pastOrders => _pastOrders;

  bool _sortOptionDateSelected = true;
  bool get sortOptionDateSelected => _sortOptionDateSelected;

  bool _sortOptionAmountSelected = false;
  bool get sortOptionAmountSelected => _sortOptionAmountSelected;

  bool _sortOptionNoOfItemsSelected = false;
  bool get sortOptionNoOfItemsSelected => _sortOptionNoOfItemsSelected;

  bool _sortDesc = true;
  bool get sortDesc => _sortDesc;

  bool _sortOptionLocationSelected = false;
  bool get sortOptionLocationSelected => _sortOptionLocationSelected;

  bool _sortOptionOrderIdSelected = false;
  bool get sortOptionOrderIdSelected => _sortOptionOrderIdSelected;

  num _totalPastOrders = 0;
  num get totalPastOrders => _totalPastOrders;

  setCustomerId(String userId) {
    this._selectedCustomerId = userId;
    notifyListeners();
  }


  /// order collapsed from expanded
  setOrderCollapsed(num orderId){
    if(this._pastOrders.isNotEmpty){
      this._pastOrders.where((o) => o.id == orderId).map((e) => e.isExpanded = false).toList();
      notifyListeners();
    }

  }

  /// expand order view
  setOrderExpanded(num orderId){
    if(this._pastOrders.isNotEmpty){
      this._pastOrders.where((o) => o.id == orderId).map((e) => e.isExpanded = true).toList();
      this._pastOrders.where((o) => o.id != orderId).map((e) => e.isExpanded = false).toList();
      notifyListeners();
    }

  }

  setDateOptionUnSelected() {
    this._sortOptionDateSelected = false;
    this._sortDesc = false;
    getCustomerPastOrders();
    notifyListeners();
  }

  setDateSortOptionSelected() {
    this._sortOptionDateSelected = true;
    this._sortOptionLocationSelected = false;
    this._sortOptionNoOfItemsSelected = false;
    this._sortOptionAmountSelected = false;
    this._sortOptionOrderIdSelected =false;
    this._sortDesc = true;
    getCustomerPastOrders();
    notifyListeners();
  }

  setOrderIdOptionUnSelected() {
    this._sortOptionOrderIdSelected = false;
    this._sortDesc = false;
    getCustomerPastOrders();
    notifyListeners();
  }

  setOrderIdSortOptionSelected() {
    this._sortOptionOrderIdSelected =true;
    this._sortOptionAmountSelected = false;
    this._sortOptionNoOfItemsSelected = false;
    this._sortOptionLocationSelected = false;
    this._sortOptionDateSelected = false;
    this._sortDesc = true;
    getCustomerPastOrders();
    notifyListeners();
  }

  setLocationOptionUnSelected() {
    this._sortOptionLocationSelected = false;
    this._sortDesc = false;
    getCustomerPastOrders();
    notifyListeners();
  }


  setLocationSortOptionSelected() {
    this._sortOptionLocationSelected = true;
    this._sortOptionDateSelected = false;
    this._sortOptionNoOfItemsSelected = false;
    this._sortOptionAmountSelected = false;
    this._sortOptionOrderIdSelected =false;
    this._sortDesc = true;
    getCustomerPastOrders();
    notifyListeners();
  }

  setNoOfItemsOptionUnSelected() {
    this._sortOptionNoOfItemsSelected = false;
    this._sortDesc = false;
    getCustomerPastOrders();
    notifyListeners();
  }

  setNoOfItemsSortOptionSelected() {
    this._sortOptionNoOfItemsSelected = true;
    this._sortOptionLocationSelected = false;
    this._sortOptionDateSelected = false;
    this._sortOptionAmountSelected = false;
    this._sortOptionOrderIdSelected =false;
    this._sortDesc = true;
    getCustomerPastOrders();
    notifyListeners();
  }

  setAmountOptionUnSelected() {
    this._sortOptionAmountSelected = false;
    this._sortDesc = false;
    getCustomerPastOrders();
    notifyListeners();
  }

  setAmountSortOptionSelected() {
    this._sortOptionAmountSelected = true;
    this._sortOptionNoOfItemsSelected = false;
    this._sortOptionLocationSelected = false;
    this._sortOptionDateSelected = false;
    this._sortOptionOrderIdSelected =false;
    this._sortDesc = true;
    getCustomerPastOrders();
    notifyListeners();
  }


  clearPastOrderData(CustomerPastOrder? _customerOrderRes){
    this._pastOrders.clear();
    _customerOrderRes = null;
    notifyListeners();
  }


  /// Customer frequent orders
  Future<CustomerFrequentOrders?> getCustomerFrequentOrders() async{
    CustomerFrequentOrders? _frequentOrders;
    try{
      _frequentOrders = await _orderService.getCustomerFrequentOrders(userId: _selectedCustomerId);
      this._customerFrequentOrders.clear();
      if(_frequentOrders.payload != null) {
        this._customerFrequentOrders.addAll(_frequentOrders.payload!);

        if(this._customerFrequentOrders.isNotEmpty) {
          List<int> itemIds = this._customerFrequentOrders.where((e) => e.itemId != null).map((e) => e.itemId!).toList();

          List<Items> itemList = await fetchItemDetails(itemId: itemIds);
          itemList.forEach((item) {
            this._customerFrequentOrders.where((e) => e.itemId == item.itemId).map((e) => e.itemData = item).toList();
          });
          /// item data has null, then that item remove from frequent order.
          this._customerFrequentOrders.removeWhere((e)=>e.itemData == null);
        }
      }
      notifyListeners();
    }catch(error){
      _frequentOrders = null;
      this._customerFrequentOrders.clear();
      notifyListeners();
    }
    return _frequentOrders;
  }

  /// fetch item details
  Future<List<Items>> fetchItemDetails({List<int>? itemId}) async {
    try {
      return await GraphQLService().fetchSearchItem(itemId: itemId);
    } catch (error) {
      throw error;
    }
  }

  Future<CustomerPastOrder?> getCustomerPastOrders() async {
    CustomerPastOrder? _customerOrderRes;
    try{
      if(this._selectedCustomerId.isNotEmpty) {
        _customerOrderRes = await _orderService.getCustomerPastOrders(userId: _selectedCustomerId,
            ascOrder: !this._sortDesc, sortOnDate: this._sortOptionDateSelected,
            sortOnAmount: this._sortOptionAmountSelected ,
            sortOnItems: this._sortOptionNoOfItemsSelected,
            sortOnLocation:  this._sortOptionLocationSelected, sortOnId: this._sortOptionOrderIdSelected);

        if(_customerOrderRes.payload !=null) {
          this._totalPastOrders = _customerOrderRes.payload!.length;
          this._pastOrders.clear();
          this._pastOrders.addAll(_customerOrderRes.payload!);

          this._pastOrders.forEach((order) async {
            List<int> itemIds = order.cartItems!.where((e) => e.itemId != 0).map((e) => e.itemId!).toList();
            List<Items> itemList = await fetchItemDetails(itemId: itemIds);
            itemList.forEach((item) async{
              order.cartItems!.where((ci) => ci.itemId! == item.itemId!).map((e) {
                e.category = item.category?.categoryName ??'';
                e.actualPrice = item.itemPriceInventory?.price ??0;
              }
              ).toList();
            });
          });
        }
        notifyListeners();
      }else {
        clearPastOrderData(_customerOrderRes);
      }
    }catch(error){
      debugPrint('error==> $error');
      clearPastOrderData(_customerOrderRes);
    }
    return _customerOrderRes;
  }

  reset() {
    this._customerFrequentOrders = [];
    this._pastOrders =[];
    this._sortOptionDateSelected = false;
    this._sortOptionAmountSelected = false;
    this._sortOptionNoOfItemsSelected = false;
    this._sortOptionLocationSelected = false;
    this._sortOptionOrderIdSelected = false;
  }

}