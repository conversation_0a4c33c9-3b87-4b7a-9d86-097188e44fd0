import 'package:flutter/cupertino.dart';
import '../../models/auth/user_model.dart';
import '../../models/auth/user_register_model.dart';
import '../../models/business/user_business_model.dart';
import '../../models/location/location_model.dart';
import '../../services/auth/auth_service.dart';
import '../../utils/pref_utils.dart';

class AuthProvider extends ChangeNotifier{
  final AuthService _auth = AuthService();

  bool _showPassword = false;
  bool get showPassword => _showPassword;

  String _loggedInUserName = '';
  String get loggedInUserName => _loggedInUserName;

  String _email = '';
  String get email => _email;

  String _password = '';
  String get password => _password;

  String _forgotEmail = '';
  String get forgotEmail => _forgotEmail;

  /// Business
  UserBusinessModel? _selectedBusiness;
  UserBusinessModel? get selectedBusiness=> _selectedBusiness;

  List<UserBusinessModel> _userBusinesses = [];
  List<UserBusinessModel> get userBusinesses => _userBusinesses;

  ///Locations
  LocationModel? _selectedLocation;
  LocationModel? get selectedLocation => _selectedLocation;

  List<LocationModel> _businessLocations = [];
  List<LocationModel> get businessLocations => _businessLocations;


  setPasswordShowHide(){
    _showPassword = !showPassword;
    notifyListeners();
  }

  void setAuthToken(String authToken) async {
    await PrefsUtils.setString(PrefKeys.authToken, authToken);
  }

  void setLocationUserData(UserModel user) async {
    await PrefsUtils.setJson(PrefKeys.user, user.toJson());
    notifyListeners();
  }

  /// login api
  Future<UserModel> loginWithEmail() async {
    UserModel user;

    try {
      user = await _auth.loginWithEmail(_email, _password);
    } catch (error) {
      throw error;
    }

    if (user.token == null) {
      throw Exception('invalid login details');
    }

    setAuthToken(user.token ?? '');
    setLocationUserData(user);
    return user;
  }

  /// get user profile api
  Future<void> getProfileResponse() async {
    UserData user;
    try {
      user = await _auth.getProfileResponse();
      setUserData(user);
    } catch (error) {
      throw error;
    }
  }

  Future<void> setUserData(UserData user) async {
    await PrefsUtils.setJson(PrefKeys.user, user.toJson());
  }

  /// user forgot password api
  Future<void> forgotPassword() async {
    try {
      await _auth.forgotPassword(_forgotEmail);
    } catch (err) {
      throw err;
    }
  }

  /// user refresh token api
  Future<RefreshTokenModel> refreshToken() async {
    RefreshTokenModel refreshToken;

    try {

      String businessId='', locationId='';
      if(_selectedBusiness != null) {
        businessId = _selectedBusiness?.id ??'';
      }
      if(_selectedLocation != null) {
        locationId = _selectedLocation?.id ??'';
      }

      refreshToken = await _auth.refreshToken(businessId, locationId);

      if(_selectedBusiness != null){
        setSelectedBusiness(_selectedBusiness!);
        saveBusiness(_selectedBusiness!);
      }
      if(_selectedLocation != null){
        setSelectedLocation(_selectedLocation!);
        saveLocation(_selectedLocation!);
      }
    } catch (err) {
      throw err;
    }

    if (refreshToken.newToken == null) {
      throw Exception('invalid business id or location id');
    }

    setAuthToken(refreshToken.newToken ?? '');
    return refreshToken;
  }

  setEmail(String email){
    _email = email;
    notifyListeners();
  }

  setPassword(String password){
    _password = password;
    notifyListeners();
  }

  setForgotEmail(String forgotEmail){
    _forgotEmail = forgotEmail;
    notifyListeners();
  }

  /// user business list api
  Future<List<UserBusinessModel>> getUserBusinesses() async {
    List<UserBusinessModel> userBusinessesList = [];

    try {
      userBusinessesList = await _auth.getUserBusiness();
    } catch (error) {
      debugPrint(error.toString());
      debugPrint('error getting businesses');
      rethrow;
    }

    updateUserBusinesses(userBusinessesList);

    return userBusinessesList;
  }

  void updateUserBusinesses(
      List<UserBusinessModel> updatedUserBusinesses) async {
    _userBusinesses = updatedUserBusinesses;
    notifyListeners();
  }

  void setSelectedBusiness(UserBusinessModel selectedBusiness) {
    _selectedBusiness = selectedBusiness;
    notifyListeners();
  }

  Future<void> saveBusiness(UserBusinessModel selectedBusiness) async {
    await PrefsUtils.setJson(PrefKeys.business, selectedBusiness.toJson());
  }

  /// Locations list api
  Future<List<LocationModel>> getBusinessLocation() async {
    List<LocationModel> businessLocationList = [];

    try {
      businessLocationList = await _auth.getBusinessLocations();
    } catch (err) {
      rethrow;
    }

    updatedBusinessLocations(businessLocationList);

    return businessLocationList;
  }

  void updatedBusinessLocations(List<LocationModel> businessLocationList) async {
    _businessLocations = businessLocationList;
    notifyListeners();
  }

  void setSelectedLocation(LocationModel selectedLocation) {
    _selectedLocation = selectedLocation;
    notifyListeners();
  }

  Future<void> saveLocation(LocationModel selectedLocation) async {
    await PrefsUtils.setJson(PrefKeys.location, selectedLocation.toJson());
  }

  /// Check business is not empty
  bool isBusinessSelected() {
    UserBusinessModel userBusiness;
    userBusiness = UserBusinessModel.fromJson(PrefsUtils.getObject(PrefKeys.business));

    if(userBusiness.id != null && userBusiness.id!.isNotEmpty) {
      setSelectedBusiness(userBusiness);
      return true;
    }
    return false;
  }

  /// Check location is not empty
  bool isLocationSelected() {
    LocationModel userLocation;
    userLocation =
        LocationModel.fromJson(PrefsUtils.getObject(PrefKeys.location));

    if(userLocation.id != null && userLocation.id!.isNotEmpty) {
      setSelectedLocation(userLocation);
      return true;
    }
    return false;
  }

  Future<void> logoutUser() async{
    _userBusinesses = [];
    _selectedBusiness = null;
    _businessLocations = [];
    _selectedLocation = null;
    await PrefsUtils.clear();
    notifyListeners();
  }

  reset(){
    _showPassword = false;
    _loggedInUserName = '';
    notifyListeners();
  }
}