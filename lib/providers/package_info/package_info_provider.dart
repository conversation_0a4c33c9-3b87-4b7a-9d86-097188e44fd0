import 'package:flutter/material.dart';

class PackageInfoProvider with ChangeNotifier {

  String? _versionName;
  String? get versionName => _versionName;

  String? _versionCode;
  String? get versionCode => _versionCode;

  setVersionNameAndCode(String versionName, String versionCode){
    _versionName = versionName;
    _versionCode = versionCode;
    notifyListeners();
  }

  reset(){
    _versionName = '';
    _versionCode = '';
    notifyListeners();
  }

}
