import 'dart:async';

import 'package:flutter/material.dart';

class PubNubProvider with ChangeNotifier {

  /// default is true as user first land to main screen
  bool _onMainScreen = true;
  bool get onMainScreen => _onMainScreen;


  //
  StreamController<Map<String, dynamic>> dataStreamController = StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get dataStream => dataStreamController.stream.asBroadcastStream();

  StreamController<Map<String, dynamic>> cartStreamController = StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get cartStream => cartStreamController.stream.asBroadcastStream();


  /// set user stand on main screen
  /// or not
  setOnMainScreen(bool value) {
    _onMainScreen = value;
    debugPrint('user comes on main screen :: $_onMainScreen');
    notifyListeners();
  }

  void reOpenStream(){
    debugPrint(':: re-open stream ::');
    if(dataStreamController.isClosed) {
      debugPrint(':: already closed data stream ::');
      dataStreamController = StreamController<Map<String, dynamic>>.broadcast();
    }
    if(cartStreamController.isClosed) {
      debugPrint(':: already closed cart stream ::');
      cartStreamController = StreamController<Map<String, dynamic>>.broadcast();
    }
  }
  /// reset data
  reset(){
    _onMainScreen = true;
    dataStreamController.close();
    cartStreamController.close();
    notifyListeners();
   }

  @override
  void dispose() {
    super.dispose();
  }
}