plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services" version "4.3.8"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.shopley.gr8tables"
    compileSdk flutter.compileSdkVersion
//    ndkVersion flutter.ndkVersion
    ndkVersion "27.2.12479018"

    compileOptions {
//        sourceCompatibility JavaVersion.VERSION_1_8
//        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
//        jvmTarget = '1.8'
        jvmTarget = 17
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.shopley.gr8tables"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 21 //flutter.minSdkVersion
        targetSdkVersion 34//flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    def keystoreProperties = new Properties()
    def keystorePropertiesFile = rootProject.file('key.properties')
    if (keystorePropertiesFile.exists()) {
        keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
    }

    signingConfigs {
        debug {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }


    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled true
            shrinkResources true
            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "version"
    productFlavors {
        dev {
            versionCode 7
            versionName "1.0.6"
            dimension "version"
            resValue "string", 'app_name', '"Amora Dev"'
            applicationIdSuffix ".dev"
        }

        beta {
            versionCode 1
            versionName "1.0.0"
            dimension "version"
            resValue "string", 'app_name', '"Amora Beta"'
            applicationIdSuffix ".beta"
        }

        uat {
            versionCode 44
            versionName "1.4.3"
            dimension "version"
            resValue "string", 'app_name', '"Amora UAT"'
            applicationIdSuffix ".uat"
        }

        live {
            versionCode 6
            versionName "1.0.6"
            dimension "version"
            resValue "string", 'app_name', '"Amora Live"'
            applicationIdSuffix ".live"
        }

        production {
            versionCode 8
            versionName "1.0.5"
            dimension "version"
            resValue "string", 'app_name', '"Amora"'
            applicationId "server.amorapos.com"
        }
    }
}
flutter {
    source '../..'
}

dependencies {
    implementation 'com.google.firebase:firebase-messaging:24.0.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'
}
