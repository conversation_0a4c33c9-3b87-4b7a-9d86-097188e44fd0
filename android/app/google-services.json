{"project_info": {"project_number": "266206055959", "project_id": "amora-pos", "storage_bucket": "amora-pos.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:266206055959:android:de3291da8f387d5243d652", "android_client_info": {"package_name": "com.shopley.gr8tables.beta"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBaoscym-sXXCxFTVeMaFCHTqUJ9O4KZjw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:266206055959:android:05970a2a20933a8143d652", "android_client_info": {"package_name": "com.shopley.gr8tables.dev"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBaoscym-sXXCxFTVeMaFCHTqUJ9O4KZjw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:266206055959:android:fcc2086a9e43f12043d652", "android_client_info": {"package_name": "com.shopley.gr8tables.live"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBaoscym-sXXCxFTVeMaFCHTqUJ9O4KZjw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:266206055959:android:67ac838fea57821843d652", "android_client_info": {"package_name": "com.shopley.gr8tables.uat"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBaoscym-sXXCxFTVeMaFCHTqUJ9O4KZjw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:266206055959:android:f4529e084e2ac16c43d652", "android_client_info": {"package_name": "server.amorapos.com"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBaoscym-sXXCxFTVeMaFCHTqUJ9O4KZjw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}